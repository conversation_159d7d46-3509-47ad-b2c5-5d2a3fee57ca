{"version": 3, "file": "case_completed.js", "sources": ["pages/case_completed/case_completed.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FzZV9jb21wbGV0ZWQvY2FzZV9jb21wbGV0ZWQudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"work-order-detail-container\">\r\n    <!-- 案件基本信息 -->\r\n    <view class=\"work-order-card\">\r\n      <view class=\"work-order-header\">\r\n        <view class=\"work-order-title\">\r\n          <text>调解案件号: </text>\r\n          <text class=\"work-order-id\">{{\r\n            basicInfo.case_number\r\n          }}</text>\r\n        </view>\r\n        <view class=\"work-order-status\">\r\n          <text\r\n            class=\"status-label\"\r\n            :class=\"{ 'status-pending': basicInfo.case_status_cn === '已完成' }\"\r\n            >{{ basicInfo.case_status_cn }}</text\r\n          >\r\n        </view>\r\n      </view>\r\n      <view class=\"work-order-date\"\r\n        >发起日期: {{ basicInfo.initiate_date }}</view\r\n      >\r\n    </view>\r\n\r\n    <!-- 进度条 -->\r\n    <view class=\"progress-bar\">\r\n      <view class=\"progress-steps\">\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">1</view>\r\n          <view class=\"step-line active\"></view>\r\n          <view class=\"step-label\">调解确认</view>\r\n        </view>\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">2</view>\r\n          <view class=\"step-line active\"></view>\r\n          <view class=\"step-label\">方案确认</view>\r\n        </view>\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">3</view>\r\n          <view class=\"step-line active\"></view>\r\n          <view class=\"step-label\">协议签署</view>\r\n        </view>\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">4</view>\r\n          <view class=\"step-label\">完成</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 协议公证状态 -->\r\n    <view class=\"info-section\">\r\n      <view class=\"section-title\">协议公证状态</view>\r\n      <view\r\n        class=\"notarization-status-card notarization-pending\"\r\n        v-if=\"workOrderData.notarization_status_cn === '未公证'\"\r\n        @click=\"handleNotarization\"\r\n      >\r\n        <view class=\"notarization-status-header\">\r\n          <view class=\"notarization-status-icon\">\r\n            <i class=\"fas fa-certificate\"></i>\r\n          </view>\r\n          <view class=\"notarization-status-info\">\r\n            <view class=\"notarization-status-title\">{{workOrderData.notarization_status_cn}}</view>\r\n            <view class=\"notarization-status-desc\">协议尚未进行公证，点击进行公证以获得法律强制执行力</view>\r\n          </view>\r\n          <view class=\"notarization-action-arrow\">\r\n            <i class=\"fas fa-chevron-right\"></i>\r\n          </view>\r\n        </view>\r\n        <view class=\"notarization-benefits\">\r\n          <view class=\"notarization-benefit-item\">\r\n            <i class=\"fas fa-shield-alt\"></i>\r\n            <text>法律强制执行力</text>\r\n          </view>\r\n          <view class=\"notarization-benefit-item\">\r\n            <i class=\"fas fa-gavel\"></i>\r\n            <text>免费办理</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view\r\n        class=\"notarization-status-card notarization-completed notarization-status-card-completed\"\r\n        id=\"notarizationCompletedCard\"\r\n        v-if=\"workOrderData.notarization_status_cn === '已公证'\"\r\n      >\r\n        <view class=\"notarization-status-header\">\r\n          <view class=\"notarization-status-icon\">\r\n            <i class=\"fas fa-certificate\"></i>\r\n          </view>\r\n          <view class=\"notarization-status-info\">\r\n            <view class=\"notarization-status-title\">已公证</view>\r\n            <view class=\"notarization-status-desc\"\r\n              >协议已完成公证，具备法律强制执行力</view\r\n            >\r\n          </view>\r\n          <view class=\"notarization-status-badge\">\r\n            <i class=\"fas fa-check-circle\"></i>\r\n          </view>\r\n        </view>\r\n        <view class=\"notarization-details\">\r\n          <view class=\"notarization-detail-row\">\r\n            <text class=\"notarization-detail-label\">公证日期：</text>\r\n            <text class=\"notarization-detail-value\">2023-10-15</text>\r\n          </view>\r\n          <view class=\"notarization-detail-row\">\r\n            <text class=\"notarization-detail-label\">公证机构：</text>\r\n            <text class=\"notarization-detail-value\">广东省佛山市公证处</text>\r\n          </view>\r\n          <view class=\"notarization-detail-row\">\r\n            <text class=\"notarization-detail-label\">公证书编号：</text>\r\n            <text class=\"notarization-detail-value\">(2023)粤佛证字第12345号</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 调解信息 -->\r\n    <view class=\"info-section\">\r\n      <view class=\"section-title\">调解信息</view>\r\n      <view class=\"info-item\" v-for=\"info in workOrderData.mediation_info\" :key=\"info.id\">\r\n        <text class=\"info-label\">{{ info.title }}</text>\r\n        <text class=\"info-value\">{{ info.value }}</text>\r\n      </view>\r\n    </view>\r\n    <!-- 还款方案 -->\r\n    <view class=\"info-section\">\r\n      <view class=\"section-title\">还款方案</view>\r\n      <view class=\"info-item\" v-for=\"plan in workOrderData.mediation_plan\" :key=\"plan.id\">\r\n        <text class=\"info-label\">{{ plan.title }}</text>\r\n        <text class=\"info-value\" :class=\"plan.title == '减免金额' ? 'reduction_amount' : ''\">{{ plan.value }}</text>\r\n      </view>\r\n    </view>\r\n\t<!-- 还款渠道 -->\r\n\t<view class=\"info-section\">\r\n\t\t<view class=\"section-title\">还款渠道</view>\r\n\t\t<view class=\"payment-memo-card\">\r\n\t\t\t<view class=\"payment-memo-header\">\r\n\t\t\t\t<i class=\"fas fa-exclamation-triangle payment-memo-icon\"></i>\r\n\t\t\t\t<view class=\"payment-memo-title\">重要提醒：还款备注信息</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment-memo-content\">{{workOrderData.repayment_note}}</view>\r\n\t\t\t<view class=\"payment-memo-actions\">\r\n\t\t\t\t<button class=\"btn btn-sm btn-copy btn-orange\" @click=\"copyMemo\" v-if=\"!isCopied\">\r\n\t\t\t\t\t<i class=\"fas fa-copy\"></i>一键复制备注\r\n\t\t\t\t</button>\r\n\t\t\t\t<button class=\"btn btn-sm btn-copy btn-orange copied\" v-else>\r\n\t\t\t\t\t<i class=\"fas fa-check\"></i>已复制\r\n\t\t\t\t</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment-memo-tip\">\r\n\t\t\t\t<i class=\"fas fa-info-circle\"></i>\r\n\t\t\t\t还款时请务必填写上述备注信息，以便系统自动识别您的还款记录\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 银行转账渠道 -->\r\n\t\t<view class=\"payment-channel-card\">\r\n\t\t\t<view class=\"payment-channel-header\">\r\n\t\t\t\t<view class=\"payment-channel-icon\" style=\"background-color: var(--primary-color);\">\r\n\t\t\t\t\t<i class=\"fas fa-university\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-channel-info\">\r\n\t\t\t\t\t<view class=\"payment-channel-name\">银行转账</view>\r\n\t\t\t\t\t<view class=\"payment-channel-desc\">通过银行转账进行还款</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment-channel-details\">\r\n\t\t\t\t<view class=\"payment-detail-row\">\r\n\t\t\t\t\t<view class=\"payment-detail-label\">收款人姓名</view>\r\n\t\t\t\t\t<view class=\"payment-detail-value\">华泰民商事调解中心</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-detail-row\">\r\n\t\t\t\t\t<view class=\"payment-detail-label\">收款账号</view>\r\n\t\t\t\t\t<view class=\"payment-detail-value\">6225 8812 3456 7890</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-detail-row\">\r\n\t\t\t\t\t<view class=\"payment-detail-label\">开户银行</view>\r\n\t\t\t\t\t<view class=\"payment-detail-value\">中国银行佛山分行</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 微信支付渠道 -->\r\n\t\t<view class=\"payment-channel-card\">\r\n\t\t\t<view class=\"payment-channel-header\">\r\n\t\t\t\t<view class=\"payment-channel-icon\" style=\"background-color: #09bb07;\">\r\n\t\t\t\t\t<i class=\"fab fa-weixin\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-channel-info\">\r\n\t\t\t\t\t<view class=\"payment-channel-name\">微信支付</view>\r\n\t\t\t\t\t<view class=\"payment-channel-desc\">扫码使用微信支付进行还款</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment-channel-qrcode-container\">\r\n\t\t\t\t<view class=\"payment-channel-qrcode\">\r\n\t\t\t\t\t收款二维码\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 支付宝支付渠道 -->\r\n\t\t<view class=\"payment-channel-card\">\r\n\t\t\t<view class=\"payment-channel-header\">\r\n\t\t\t\t<view class=\"payment-channel-icon\" style=\"background-color: #00a0e9;\">\r\n\t\t\t\t\t<i class=\"fab fa-alipay\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"payment-channel-info\">\r\n\t\t\t\t\t<view class=\"payment-channel-name\">支付宝</view>\r\n\t\t\t\t\t<view class=\"payment-channel-desc\">扫码使用支付宝进行还款</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"payment-channel-qrcode-container\">\r\n\t\t\t\t<view class=\"payment-channel-qrcode\">\r\n\t\t\t\t\t收款二维码\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n\r\n    <!-- 底部操作按钮 -->\r\n    <view class=\"action-buttons\">\r\n      <button class=\"confirm-button\" @click=\"handleViewProtocol\"><i class=\"fas fa-file-contract\"></i>查看协议</button>\r\n      <button class=\"confirm-button svg-btn\" @click=\"handleViewSvgFile\"><i class=\"fas fa-image\"></i>查看SVG文件</button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from \"vue\";\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { api } from \"@/utils/api.js\";\r\n\r\n// 接收页面参数 - 调解案件编号\r\nconst caseNumber = ref('');\r\n\r\n// 接收页面参数\r\nconst basicInfo = ref({       // 基本信息\r\n  case_number: '',\r\n  initiate_date: '',\r\n  case_status_cn: '',\r\n  mediation_progress: ''\r\n});\r\n// 工单数据\r\nconst workOrderData = ref({});\r\n\r\nconst isCopied = ref(false);\r\n\r\n// 页面加载时处理URL参数\r\nonLoad((options) => {\r\n  console.log(\"方案确认页面加载，参数:\", options);\r\n\r\n  // 处理URL参数\r\n  if (options && options.case_number) {\r\n    handleUrlParams(options);\r\n  }\r\n});\r\n\r\n\r\n// 处理URL参数\r\nconst handleUrlParams = async (options) => {\r\n  console.log(\"处理URL参数:\", options);\r\n\r\n  const { case_number, initiate_date, case_status_cn, mediation_progress } = options;\r\n\r\n  // 检查是否有完整参数\r\n  const hasCompleteParams = case_number && initiate_date && case_status_cn && mediation_progress;\r\n\r\n  try {\r\n    uni.showLoading({ title: \"加载中...\" });\r\n\r\n    if (hasCompleteParams) {\r\n      // 有完整参数，直接显示基本信息\r\n      console.log(\"检测到完整参数，直接显示基本信息\");\r\n      basicInfo.value.case_number = decodeURIComponent(case_number);\r\n      basicInfo.value.initiate_date = decodeURIComponent(initiate_date);\r\n      basicInfo.value.case_status_cn = decodeURIComponent(case_status_cn);\r\n      basicInfo.value.mediation_progress = decodeURIComponent(mediation_progress);\r\n\r\n      // 设置caseNumber用于后续API调用\r\n      caseNumber.value = basicInfo.value.case_number;\r\n      // 获取详细数据\r\n      await fetchWorkOrderDetail(basicInfo.value.case_number);\r\n    }else if (case_number) {\r\n      // 仅有案件编号，调用接口获取详细信息\r\n      console.log(\"仅有案件编号，调用接口获取详细信息\");\r\n      const decodedCaseNumber = decodeURIComponent(case_number);\r\n      caseNumber.value = decodedCaseNumber;\r\n\r\n      // 先获取案件基本信息\r\n      await fetchMediationSingleDetailWithoutLoading(decodedCaseNumber);\r\n      \r\n      // 获取详细数据\r\n      await fetchWorkOrderDetail(decodedCaseNumber);\r\n    }\r\n  }catch (error) {\r\n    console.error(\"处理URL参数失败:\", error);\r\n    uni.showToast({\r\n      title: \"页面加载失败\",\r\n      icon: \"none\",\r\n      duration: 2000,\r\n    });\r\n  } finally {\r\n    uni.hideLoading();\r\n  }\r\n};\r\n\r\n\r\n// 不带加载提示的版本 - 用于并行调用\r\nconst fetchMediationSingleDetailWithoutLoading = async (caseNumber) => {\r\n  try {\r\n    const result = await api.mediationQuery.getSingleDetail(caseNumber);\r\n\r\n    if (result.state === \"success\" && result.data) {\r\n      const data = result.data;\r\n      basicInfo.value.case_number = data.case_number || caseNumber;\r\n      basicInfo.value.initiate_date = data.initiate_date || '';\r\n      basicInfo.value.case_status_cn = data.case_status_cn || '';\r\n      basicInfo.value.mediation_progress = data.mediation_progress || '';\r\n    } else {\r\n      throw new Error(result.msg || \"获取案件信息失败\");\r\n    }\r\n  } catch (error) {\r\n    console.error(\"获取案件详情失败:\", error);\r\n    throw error;\r\n  }\r\n};\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取页面参数\r\n  /* const pages = getCurrentPages();\r\n  const currentPage = pages[pages.length - 1];\r\n  const options = currentPage.$page?.options;\r\n\r\n  if (options && options.case_number) {\r\n    try {\r\n      // URL解码处理中文参数\r\n      caseNumber.value = decodeURIComponent(options.case_number);\r\n      console.log(\"接收到工单ID:\", caseNumber.value);\r\n      fetchWorkOrderDetail(caseNumber.value);\r\n    } catch (error) {\r\n      console.error('case_number参数解码失败:', error);\r\n      caseNumber.value = options.case_number;\r\n      fetchWorkOrderDetail(caseNumber.value);\r\n    }\r\n  } else if (options && options.id) {\r\n    try {\r\n      // URL解码处理中文参数（兼容旧的id参数）\r\n      caseNumber.value = decodeURIComponent(options.id);\r\n      console.log(\"接收到工单ID:\", caseNumber.value);\r\n      fetchWorkOrderDetail(caseNumber.value);\r\n    } catch (error) {\r\n      console.error('id参数解码失败:', error);\r\n      caseNumber.value = options.id;\r\n      fetchWorkOrderDetail(caseNumber.value);\r\n    }\r\n  } else {\r\n    // 默认使用模拟数据\r\n    fetchWorkOrderDetail();\r\n  } */\r\n});\r\n\r\n// 获取调解确认数据\r\nconst fetchWorkOrderDetail = (id) => {\r\n  // 使用API获取数据\r\n  uni.showLoading({\r\n    title: \"加载中...\",\r\n  });\r\n\r\n  const result = api.mediationQuery.getCompletedDetail(id)\r\n  uni.hideLoading();\r\n  if (result.state == 'success') {\r\n    workOrderData.value = result.data;\r\n  } else {\r\n    uni.showToast({\r\n      title: result.msg || \"获取数据失败\",\r\n      icon: \"none\",\r\n    });\r\n  }\r\n};\r\n\r\n// 查看协议\r\nconst handleViewProtocol = () => {\r\n  // 构建文件预览参数\r\n  const fileParams = {\r\n    fileUrl: encodeURIComponent('http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx'),\r\n    fileType: encodeURIComponent('pdf'),\r\n    fileName: encodeURIComponent('调解协议.pdf'),\r\n    caseNumber: encodeURIComponent(caseNumber.value || '485')\r\n  };\r\n\r\n  // 构建URL参数字符串\r\n  const paramsString = Object.entries(fileParams)\r\n    .map(([key, value]) => `${key}=${value}`)\r\n    .join('&');\r\n\r\n  uni.navigateTo({\r\n    url: `/pages/protocol_preview/protocol_preview?${paramsString}`,\r\n  });\r\n};\r\n\r\n// 查看SVG文件示例\r\nconst handleViewSvgFile = () => {\r\n  // 构建SVG文件预览参数\r\n  const fileParams = {\r\n    fileUrl: encodeURIComponent('http://192.168.1.101:10010/file_download/svg/sample.svg'),\r\n    fileType: encodeURIComponent('svg'),\r\n    fileName: encodeURIComponent('示例图表.svg'),\r\n    caseNumber: encodeURIComponent(caseNumber.value || '485')\r\n  };\r\n\r\n  // 构建URL参数字符串\r\n  const paramsString = Object.entries(fileParams)\r\n    .map(([key, value]) => `${key}=${value}`)\r\n    .join('&');\r\n\r\n  uni.navigateTo({\r\n    url: `/pages/protocol_preview/protocol_preview?${paramsString}`,\r\n  });\r\n};\r\n\r\n// 公证\r\nconst handleNotarization = () => {\r\n\t// 跳转至协议公证页面\r\n\tuni.navigateTo({\r\n\t\turl: \"/pages/agreement_notarization/agreement_notarization\",\r\n\t});\r\n//   workOrderData.value.notarization_status_cn = \"已公证\";\r\n};\r\n\r\n// 复制还款备注\r\nconst copyMemo = () => {\r\n  uni.setClipboardData({\r\n    data: workOrderData.value.paymentMemo,\r\n  });\r\n  uni.showToast({\r\n    title: \"还款备注信息已复制\",\r\n    icon: \"success\",\r\n  });\r\n  isCopied.value = true;\r\n};\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:root {\r\n\t--text-color: #333;\r\n\t--text-secondary: #666;\r\n\t--success-color: #52c41a;\r\n\t--warning-color: #faad14;\r\n\t--primary-color: #3b7eeb;\r\n\t--border-color: #e0e0e0;\r\n\t--transition-normal: all 0.3s ease;\r\n}\r\n.work-order-detail-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  padding: 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30rpx;\r\n}\r\n\r\n.work-order-card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.work-order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n  font-size: 29rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n  padding: 6rpx 20rpx;\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n  font-size: 26rpx;\r\n  padding: 8rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background-color: #52c41a;\r\n  color: #fff;\r\n}\r\n\r\n.status-pending {\r\n  background-color: #52c41a;\r\n}\r\n\r\n.work-order-date {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.progress-steps {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  position: relative;\r\n}\r\n\r\n.progress-step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  flex: 1;\r\n  position: relative;\r\n}\r\n\r\n.step-circle {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.step-line {\r\n  position: absolute;\r\n  top: 30rpx;\r\n  left: 50%;\r\n  right: -50%;\r\n  height: 4rpx;\r\n  background-color: #e0e0e0;\r\n  z-index: 1;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n  display: none;\r\n}\r\n.step-line.active {\r\n  background-color: #2979ff;\r\n}\r\n.step-label {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n  background-color: #2979ff;\r\n}\r\n\r\n.progress-step.active .step-label {\r\n  color: #2979ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.info-section {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n}\r\n\r\n.info-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: flex-start;\r\n  gap: 16rpx;\r\n  padding: 20rpx 0;\r\n  color: #333;\r\n  font-size: 32rpx;\r\n}\r\n\r\n.info-item:last-child {\r\n  border-bottom: none;\r\n}\r\n.reduction_amount{\r\n  color: #52c41a;\r\n}\r\n.info-label {\r\n  font-weight: bold;\r\n}\r\n.notarization-pending {\r\n  background: linear-gradient(135deg, #fff7e6 0%, #fff2d6 100%);\r\n  border: 2rpx solid #ffd591;\r\n  cursor: pointer;\r\n}\r\n.notarization-pending:hover {\r\n  background: linear-gradient(135deg, #fff2d6 0%, #ffec99 100%);\r\n  border-color: #ffa940;\r\n  // transform: translateY(-2px);\r\n  box-shadow: 0 8rpx 24rpx rgba(255, 140, 0, 0.15);\r\n}\r\n.notarization-status-card {\r\n  border-radius: 24rpx;\r\n  padding: 32rpx;\r\n  margin-bottom: 24rpx;\r\n  transition: all 0.3s ease, opacity 0.3s ease, transform 0.3s ease;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n.notarization-status-card-completed{\r\n  display: block; \r\n  opacity: 1;\r\n  transform: translateY(0rpx);\r\n}\r\n.notarization-completed {\r\n  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);\r\n  border: 2rpx solid #b7eb8f;\r\n}\r\n.notarization-details {\r\n  margin-top: 24rpx;\r\n  padding-top: 24rpx;\r\n  border-top: 2rpx solid #d9f7be;\r\n}\r\n.notarization-detail-row {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-bottom: 12rpx;\r\n  font-size: 26rpx;\r\n  .notarization-detail-label {\r\n    color: var(--text-secondary);\r\n    font-weight: bold;\r\n  }\r\n  .notarization-detail-value {\r\n    color: var(--text-color);\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n.notarization-status-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  margin-bottom: 24rpx;\r\n  .fa-certificate{\r\n    color: #52c41a;\r\n    font-size: 40rpx;\r\n  }\r\n  .fa-check-circle{\r\n    color: #52c41a; \r\n    font-size: 32rpx;\r\n  }\r\n}\r\n.notarization-pending .notarization-status-icon {\r\n  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);\r\n}\r\n// .notarization-pending:hover .notarization-action-arrow {\r\n//   transform: translateX(3px);\r\n// }\r\n.notarization-status-icon {\r\n  width: 80rpx;\r\n  height: 80rpx;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-right: 24rpx;\r\n  flex-shrink: 0;\r\n  .fa-certificate{\r\n    color: #ff8c00;\r\n    font-size: 40rpx\r\n  }\r\n}\r\n.notarization-completed .notarization-status-icon {\r\n    background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);\r\n}\r\n.notarization-status-info {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n.notarization-status-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 8rpx;\r\n  color: var(--text-color);\r\n}\r\n.notarization-status-desc {\r\n  font-size: 26rpx;\r\n  color: var(--text-secondary);\r\n  line-height: 1.4;\r\n}\r\n.notarization-action-arrow {\r\n  margin-left: 16rpx;\r\n  transition: transform 0.3s ease;\r\n  .fa-chevron-right{\r\n    color: #ff8c00;\r\n    font-size: 32rpx;\r\n  }\r\n}\r\n.notarization-benefits {\r\n  display: flex;\r\n  gap: 40rpx;\r\n  margin-top: 16rpx;\r\n}\r\n.notarization-benefit-item {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24rpx;\r\n  color: var(--text-secondary);\r\n  .fa-shield-alt,.fa-gavel{\r\n    color: #ff8c00;\r\n    margin-right: 16rpx;\r\n  }\r\n}\r\n\r\n.payment-memo-card {\r\n    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);\r\n    border: 2rpx solid #ffcc02;\r\n    border-radius: 24rpx;\r\n    padding: 36rpx;\r\n    // margin-top: 40rpx;\r\n    margin-bottom: 40rpx;\r\n    margin-top: 0;\r\n}\r\n.payment-memo-header {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 24rpx;\r\n}\r\n.payment-memo-icon {\r\n    width: 48rpx;\r\n    height: 48rpx;\r\n    color: #ff8f00;\r\n\talign-items: center;\r\n    display: flex;\r\n    text-align: center;\r\n}\r\n.payment-memo-title {\r\n    font-size: 32rpx;\r\n    font-weight: 600;\r\n    color: #e65100;\r\n}\r\n.payment-memo-content {\r\n    background-color: white;\r\n    border-radius: 16rpx;\r\n    padding: 24rpx;\r\n    font-size: 28rpx;\r\n    color: var(--text-color);\r\n    font-weight: bold;\r\n    word-break: break-all;\r\n    line-height: 1.4;\r\n    border: 2rpx solid rgba(255, 152, 0, 0.2);\r\n}\r\n.payment-memo-actions {\r\n    margin-top: 24rpx;\r\n    text-align: right;\r\n}\r\n.payment-memo-tip{\r\n  display: flex;\r\n\tmargin-top: 20rpx;\r\n\tfont-size: 24rpx;\r\n\tcolor: #e65100;\r\n\tline-height: 1.4;\r\n  .fa-info-circle{\r\n    margin-right: 10rpx;\r\n  }\r\n}\r\n.btn-copy.btn-orange {\r\n    background-color: #ff8f00;\r\n    border-color: #ff8f00;\r\n    color: white;\r\n}\r\n.btn-copy.btn-orange:hover {\r\n    background-color: #f57700;\r\n    border-color: #f57700;\r\n}\r\n\r\n.btn-copy.btn-orange.copied {\r\n    background-color: var(--success-color);\r\n    border-color: var(--success-color);\r\n    .fa-check{\r\n      margin-left: 20rpx;\r\n    }\r\n}\r\n.btn-copy {\r\n    display: inline-flex;\r\n    align-items: center;\r\n    gap: 10rpx;\r\n}\r\n.btn-sm {\r\n    padding: 16rpx 32rpx;\r\n    font-size: 28rpx;\r\n    border-radius: 12rpx;\r\n    height: 80rpx;\r\n}\r\n.payment-channel-card {\r\n    background: white;\r\n    border-radius: 24rpx;\r\n    padding: 40rpx;\r\n    margin-bottom: 24rpx;\r\n    border: 2rpx solid var(--border-color);\r\n    transition: var(--transition-normal);\r\n\t\r\n\t.payment-channel-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\t.payment-channel-icon {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t\tborder-radius: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 30rpx;\r\n\t\tflex-shrink: 0;\r\n    .fa-university,.fa-weixin,.fa-alipay{\r\n      color: white;\r\n      font-size: 40rpx;\r\n    }\r\n\t}\r\n\t.payment-channel-info {\r\n\t\tflex: 1;\r\n\t}\r\n  .payment-channel-qrcode-container{\r\n    text-align: center;\r\n    margin-top: 15px;\r\n    .payment-channel-qrcode{\r\n      width: 80px;\r\n      height: 80px;\r\n      background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);\r\n      border: 2px dashed #ccc;\r\n      border-radius: 8px;\r\n      display: inline-flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 12px;\r\n      color: #999;\r\n    }\r\n  }\r\n\t.payment-channel-name {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: var(--text-color);\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t.payment-channel-desc {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: var(--text-secondary);\r\n\t}\r\n\t.payment-channel-details {\r\n\t\tpadding-top: 30rpx;\r\n\t\tborder-top: 2rpx solid rgba(0, 0, 0, 0.06);\r\n\t}\r\n\t.payment-detail-row {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\t.payment-detail-label {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: var(--text-secondary);\r\n\t\t\tflex-shrink: 0;\r\n\t\t\tmargin-right: 30rpx;\r\n\t\t}\r\n\t\t.payment-detail-value {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: var(--text-color);\r\n\t\t\tfont-weight: bold;\r\n\t\t\tflex: 1;\r\n\t\t\ttext-align: right;\r\n\t\t\tword-break: break-all;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// 底部按钮\r\n.action-buttons {\r\n  margin-top: 40rpx;\r\n  padding: 20rpx 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20rpx;\r\n}\r\n\r\n.confirm-button {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\ttext-align: center;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 32rpx;\r\n\tbackground-color: #2979ff;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\t.fas{\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\r\n\t&.svg-btn {\r\n\t\tbackground-color: #52c41a;\r\n\r\n\t\t&:active {\r\n\t\t\tbackground-color: #45a049;\r\n\t\t}\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/case_completed/case_completed.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "caseNumber", "api", "onMounted"], "mappings": ";;;;;;AAqOA,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,YAAYA,cAAAA,IAAI;AAAA;AAAA,MACpB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACtB,CAAC;AAED,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAE5B,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAG1BC,kBAAM,OAAC,CAAC,YAAY;AAClBC,oBAAY,MAAA,MAAA,OAAA,kDAAA,gBAAgB,OAAO;AAGnC,UAAI,WAAW,QAAQ,aAAa;AAClC,wBAAgB,OAAO;AAAA,MACxB;AAAA,IACH,CAAC;AAID,UAAM,kBAAkB,OAAO,YAAY;AACzCA,yFAAY,YAAY,OAAO;AAE/B,YAAM,EAAE,aAAa,eAAe,gBAAgB,mBAAkB,IAAK;AAG3E,YAAM,oBAAoB,eAAe,iBAAiB,kBAAkB;AAE5E,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,YAAI,mBAAmB;AAErBA,wBAAAA,MAAY,MAAA,OAAA,kDAAA,kBAAkB;AAC9B,oBAAU,MAAM,cAAc,mBAAmB,WAAW;AAC5D,oBAAU,MAAM,gBAAgB,mBAAmB,aAAa;AAChE,oBAAU,MAAM,iBAAiB,mBAAmB,cAAc;AAClE,oBAAU,MAAM,qBAAqB,mBAAmB,kBAAkB;AAG1E,qBAAW,QAAQ,UAAU,MAAM;AAEnC,gBAAM,qBAAqB,UAAU,MAAM,WAAW;AAAA,QACvD,WAAS,aAAa;AAErBA,wBAAAA,MAAY,MAAA,OAAA,kDAAA,mBAAmB;AAC/B,gBAAM,oBAAoB,mBAAmB,WAAW;AACxD,qBAAW,QAAQ;AAGnB,gBAAM,yCAAyC,iBAAiB;AAGhE,gBAAM,qBAAqB,iBAAiB;AAAA,QAC7C;AAAA,MACF,SAAO,OAAO;AACbA,sBAAc,MAAA,MAAA,SAAA,kDAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACRA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACH;AAIA,UAAM,2CAA2C,OAAOC,gBAAe;AACrE,UAAI;AACF,cAAM,SAAS,MAAMC,UAAG,IAAC,eAAe,gBAAgBD,WAAU;AAElE,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC7C,gBAAM,OAAO,OAAO;AACpB,oBAAU,MAAM,cAAc,KAAK,eAAeA;AAClD,oBAAU,MAAM,gBAAgB,KAAK,iBAAiB;AACtD,oBAAU,MAAM,iBAAiB,KAAK,kBAAkB;AACxD,oBAAU,MAAM,qBAAqB,KAAK,sBAAsB;AAAA,QACtE,OAAW;AACL,gBAAM,IAAI,MAAM,OAAO,OAAO,UAAU;AAAA,QACzC;AAAA,MACF,SAAQ,OAAO;AACdD,sBAAc,MAAA,MAAA,SAAA,kDAAA,aAAa,KAAK;AAChC,cAAM;AAAA,MACP;AAAA,IACH;AAEAG,kBAAAA,UAAU,MAAM;AAAA,IAgChB,CAAC;AAGD,UAAM,uBAAuB,CAAC,OAAO;AAEnCH,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,MACX,CAAG;AAED,YAAM,SAASE,UAAG,IAAC,eAAe,mBAAmB,EAAE;AACvDF,oBAAG,MAAC,YAAW;AACf,UAAI,OAAO,SAAS,WAAW;AAC7B,sBAAc,QAAQ,OAAO;AAAA,MACjC,OAAS;AACLA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,OAAO,OAAO;AAAA,UACrB,MAAM;AAAA,QACZ,CAAK;AAAA,MACF;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAE/B,YAAM,aAAa;AAAA,QACjB,SAAS,mBAAmB,wGAAwG;AAAA,QACpI,UAAU,mBAAmB,KAAK;AAAA,QAClC,UAAU,mBAAmB,UAAU;AAAA,QACvC,YAAY,mBAAmB,WAAW,SAAS,KAAK;AAAA,MAC5D;AAGE,YAAM,eAAe,OAAO,QAAQ,UAAU,EAC3C,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,EACvC,KAAK,GAAG;AAEXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4CAA4C,YAAY;AAAA,MACjE,CAAG;AAAA,IACH;AAGA,UAAM,oBAAoB,MAAM;AAE9B,YAAM,aAAa;AAAA,QACjB,SAAS,mBAAmB,yDAAyD;AAAA,QACrF,UAAU,mBAAmB,KAAK;AAAA,QAClC,UAAU,mBAAmB,UAAU;AAAA,QACvC,YAAY,mBAAmB,WAAW,SAAS,KAAK;AAAA,MAC5D;AAGE,YAAM,eAAe,OAAO,QAAQ,UAAU,EAC3C,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,EAAE,EACvC,KAAK,GAAG;AAEXA,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,4CAA4C,YAAY;AAAA,MACjE,CAAG;AAAA,IACH;AAGA,UAAM,qBAAqB,MAAM;AAEhCA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IAEF;AAGA,UAAM,WAAW,MAAM;AACrBA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM,cAAc,MAAM;AAAA,MAC9B,CAAG;AACDA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,MAAM;AAAA,MACV,CAAG;AACD,eAAS,QAAQ;AAAA,IACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClbA,GAAG,WAAW,eAAe;"}