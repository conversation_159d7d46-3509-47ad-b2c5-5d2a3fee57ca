/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-1cf27b2a:root {
  --primary-color: #3b7eeb;
  --primary-light: #e6f0ff;
  --primary-dark: #2c62c9;
  --secondary-color: #f5f7fa;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --danger-color: #f5222d;
  --info-color: #1890ff;
  --text-color: #333333;
  --text-secondary: #666666;
  --text-light: #999999;
  --border-color: #e8e8e8;
  --background-color: #f5f7fa;
  --card-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  --transition-normal: all 0.3s ease;
}
.index-container.data-v-1cf27b2a {
  height: calc(100% - 94px);
  overflow-y: auto;
  padding: 30rpx;
  padding-bottom: 140rpx;
  background-color: #f8fafc;
}
.welcome-banner-minimal.data-v-1cf27b2a {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 0;
  margin: -30rpx -30rpx 40rpx -30rpx;
  position: relative;
  overflow: hidden;
  padding: 64rpx 40rpx;
}
.banner-minimal-content.data-v-1cf27b2a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}
.banner-minimal-left.data-v-1cf27b2a {
  flex-shrink: 0;
}
.banner-minimal-lines.data-v-1cf27b2a {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12rpx;
}
.line.data-v-1cf27b2a {
  height: 6rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 4rpx;
  transition-property: all;
  transition-duration: 3s;
  transition-timing-function: ease-in-out;
}
.line-1.data-v-1cf27b2a {
  width: 80rpx;
  animation-name: line-expand-1;
  animation-duration: 3s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
}
.line-2.data-v-1cf27b2a {
  width: 110rpx;
  animation-name: line-expand-2;
  animation-duration: 3s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-delay: 0.5s;
}
.line-3.data-v-1cf27b2a {
  width: 60rpx;
  animation-name: line-expand-3;
  animation-duration: 3s;
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-delay: 1s;
}
.banner-minimal-right.data-v-1cf27b2a {
  flex: 1;
  margin-left: 48rpx;
  text-align: left;
}
.banner-minimal-title.data-v-1cf27b2a {
  color: white;
  font-size: 40rpx;
  font-weight: 600;
  margin: 0 0 12rpx 0;
  line-height: 1.3;
  letter-spacing: 1rpx;
}
.banner-minimal-subtitle.data-v-1cf27b2a {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28rpx;
  margin: 0;
  letter-spacing: 4rpx;
  font-weight: 300;
}
.banner-minimal-decoration.data-v-1cf27b2a {
  position: absolute;
  top: -40rpx;
  right: -40rpx;
  width: 240rpx;
  height: 240rpx;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}
.grid-container.data-v-1cf27b2a {
  margin-top: 40rpx;
}
.grid-row.data-v-1cf27b2a {
  display: flex;
  margin-bottom: 30rpx;
}
.grid-item.data-v-1cf27b2a {
  background-color: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: var(--transition-normal);
  cursor: pointer;
  border: 2rpx solid rgba(0, 0, 0, 0.03);
}
.grid-item.data-v-1cf27b2a:hover {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-4rpx);
}
.feature-arrow.data-v-1cf27b2a {
  color: var(--text-light);
  font-size: 28rpx;
  flex-shrink: 0;
}

/* // :first-child 是第一个元素
.grid-item:first-child {
	margin-left: 0;
}
// :last-child 是最后一个元素
.grid-item:last-child {
	margin-right: 0;
} */
.grid-icon.data-v-1cf27b2a {
  width: 90rpx;
  height: 90rpx;
  border-radius: 24rpx;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}
.grid-icon .fas.data-v-1cf27b2a {
  font-size: 40rpx;
  color: var(--primary-color);
}
.grid-item-content.data-v-1cf27b2a {
  flex: 1;
}
.grid-title.data-v-1cf27b2a {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 8rpx;
}
.grid-subtitle.data-v-1cf27b2a {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.3;
}

/* 电子签名按钮样式 */
.sign-button.data-v-1cf27b2a {
  margin: 20rpx auto;
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #2979ff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
  box-shadow: 0 2rpx 10rpx rgba(41, 121, 255, 0.3);
}