"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "work_order_detail",
  setup(__props) {
    const basicInfo = common_vendor.ref({
      case_number: "",
      initiate_date: "",
      close_date: "",
      case_status_cn: "",
      mediation_progress: ""
    });
    const workOrderData = common_vendor.ref([]);
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:96", "页面参数:", options);
      if (options && options.case_number) {
        handleUrlParams(options);
      }
    });
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:105", "工单详情页面组件已挂载");
    });
    const handleUrlParams = async (options) => {
      const { case_number, initiate_date, close_date, case_status_cn, mediation_progress } = options;
      const hasCompleteParams = case_number && (initiate_date || close_date) && case_status_cn || mediation_progress;
      if (hasCompleteParams) {
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:120", "检测到完整参数，直接显示基本信息");
        basicInfo.value.case_number = decodeURIComponent(case_number);
        basicInfo.value.initiate_date = decodeURIComponent(initiate_date);
        basicInfo.value.closeDate = decodeURIComponent(close_date);
        basicInfo.value.case_status_cn = decodeURIComponent(case_status_cn);
        basicInfo.value.mediation_progress = decodeURIComponent(mediation_progress);
        if (caseNumber.value) {
          fetchWorkOrderDetail(basicInfo.value.case_number);
        }
      } else if (case_number) {
        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:134", "仅有案件编号，调用接口获取详细信息");
        const decodedCaseNumber = decodeURIComponent(case_number);
        caseNumber.value = decodedCaseNumber;
        await fetchMediationSingleDetail(decodedCaseNumber);
        await fetchWorkOrderDetail(decodedCaseNumber);
      }
    };
    const fetchMediationSingleDetail = async (caseNumber2) => {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        const result = await utils_api.api.mediationQuery.getSingleDetail(caseNumber2);
        common_vendor.index.hideLoading();
        if (result.state === "success" && result.data) {
          const data = result.data;
          basicInfo.value.case_number = data.case_number || caseNumber2;
          basicInfo.value.initiate_date = data.initiate_date || "";
          basicInfo.value.case_status_cn = data.case_status_cn || "";
          basicInfo.value.mediation_progress = data.mediation_progress || "";
        } else {
          common_vendor.index.showToast({
            title: result.msg || "获取案件信息失败",
            icon: "none",
            duration: 2e3
          });
        }
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:166", "获取案件详情失败:", error);
        common_vendor.index.showToast({
          title: "获取案件信息失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const fetchWorkOrderDetail = async (id) => {
      if (id) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        const result = await utils_api.api.workOrder.getDetail(id);
        common_vendor.index.hideLoading();
        if (result.state === "success" && result.data) {
          workOrderData.value = result.data;
        } else {
          common_vendor.index.showToast({
            title: res.msg || "获取调解确认失败",
            icon: "none"
          });
        }
      }
    };
    const handleAccept = () => {
      common_vendor.index.showModal({
        title: "确认接受",
        content: "您确定要接受此调解吗？",
        success: (res2) => {
          if (res2.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            if (caseNumber.value) {
              utils_api.api.workOrder.acceptWorkOrder(caseNumber.value).then((res3) => {
                common_vendor.index.hideLoading();
                if (res3.state === "success") {
                  common_vendor.index.showToast({
                    title: res3.msg,
                    icon: "success",
                    duration: 1500
                  });
                  setTimeout(() => {
                    common_vendor.index.navigateTo({
                      url: `/pages/solution_confirm/solution_confirm?case_number=${res3.data.case_number}`,
                      success: () => {
                        common_vendor.index.__f__("log", "at pages/work_order_detail/work_order_detail.vue:226", "跳转到调解方案确认页面");
                      },
                      fail: (err) => {
                        common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:229", "跳转失败", err);
                        common_vendor.index.showToast({
                          title: "跳转失败",
                          icon: "none"
                        });
                      }
                    });
                  }, 1500);
                } else {
                  common_vendor.index.showToast({
                    title: res3.msg || "操作失败",
                    icon: "none"
                  });
                }
              }).catch((err) => {
                common_vendor.index.hideLoading();
                common_vendor.index.__f__("error", "at pages/work_order_detail/work_order_detail.vue:246", "接受调解失败", err);
                common_vendor.index.showToast({
                  title: "接受调解失败",
                  icon: "none"
                });
              });
            } else {
              common_vendor.index.showToast({
                title: "缺少调解案件编号",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const handleReject = () => {
      common_vendor.index.redirectTo({
        url: `/pages/mediation_query/mediation_query`
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(basicInfo.value.case_number),
        b: common_vendor.t(basicInfo.value.case_status_cn),
        c: basicInfo.value.case_status_cn === "待确认" ? 1 : "",
        d: common_vendor.t(basicInfo.value.initiate_date),
        e: basicInfo.value.case_status_cn === "已关闭"
      }, basicInfo.value.case_status_cn === "已关闭" ? {
        f: common_vendor.t(basicInfo.value.close_date)
      } : {}, {
        g: common_vendor.f(workOrderData.value.mediation_config, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(item.value),
            c: item.id
          };
        }),
        h: common_vendor.f(workOrderData.value.attachments, (file, k0, i0) => {
          return {
            a: common_vendor.n(file.name.split(".").pop() === "pdf" ? "fa-file-pdf" : "fa-file-image"),
            b: file.name.split(".").pop() === "pdf" ? "#ff4d4f" : "#52c41a",
            c: common_vendor.t(file.name),
            d: file.id
          };
        }),
        i: workOrderData.value.case_status_cn === "已关闭"
      }, workOrderData.value.case_status_cn === "已关闭" ? {
        j: common_vendor.t(workOrderData.value.closingReason || "调解案件已超过规定期限。")
      } : {
        k: common_vendor.o(handleAccept),
        l: common_vendor.o(handleReject)
      });
    };
  }
};
wx.createPage(_sfc_main);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/work_order_detail/work_order_detail.js.map
