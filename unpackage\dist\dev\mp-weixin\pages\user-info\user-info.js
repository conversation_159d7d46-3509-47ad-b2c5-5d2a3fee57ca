"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_wechatAuth = require("../../utils/wechat-auth.js");
const utils_userStore = require("../../utils/user-store.js");
if (!Array) {
  const _easycom_user_info_form2 = common_vendor.resolveComponent("user-info-form");
  const _easycom_uni_load_more2 = common_vendor.resolveComponent("uni-load-more");
  (_easycom_user_info_form2 + _easycom_uni_load_more2)();
}
const _easycom_user_info_form = () => "../../components/user-info-form/user-info-form.js";
const _easycom_uni_load_more = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.js";
if (!Math) {
  (_easycom_user_info_form + _easycom_uni_load_more)();
}
const _sfc_main = {
  __name: "user-info",
  setup(__props) {
    const isProcessing = common_vendor.ref(false);
    const initialUserInfo = common_vendor.ref({});
    const loadingText = common_vendor.ref({
      contentText: {
        contentdown: "正在保存...",
        contentrefresh: "正在保存...",
        contentnomore: "保存完成"
      }
    });
    common_vendor.onMounted(() => {
      loadUserInfo();
    });
    function loadUserInfo() {
      try {
        const storedInfo = utils_wechatAuth.wechatAuth.getStoredUserInfo();
        if (storedInfo.wechatUserInfo) {
          initialUserInfo.value = storedInfo.wechatUserInfo;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user-info/user-info.vue:74", "加载用户信息失败:", error);
      }
    }
    function handleAvatarChange(avatarUrl) {
      common_vendor.index.__f__("log", "at pages/user-info/user-info.vue:80", "头像已更新:", avatarUrl);
    }
    function handleNicknameChange(nickname) {
      common_vendor.index.__f__("log", "at pages/user-info/user-info.vue:85", "昵称已更新:", nickname);
    }
    async function handleSubmit(result) {
      if (!result.success) {
        common_vendor.index.showToast({
          title: result.error || "设置失败",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      try {
        isProcessing.value = true;
        const completeUserData = utils_wechatAuth.wechatAuth.getCompleteUserData();
        utils_userStore.userStore.setWechatUserInfo(result.userInfo);
        common_vendor.index.showToast({
          title: "设置完成",
          icon: "success",
          duration: 1500
        });
        setTimeout(() => {
          common_vendor.index.switchTab({
            url: "/pages/index/index"
          });
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/user-info/user-info.vue:121", "保存用户信息失败:", error);
        common_vendor.index.showToast({
          title: "保存失败，请重试",
          icon: "none",
          duration: 2e3
        });
      } finally {
        isProcessing.value = false;
      }
    }
    function handleSkip() {
      common_vendor.index.showModal({
        title: "确认跳过",
        content: "跳过设置后，您可以稍后在个人中心完善信息",
        confirmText: "确认跳过",
        cancelText: "继续设置",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.switchTab({
              url: "/pages/index/index"
            });
          }
        }
      });
    }
    common_vendor.index.setNavigationBarTitle({
      title: "完善个人信息"
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(handleSubmit),
        b: common_vendor.o(handleSkip),
        c: common_vendor.o(handleAvatarChange),
        d: common_vendor.o(handleNicknameChange),
        e: common_vendor.p({
          ["allow-skip"]: true,
          ["submit-button-text"]: "完成设置",
          ["initial-user-info"]: initialUserInfo.value
        }),
        f: isProcessing.value
      }, isProcessing.value ? {
        g: common_vendor.p({
          status: "loading",
          ["content-text"]: loadingText.value
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-759141ee"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/user-info/user-info.js.map
