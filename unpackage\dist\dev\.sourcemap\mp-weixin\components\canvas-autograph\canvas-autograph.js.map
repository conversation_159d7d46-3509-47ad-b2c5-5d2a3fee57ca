{"version": 3, "file": "canvas-autograph.js", "sources": ["components/canvas-autograph/canvas-autograph.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovd29yay_kuI3oia_otYTkuqfns7vnu58vbm9uLXBlcmZvcm1pbmctYXNzZXRzL2NvbXBvbmVudHMvY2FudmFzLWF1dG9ncmFwaC9jYW52YXMtYXV0b2dyYXBoLnZ1ZQ"], "sourcesContent": ["<template>\n\t<view class=\"whole canvas-autograph flexc\" @touchmove.prevent.stop @wheel.prevent.stop v-show=\"modelValue\">\n\t\t<canvas class=\"scroll-view\" id=\"mycanvas\" canvas-id=\"mycanvas\" \n\t\t\t@touchstart=\"touchstart\" \n\t\t\t@touchmove=\"touchmove\" \n\t\t\t@touchend=\"touchend\"/>\n\t\t<!-- 操作按钮区域 - 水平排版 -->\n\t\t<view class=\"action-buttons\">\n\t\t\t<view class=\"btn btn-clear\" @click=\"clear\">\n\t\t\t\t<text>清空</text>\n\t\t\t</view>\n\t\t\t<view class=\"btn btn-confirm\" @click=\"confirm\">\n\t\t\t\t<text>确认</text>\n\t\t\t</view>\n\t\t\t<view class=\"btn btn-cancel\" @click=\"cancel\">\n\t\t\t\t<text>取消</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script setup>\n\t/*\n\t\t使用如下\n\t\t<canvas-autograph v-model=\"isCanvas\" @complete=\"complete\"/>\n\t\t\n\t\t// 打开、关闭\n\t\tlet isCanvas = ref(false)\n\t\t// 确认事件\n\t\tconst complete = e=>{\n\t\t\tconsole.log(e)\n\t\t}\n\t\n\t*/\n\timport { ref, reactive, watch, getCurrentInstance } from 'vue'\n\t\n\tconst emits = defineEmits(['update:modelValue','complete'])\n\t\n\tconst props = defineProps({\n\t\tmodelValue:Boolean,\n\t})\n\tconst _this = getCurrentInstance()\n\twatch(()=>props.modelValue,e=>{\n\t\t// 这里可进行 tabbar 的 显示、隐藏  不要也可以\n\t\t// 自己写\n\t},{\n\t\timmediate:true, // 是否默认执行一次  默认为false\n\t})\n\t\n\tlet points = reactive([]) //路径点集合 \n\t\n\tlet canvaCtx = reactive(uni.createCanvasContext('mycanvas', _this))   //创建绘图对象\n\t//设置画笔样式\n\tcanvaCtx.lineWidth = 4;\n\tcanvaCtx.lineCap = 'round'\n\tcanvaCtx.lineJoin = 'round'\n\t\n\t\n\t//触摸开始，获取到起点\n\tconst touchstart = e=>{\n\t\tlet startX = e.changedTouches[0].x\n\t\tlet startY = e.changedTouches[0].y\n\t\tlet startPoint = {X:startX,Y:startY}\n\t\tpoints.push(startPoint);\n\t\t//每次触摸开始，开启新的路径\n\t\tcanvaCtx.beginPath();\n\t}\n\t//触摸移动，获取到路径点\n\tconst touchmove = e=>{\n\t\tlet moveX = e.changedTouches[0].x\n\t\tlet moveY = e.changedTouches[0].y\n\t\tlet movePoint = {X:moveX,Y:moveY}\n\t\tpoints.push(movePoint)       //存点\n\t\tlet len = points.length\n\t\tif(len>=2){\n\t\t\tdraw()\n\t\t}\n\t}\n\t//绘制路径\n\tconst draw = ()=> {\n\t\tlet point1 = points[0]\n\t\tlet point2 = points[1]\n\t\tpoints.shift()\n\t\tcanvaCtx.moveTo(point1.X, point1.Y)\n\t\tcanvaCtx.lineTo(point2.X, point2.Y)\n\t\tcanvaCtx.stroke()\n\t\tcanvaCtx.draw(true)\n\t}\n\t// 触摸结束，将未绘制的点清空防止对后续路径产生干扰\n\tconst touchend = e=>{                   \n\t\tpoints = [];\n\t}\n\t// 清空画布\n\tconst clear = ()=>{\n\t\treturn uni.getSystemInfo()\n\t\t.then(res=>{\n\t\t\tcanvaCtx.clearRect(0, 0, res.windowWidth, res.windowHeight);\n\t\t\tcanvaCtx.draw(true);\n\t\t\treturn res\n\t\t})\n\t\t.catch(err=>{\n\t\t\t// console.log(err);\n\t\t})\n\t}\n\t// 确认\n\tconst confirm = ()=>{\n\t\tuni.canvasToTempFilePath({ canvasId: 'mycanvas', }, _this, _this.parent)\n\t\t.then(res=>{\n\t\t\tconsole.log(res.tempFilePath);\n\t\t\temits('complete',res.tempFilePath)\n\t\t\tcancel()\n\t\t})\n\t}\n\t// 取消\n\tconst cancel = ()=>{\n\t\tclear().then(res=>emits('update:modelValue',false))\n\t}\n\n</script>\n\n<style scoped lang=\"scss\">\n\t.canvas-autograph {\n\t\tposition: fixed;\n\t\tz-index: 998;\n\t\twidth: 100vw;\n\t\theight: 100vh;\n\t\ttop: 0;\n\t\tleft: 0;\n\t\t.scroll-view {\n\t\t\twidth: 100%;\n\t\t\theight: 100%;\n\t\t\tbackground-color: #FFFFFF;\n\t\t}\n\t\t\n\t\t.action-buttons {\n\t\t\tposition: absolute;\n\t\t\tbottom: 40rpx;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tpadding: 0 30rpx;\n\t\t\t\n\t\t\t.btn {\n\t\t\t\tmin-width: 160rpx;\n\t\t\t\theight: 80rpx;\n\t\t\t\tmargin: 0 20rpx;\n\t\t\t\tborder-radius: 40rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\t\t\t}\n\t\t\t\n\t\t\t.btn-clear {\n\t\t\t\tbackground-color: #F4F4F5;\n\t\t\t\tcolor: #909399;\n\t\t\t}\n\t\t\t\n\t\t\t.btn-confirm {\n\t\t\t\tbackground-color: #409EFF;\n\t\t\t}\n\t\t\t\n\t\t\t.btn-cancel {\n\t\t\t\tbackground-color: #F67D7D;\n\t\t\t}\n\t\t}\n\t\n\t}\n</style>\n", "import Component from 'D:/work/不良资产系统/non-performing-assets/components/canvas-autograph/canvas-autograph.vue'\nwx.createComponent(Component)"], "names": ["getCurrentInstance", "watch", "reactive", "uni"], "mappings": ";;;;;;;;;AAoCC,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAGd,UAAM,QAAQA,cAAAA,mBAAoB;AAClCC,kBAAAA,MAAM,MAAI,MAAM,YAAW,OAAG;AAAA,IAG/B,GAAG;AAAA,MACD,WAAU;AAAA;AAAA,IACZ,CAAE;AAED,QAAI,SAASC,cAAQ,SAAC,EAAE;AAExB,QAAI,WAAWA,cAAAA,SAASC,cAAAA,MAAI,oBAAoB,YAAY,KAAK,CAAC;AAElE,aAAS,YAAY;AACrB,aAAS,UAAU;AACnB,aAAS,WAAW;AAIpB,UAAM,aAAa,OAAG;AACrB,UAAI,SAAS,EAAE,eAAe,CAAC,EAAE;AACjC,UAAI,SAAS,EAAE,eAAe,CAAC,EAAE;AACjC,UAAI,aAAa,EAAC,GAAE,QAAO,GAAE,OAAM;AACnC,aAAO,KAAK,UAAU;AAEtB,eAAS,UAAS;AAAA,IAClB;AAED,UAAM,YAAY,OAAG;AACpB,UAAI,QAAQ,EAAE,eAAe,CAAC,EAAE;AAChC,UAAI,QAAQ,EAAE,eAAe,CAAC,EAAE;AAChC,UAAI,YAAY,EAAC,GAAE,OAAM,GAAE,MAAK;AAChC,aAAO,KAAK,SAAS;AACrB,UAAI,MAAM,OAAO;AACjB,UAAG,OAAK,GAAE;AACT,aAAM;AAAA,MACN;AAAA,IACD;AAED,UAAM,OAAO,MAAK;AACjB,UAAI,SAAS,OAAO,CAAC;AACrB,UAAI,SAAS,OAAO,CAAC;AACrB,aAAO,MAAO;AACd,eAAS,OAAO,OAAO,GAAG,OAAO,CAAC;AAClC,eAAS,OAAO,OAAO,GAAG,OAAO,CAAC;AAClC,eAAS,OAAQ;AACjB,eAAS,KAAK,IAAI;AAAA,IAClB;AAED,UAAM,WAAW,OAAG;AACnB,eAAS,CAAA;AAAA,IACT;AAED,UAAM,QAAQ,MAAI;AACjB,aAAOA,cAAAA,MAAI,cAAe,EACzB,KAAK,SAAK;AACV,iBAAS,UAAU,GAAG,GAAG,IAAI,aAAa,IAAI,YAAY;AAC1D,iBAAS,KAAK,IAAI;AAClB,eAAO;AAAA,MACV,CAAG,EACA,MAAM,SAAK;AAAA,MAEd,CAAG;AAAA,IACD;AAED,UAAM,UAAU,MAAI;AACnBA,oBAAG,MAAC,qBAAqB,EAAE,UAAU,WAAU,GAAK,OAAO,MAAM,MAAM,EACtE,KAAK,SAAK;AACVA,sBAAY,MAAA,MAAA,OAAA,2DAAA,IAAI,YAAY;AAC5B,cAAM,YAAW,IAAI,YAAY;AACjC,eAAQ;AAAA,MACX,CAAG;AAAA,IACD;AAED,UAAM,SAAS,MAAI;AAClB,YAAK,EAAG,KAAK,SAAK,MAAM,qBAAoB,KAAK,CAAC;AAAA,IAClD;;;;;;;;;;;;;;;;;;;ACnHF,GAAG,gBAAgB,SAAS;"}