"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const config_assets = require("../config/assets.js");
const utils_defaultAvatarBase64 = require("./default-avatar-base64.js");
function getDefaultAvatar(type = "default", gender = null, useBase64Fallback = true) {
  const avatarUrl = config_assets.AssetUrlGenerator.getAvatarUrl(type, { gender, fallback: true });
  if (!avatarUrl && useBase64Fallback) {
    return utils_defaultAvatarBase64.getBase64Avatar(type, gender);
  }
  return avatarUrl || utils_defaultAvatarBase64.getBase64Avatar(type, gender);
}
function handleAvatarError(avatarUrl, options = {}) {
  const { gender, useGenderDefault = true, useBase64Fallback = true } = options;
  if (config_env.isDebug()) {
    common_vendor.index.__f__("warn", "at utils/avatar-helper.js:135", "头像加载失败，使用默认头像:", avatarUrl);
  }
  if (useGenderDefault && gender !== void 0) {
    return getDefaultAvatar("default", gender, useBase64Fallback);
  }
  return getDefaultAvatar("default", null, useBase64Fallback);
}
function createAvatarPreviewUrl(avatarUrl, options = {}) {
  const { addTimestamp = false, gender } = options;
  if (!avatarUrl || !config_assets.AssetUrlGenerator.isValidUrl(avatarUrl)) {
    return getDefaultAvatar("placeholder", gender);
  }
  if (avatarUrl.startsWith("wxfile://") || avatarUrl.startsWith("http://tmp/")) {
    return avatarUrl;
  }
  if (addTimestamp) {
    return config_assets.AssetUrlGenerator.addTimestamp(avatarUrl);
  }
  return avatarUrl;
}
function cleanupTempAvatars(filePaths) {
  if (!Array.isArray(filePaths)) {
    filePaths = [filePaths];
  }
  filePaths.forEach((filePath) => {
    if (filePath && (filePath.startsWith("wxfile://") || filePath.includes("tmp"))) {
      try {
        common_vendor.index.removeSavedFile({
          filePath,
          success: () => {
            if (config_env.isDebug()) {
              common_vendor.index.__f__("log", "at utils/avatar-helper.js:258", "临时头像文件已清理:", filePath);
            }
          },
          fail: (error) => {
            if (config_env.isDebug()) {
              common_vendor.index.__f__("warn", "at utils/avatar-helper.js:263", "清理临时头像文件失败:", filePath, error);
            }
          }
        });
      } catch (error) {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("warn", "at utils/avatar-helper.js:269", "清理临时头像文件异常:", error);
        }
      }
    }
  });
}
exports.cleanupTempAvatars = cleanupTempAvatars;
exports.createAvatarPreviewUrl = createAvatarPreviewUrl;
exports.getDefaultAvatar = getDefaultAvatar;
exports.handleAvatarError = handleAvatarError;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/avatar-helper.js.map
