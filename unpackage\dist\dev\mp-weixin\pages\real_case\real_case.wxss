/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.real-case-container.data-v-54ea8e22 {
  height: calc(100% - 94px);
  overflow-y: auto;
  background-color: #f8fafc;
  padding: 30rpx 30rpx 140rpx;
}
.header.data-v-54ea8e22 {
  background-color: white;
  box-shadow: rgba(0, 0, 0, 0.04) 0px 2px 8px;
  margin-bottom: 30rpx;
  border-radius: 24rpx;
  padding: 46rpx;
  transition: all 0.3s ease;
  border-width: 2rpx;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.03);
  border-image: initial;
}
.header-title.data-v-54ea8e22 {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.header-text.data-v-54ea8e22 {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
}

/* 
.search-filter-section {
	background-color: #ffffff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-box {
	margin-bottom: 30rpx;
}

.filter-tabs {
	display: flex;
	gap: 20rpx;
	overflow-x: auto;
}

.filter-tab {
	padding: 15rpx 30rpx;
	background-color: #f8f8f8;
	color: #666;
	border-radius: 30rpx;
	font-size: 26rpx;
	white-space: nowrap;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background-color: #2979ff;
	color: #ffffff;
} */
.case-card.data-v-54ea8e22 {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  position: relative;
}
.case-header.data-v-54ea8e22 {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}
.case-title.data-v-54ea8e22 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
  line-height: 1.4;
}
.case-date.data-v-54ea8e22 {
  font-size: 24rpx;
  color: #999;
  margin-left: 20rpx;
}
.case-tags.data-v-54ea8e22 {
  display: flex;
  gap: 15rpx;
  margin-bottom: 25rpx;
}
.case-tag.data-v-54ea8e22 {
  padding: 8rpx 16rpx;
  font-size: 22rpx;
  border-radius: 20rpx;
}
.type-tag.data-v-54ea8e22 {
  background-color: #e3f2fd;
  color: #1976d2;
}
.status-tag.success.data-v-54ea8e22 {
  background-color: #e8f5e8;
  color: #4caf50;
}
.status-tag.warning.data-v-54ea8e22 {
  background-color: #fff3e0;
  color: #ff9800;
}
.status-tag.processing.data-v-54ea8e22 {
  background-color: #f3e5f5;
  color: #9c27b0;
}
.status-tag.default.data-v-54ea8e22 {
  background-color: #f5f5f5;
  color: #666;
}
.amount-info.data-v-54ea8e22 {
  margin-bottom: 25rpx;
}
.amount-row.data-v-54ea8e22 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}
.amount-label.data-v-54ea8e22 {
  font-size: 26rpx;
  color: #666;
}
.debt-amount.data-v-54ea8e22 {
  font-size: 26rpx;
  color: #f44336;
  font-weight: 600;
}
.resolved-amount.data-v-54ea8e22 {
  font-size: 26rpx;
  color: #4caf50;
  font-weight: 600;
}
.reduction-info.data-v-54ea8e22 {
  text-align: right;
  margin-top: 5rpx;
}
.reduction-rate.data-v-54ea8e22 {
  font-size: 24rpx;
  color: #ff9800;
  font-weight: 500;
}
.case-summary.data-v-54ea8e22 {
  margin-bottom: 25rpx;
}
.summary-text.data-v-54ea8e22 {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.detail-button.data-v-54ea8e22 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15rpx 0;
  border-top: 2rpx solid #f0f0f0;
  margin-top: 20rpx;
}
.button-text.data-v-54ea8e22 {
  font-size: 28rpx;
  color: #2979ff;
  margin-right: 10rpx;
}
.arrow-icon.data-v-54ea8e22 {
  font-size: 32rpx;
  color: #2979ff;
  transform: rotate(90deg);
}
.empty-state.data-v-54ea8e22, .loading-state.data-v-54ea8e22 {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;
}
.empty-text.data-v-54ea8e22, .loading-text.data-v-54ea8e22 {
  font-size: 28rpx;
  color: #999;
}

/* uni-easyinput 组件样式覆盖 */
.data-v-54ea8e22 .uni-easyinput__content {
  height: 80rpx;
  background-color: #f8f8f8;
  border: none;
  border-radius: 40rpx;
  padding: 0 30rpx;
}
.data-v-54ea8e22 .uni-easyinput__content-input {
  font-size: 28rpx;
}