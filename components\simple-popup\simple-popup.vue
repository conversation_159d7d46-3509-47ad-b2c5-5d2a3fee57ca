<template>
  <view class="simple-popup" v-if="visible" @touchmove.stop.prevent>
    <!-- 遮罩层 -->
    <view 
      class="popup-mask" 
      :class="{ 'popup-mask--show': showMask }"
      @click="onMaskClick"
    ></view>
    
    <!-- 弹窗内容 -->
    <view 
      class="popup-content" 
      :class="[
        `popup-content--${type}`,
        { 'popup-content--show': showContent }
      ]"
      @click.stop
    >
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue';

// Props
const props = defineProps({
  // 弹窗类型：center, bottom, top
  type: {
    type: String,
    default: 'center'
  },
  // 是否显示
  modelValue: {
    type: Boolean,
    default: false
  },
  // 点击遮罩是否关闭
  maskClick: {
    type: Boolean,
    default: true
  },
  // 是否显示遮罩
  mask: {
    type: Boolean,
    default: true
  },
  // 动画时长
  duration: {
    type: Number,
    default: 300
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'open', 'close', 'maskClick']);

// 响应式数据
const visible = ref(false);
const showMask = ref(false);
const showContent = ref(false);

// 监听modelValue变化
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    open();
  } else {
    close();
  }
}, { immediate: true });

// 打开弹窗
async function open() {
  if (visible.value) return;
  
  visible.value = true;
  
  await nextTick();
  
  // 先显示遮罩
  if (props.mask) {
    showMask.value = true;
  }
  
  // 延迟显示内容，创建动画效果
  setTimeout(() => {
    showContent.value = true;
    emit('open');
  }, 50);
}

// 关闭弹窗
function close() {
  if (!visible.value) return;
  
  showContent.value = false;
  showMask.value = false;
  
  // 等待动画完成后隐藏弹窗
  setTimeout(() => {
    visible.value = false;
    emit('update:modelValue', false);
    emit('close');
  }, props.duration);
}

// 遮罩点击处理
function onMaskClick() {
  emit('maskClick');
  
  if (props.maskClick) {
    close();
  }
}

// 暴露方法给父组件
defineExpose({
  open,
  close
});
</script>

<style lang="scss" scoped>
.simple-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &--show {
    opacity: 1;
  }
}

.popup-content {
  position: relative;
  background-color: #ffffff;
  border-radius: 16rpx;
  max-width: 90%;
  max-height: 80%;
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  
  &--show {
    transform: scale(1);
    opacity: 1;
  }
  
  // 居中弹出
  &--center {
    // 默认样式已设置
  }
  
  // 底部弹出
  &--bottom {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    max-width: 100%;
    border-radius: 20rpx 20rpx 0 0;
    transform: translateY(100%);
    
    &.popup-content--show {
      transform: translateY(0);
    }
  }
  
  // 顶部弹出
  &--top {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    max-width: 100%;
    border-radius: 0 0 20rpx 20rpx;
    transform: translateY(-100%);
    
    &.popup-content--show {
      transform: translateY(0);
    }
  }
}

// 适配安全区域
.popup-content--bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
