<template>
  <view class="webview-container">
    <web-view :src="url" v-if="url"></web-view>
    <view v-else class="loading-container">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      url: ''
    };
  },
  onLoad(options) {
    console.log('webview页面接收参数:', options);
    if (options && options.url) {
      this.url = decodeURIComponent(options.url);
      console.log('解码后的URL:', this.url);
    }
  }
};
</script>

<style scoped>
.webview-container {
  width: 100%;
  height: 100vh;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-text {
  color: #999;
  font-size: 14px;
}
</style>