// 用户状态管理
import { reactive, computed } from 'vue';
import wechatAuth, { checkLogin, logout } from './wechat-auth.js';
import { isDebug } from '@/config/env.js';

/**
 * 用户状态枚举
 */
export const USER_STATUS = {
  GUEST: 'guest',           // 游客状态
  LOGGED_IN: 'logged_in',   // 已登录
  VERIFIED: 'verified',     // 已实名认证
  DISABLED: 'disabled'      // 账户被禁用
};

/**
 * 用户数据结构
 */
const defaultUserData = {
  // 基础信息
  userId: null,
  username: '',
  nickname: '',
  avatar: '',
  phone: '',
  email: '',

  // 微信信息
  openid: '',
  unionid: '',
  wechat_openid: '',
  wechat_nickname: '',
  wechat_avatar_url: '',
  wechatInfo: {
    nickName: '',
    avatarUrl: '',
    gender: 0,
    country: '',
    province: '',
    city: '',
    language: ''
  },

  // 认证信息
  isVerified: false,
  realName: '',
  idCard: '',
  verifyTime: null,

  // 状态信息
  status: USER_STATUS.GUEST,
  loginTime: null,
  lastActiveTime: null,

  // 权限信息
  permissions: [],
  roles: [],

  detect_auth_result: false
};

/**
 * 用户状态管理
 */
class UserStore {
  constructor() {
    // 响应式用户数据
    this.state = reactive({
      ...defaultUserData,
      isLoading: false,
      isInitialized: false
    });
    
    // 初始化
    this.init();
  }

  /**
   * 初始化用户状态
   */
  async init() {
    try {
      this.state.isLoading = true;
      
      // 检查登录状态
      const loginStatus = await checkLogin();
      
      if (loginStatus.isLogin) {
        // 从本地存储恢复用户信息
        await this.restoreUserInfo();
        
        // 验证token有效性
        await this.validateToken();
      } else {
        // 清除过期信息
        this.clearUserInfo();
      }
      
      this.state.isInitialized = true;
      
      if (isDebug()) {
        console.log('用户状态初始化完成:', this.state);
      }
    } catch (error) {
      console.error('用户状态初始化失败:', error);
      this.clearUserInfo();
    } finally {
      this.state.isLoading = false;
    }
  }

  /**
   * 从本地存储恢复用户信息
   */
  async restoreUserInfo() {
    try {
      const userInfo = uni.getStorageSync('userInfo');
      const wechatUserInfo = uni.getStorageSync('wechat_userInfo');
      const openid = uni.getStorageSync('openid');
      const unionid = uni.getStorageSync('unionid');
      const wechatOpenid = uni.getStorageSync('wechat_openid');
      const userIsStaff = uni.getStorageSync('detect_auth_result');

      if (userInfo) {
        Object.assign(this.state, userInfo);
        this.state.status = USER_STATUS.LOGGED_IN;
      }

      if (wechatUserInfo) {
        this.state.wechatInfo = wechatUserInfo;
        // 如果没有设置头像，使用微信头像
        if (!this.state.avatar && wechatUserInfo.avatarUrl) {
          this.state.avatar = wechatUserInfo.avatarUrl;
        }
        // 如果没有设置昵称，使用微信昵称
        if (!this.state.nickname && wechatUserInfo.nickName) {
          this.state.nickname = wechatUserInfo.nickName;
        }
      }

      if (openid) {
        this.state.openid = openid;
      }

      if (unionid) {
        this.state.unionid = unionid;
      }

      if (wechatOpenid) {
        this.state.wechat_openid = wechatOpenid;
      }

      if (userIsStaff !== null && userIsStaff !== undefined) {
        this.state.detect_auth_result = userIsStaff;
      }

    } catch (error) {
      console.error('恢复用户信息失败:', error);
    }
  }

  /**
   * 验证token有效性
   */
  async validateToken() {
    try {
      // 这里可以调用后端API验证token
      // 暂时跳过，实际项目中应该实现
      return true;
    } catch (error) {
      console.error('Token验证失败:', error);
      this.clearUserInfo();
      return false;
    }
  }

  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    try {
      // 合并用户信息到状态中
      Object.assign(this.state, userInfo);
      this.state.status = USER_STATUS.LOGGED_IN;
      this.state.loginTime = new Date().toISOString();
      this.updateLastActiveTime();

      // 保存完整的用户信息到本地存储，包含所有从API返回的字段
      const userInfoToStore = {
        // 基础字段
        userId: this.state.userId,
        username: this.state.username || this.state.wechat_nickname,
        nickname: this.state.nickname,
        avatar: this.state.avatar,
        phone: this.state.phone,
        email: this.state.email,
        isVerified: this.state.isVerified,
        realName: this.state.realName,
        status: this.state.status,
        loginTime: this.state.loginTime,
        permissions: this.state.permissions,
        roles: this.state.roles,

        // 保存API返回的所有原始字段，确保数据完整性
        ...userInfo,

        // 特别处理关键字段
        detect_auth_result: userInfo.detect_auth_result || this.state.detect_auth_result,
        wechat_openid: userInfo.wechat_openid || this.state.wechat_openid,
        wechat_nickname: userInfo.wechat_nickname || this.state.wechat_nickname,
        wechat_avatar_url: userInfo.wechat_avatar_url || this.state.wechat_avatar_url
      };

      uni.setStorageSync('userInfo', userInfoToStore);

      // 同时存储关键字段到独立的存储项，保持向后兼容
      if (userInfo.detect_auth_result !== undefined) {
        uni.setStorageSync('detect_auth_result', userInfo.detect_auth_result);
      }
      if (userInfo.wechat_openid) {
        uni.setStorageSync('wechat_openid', userInfo.wechat_openid);
      }

      if (isDebug()) {
        console.log('用户信息已更新:', this.state);
        console.log('存储的用户信息:', userInfoToStore);
      }
    } catch (error) {
      console.error('设置用户信息失败:', error);
    }
  }

  /**
   * 更新用户信息
   */
  updateUserInfo(updates) {
    try {
      Object.assign(this.state, updates);
      this.updateLastActiveTime();
      
      // 更新本地存储
      const currentUserInfo = uni.getStorageSync('userInfo') || {};
      const updatedUserInfo = { ...currentUserInfo, ...updates };
      uni.setStorageSync('userInfo', updatedUserInfo);
      
      if (isDebug()) {
        console.log('用户信息已更新:', updates);
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
    }
  }

  /**
   * 设置微信用户信息
   */
  setWechatUserInfo(wechatInfo) {
    try {
      this.state.wechatInfo = wechatInfo;
      
      // 同步部分信息到主用户信息
      if (!this.state.avatar && wechatInfo.avatarUrl) {
        this.state.avatar = wechatInfo.avatarUrl;
      }
      if (!this.state.nickname && wechatInfo.nickName) {
        this.state.nickname = wechatInfo.nickName;
      }
      
      // 保存到本地存储
      uni.setStorageSync('wechat_userInfo', wechatInfo);
      
      if (isDebug()) {
        console.log('微信用户信息已设置:', wechatInfo);
      }
    } catch (error) {
      console.error('设置微信用户信息失败:', error);
    }
  }

  /**
   * 清除用户信息
   */
  clearUserInfo() {
    try {
      // 重置状态
      Object.assign(this.state, defaultUserData);
      this.state.isLoading = false;
      this.state.isInitialized = true;

      // 清除本地存储 - 包含所有相关的存储项
      uni.removeStorageSync('userInfo');
      uni.removeStorageSync('wechat_userInfo');
      uni.removeStorageSync('openid');
      uni.removeStorageSync('unionid');
      uni.removeStorageSync('wechat_openid');
      uni.removeStorageSync('detect_auth_result');
      uni.removeStorageSync('token');

      if (isDebug()) {
        console.log('用户信息已清除');
      }
    } catch (error) {
      console.error('清除用户信息失败:', error);
    }
  }

  /**
   * 更新最后活跃时间
   */
  updateLastActiveTime() {
    this.state.lastActiveTime = new Date().toISOString();
  }

  /**
   * 用户登出
   */
  /* async logout() {
    try {
      await logout();
      this.clearUserInfo();
      
      // 跳转到首页或登录页
      uni.switchTab({
        url: '/pages/index/index'
      });
      
      uni.showToast({
        title: '已退出登录',
        icon: 'success'
      });
    } catch (error) {
      console.error('退出登录失败:', error);
      uni.showToast({
        title: '退出登录失败',
        icon: 'none'
      });
    }
  } */

  /**
   * 检查用户权限
   */
  hasPermission(permission) {
    return this.state.permissions.includes(permission);
  }

  /**
   * 检查用户角色
   */
  hasRole(role) {
    return this.state.roles.includes(role);
  }

  /**
   * 获取用户状态
   */
  getUserState() {
    return this.state;
  }
}

// 创建用户状态管理实例
const userStore = new UserStore();

// 计算属性
export const userComputed = {
  // 是否已登录
  isLoggedIn: computed(() => userStore.state.status !== USER_STATUS.GUEST),
  
  // 是否已实名认证
  isVerified: computed(() => userStore.state.isVerified),
  
  // 用户显示名称
  displayName: computed(() => {
    return userStore.state.realName || 
           userStore.state.nickname || 
           userStore.state.wechatNickname || 
           userStore.state.wechatInfo.nickName || 
           '用户';
  }),
  
  // 用户头像
  displayAvatar: computed(() => {
    return userStore.state.avatar || 
           userStore.state.wechatInfo.avatarUrl || 
           '/static/tabbar/mine.png';
  })
};

// 导出用户状态管理
export default userStore;
