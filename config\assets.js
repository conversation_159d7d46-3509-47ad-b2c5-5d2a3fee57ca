// 静态资源配置
export const ASSETS_CONFIG = {
  // 头像相关
  avatars: {
    // 默认头像
    default: '/static/default-avatar.svg',
    defaultPng: '/static/default-avatar.png',
    
    // 性别头像
    male: '/static/avatar-male.svg',
    female: '/static/avatar-female.svg',
    
    // 占位符头像（base64编码的SVG）
    placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGM0Y0RjYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjMwIiBmaWxsPSIjRDFENURCIi8+PC9zdmc+',
    
    // 加载中头像
    loading: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNFNUU3RUIiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjIwIiBmaWxsPSIjOUNBM0FGIiBvcGFjaXR5PSIwLjUiLz48L3N2Zz4='
  },
  
  // 图标相关
  icons: {
    // 登录页面图标
    wechat: '/static/icons/wechat.png',
    phone: '/static/icons/phone.png',
    
    // 功能图标
    camera: '/static/icons/camera.png',
    edit: '/static/icons/edit.png',
    success: '/static/icons/success.png',
    error: '/static/icons/error.png',
    warning: '/static/icons/warning.png',
    info: '/static/icons/info.png'
  },
  
  // Logo相关
  logos: {
    app: '/static/logo.png',
    appLarge: '/static/logo-large.png',
    splash: '/static/splash.png'
  },
  
  // 背景图片
  backgrounds: {
    login: '/static/bg/login-bg.jpg',
    default: '/static/bg/default-bg.jpg'
  }
};

// 资源URL生成器
export class AssetUrlGenerator {
  /**
   * 获取头像URL
   * @param {string} type 头像类型
   * @param {Object} options 选项
   * @returns {string} 头像URL
   */
  static getAvatarUrl(type = 'default', options = {}) {
    const { gender, fallback = true } = options;
    
    // 根据性别返回对应头像
    if (type === 'default' && gender !== undefined) {
      switch (parseInt(gender)) {
        case 0: // 男性
          return ASSETS_CONFIG.avatars.male;
        case 1: // 女性
          return ASSETS_CONFIG.avatars.female;
        default: // 保密或其他
          return ASSETS_CONFIG.avatars.default;
      }
    }
    
    // 获取指定类型的头像
    const avatarUrl = ASSETS_CONFIG.avatars[type];
    if (avatarUrl) {
      return avatarUrl;
    }
    
    // 如果找不到且允许回退，返回默认头像
    if (fallback) {
      return ASSETS_CONFIG.avatars.default;
    }
    
    return null;
  }
  
  /**
   * 获取图标URL
   * @param {string} name 图标名称
   * @returns {string} 图标URL
   */
  static getIconUrl(name) {
    return ASSETS_CONFIG.icons[name] || null;
  }
  
  /**
   * 获取Logo URL
   * @param {string} type Logo类型
   * @returns {string} Logo URL
   */
  static getLogoUrl(type = 'app') {
    return ASSETS_CONFIG.logos[type] || ASSETS_CONFIG.logos.app;
  }
  
  /**
   * 获取背景图片URL
   * @param {string} type 背景类型
   * @returns {string} 背景图片URL
   */
  static getBackgroundUrl(type = 'default') {
    return ASSETS_CONFIG.backgrounds[type] || ASSETS_CONFIG.backgrounds.default;
  }
  
  /**
   * 检查资源是否存在（简单的URL格式检查）
   * @param {string} url 资源URL
   * @returns {boolean} 是否为有效URL
   */
  static isValidUrl(url) {
    if (!url || typeof url !== 'string') {
      return false;
    }
    
    // 检查是否为有效的URL格式
    return url.startsWith('/') || 
           url.startsWith('http://') || 
           url.startsWith('https://') || 
           url.startsWith('data:') ||
           url.startsWith('wxfile://');
  }
  
  /**
   * 添加时间戳防止缓存
   * @param {string} url 原始URL
   * @returns {string} 带时间戳的URL
   */
  static addTimestamp(url) {
    if (!this.isValidUrl(url)) {
      return url;
    }
    
    // 对于data URL和本地文件，不添加时间戳
    if (url.startsWith('data:') || url.startsWith('wxfile://')) {
      return url;
    }
    
    const separator = url.includes('?') ? '&' : '?';
    return `${url}${separator}t=${Date.now()}`;
  }
}

// 资源预加载器
export class AssetPreloader {
  constructor() {
    this.loadedAssets = new Set();
    this.failedAssets = new Set();
  }
  
  /**
   * 预加载图片资源
   * @param {Array<string>} urls 图片URL数组
   * @returns {Promise<Object>} 加载结果
   */
  async preloadImages(urls) {
    if (!Array.isArray(urls)) {
      urls = [urls];
    }
    
    const results = {
      success: [],
      failed: [],
      total: urls.length
    };
    
    const loadPromises = urls.map(url => {
      return new Promise((resolve) => {
        if (this.loadedAssets.has(url)) {
          results.success.push(url);
          resolve();
          return;
        }
        
        if (this.failedAssets.has(url)) {
          results.failed.push(url);
          resolve();
          return;
        }
        
        // 使用uni.getImageInfo预加载图片
        uni.getImageInfo({
          src: url,
          success: () => {
            this.loadedAssets.add(url);
            results.success.push(url);
            resolve();
          },
          fail: () => {
            this.failedAssets.add(url);
            results.failed.push(url);
            resolve();
          }
        });
      });
    });
    
    await Promise.all(loadPromises);
    
    return {
      ...results,
      successRate: results.success.length / results.total
    };
  }
  
  /**
   * 预加载关键资源
   * @returns {Promise<Object>} 加载结果
   */
  async preloadCriticalAssets() {
    const criticalAssets = [
      ASSETS_CONFIG.avatars.default,
      ASSETS_CONFIG.avatars.defaultPng,
      ASSETS_CONFIG.logos.app,
      ASSETS_CONFIG.icons.wechat
    ].filter(url => url && !url.startsWith('data:'));
    
    return await this.preloadImages(criticalAssets);
  }
  
  /**
   * 清除预加载缓存
   */
  clearCache() {
    this.loadedAssets.clear();
    this.failedAssets.clear();
  }
}

// 创建全局实例
export const assetPreloader = new AssetPreloader();

// 便捷方法
export const getAvatarUrl = AssetUrlGenerator.getAvatarUrl;
export const getIconUrl = AssetUrlGenerator.getIconUrl;
export const getLogoUrl = AssetUrlGenerator.getLogoUrl;
export const getBackgroundUrl = AssetUrlGenerator.getBackgroundUrl;
