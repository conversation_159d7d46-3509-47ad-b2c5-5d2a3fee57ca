<template>
  <view class="mediation-query-container">
    <!-- 未认证状态 - 身份信息查询 -->
    <view class="auth-required" v-if="!isAuthenticated">
      <view class="form-container">
        <view class="form-card">
          <view class="card-header">
            <text class="card-title">姓名</text>
          </view>
          <view class="card-content">
            <uni-easyinput
              v-model="formData.name"
              placeholder="请输入真实姓名"
              class="input-field"
            >
            </uni-easyinput>
          </view>
        </view>
        <view class="form-card">
          <view class="card-header">
            <text class="card-title">身份证号</text>
          </view>
          <view class="card-content">
            <uni-easyinput
              v-model="formData.card"
              placeholder="请输入身份证号"
              class="input-field"
            >
            </uni-easyinput>
          </view>
        </view>
      </view>
      <view class="help-text">
        <i class="fas fa-info-circle"></i>仅显示案件数量统计
      </view>
      <button class="auth-button" @click="handleSearch">查询案件</button>
      
      <!-- 身份信息查询结果显示 -->
      <view v-if="mediationCase !== '' && (formData.name || formData.card)">
        <view class="query-result-card" @click="navigateToAuth">
          <view class="query-result-icon">
            <i class="fas fa-search"></i>
          </view>
          <view class="query-result-text">{{mediationCase}}</view>
        </view>
        <view class="query-tip">
          <view>需要查看详细信息？<text href="#" class="certification" @click="navigateToAuth">去认证</text></view>
        </view>
      </view>
    </view>
    <!-- 已认证状态 - 案件列表 -->
    <view v-if="isAuthenticated">
      <!-- 搜索框 -->
      <view class="search-box">
        <uni-easyinput
          class="search-input-wrapper"
          v-model="searchKeyword"
          placeholder="请输入调解案件号进行查询"
          confirmType="search"
          suffixIcon="search"
          :clearable="true"
          @iconClick="handleSearch"
        ></uni-easyinput>
      </view>

      <!-- 工单列表 -->
      <view class="order-list" v-if="filteredOrderList.length > 0">
        <view
          class="order-item"
          v-for="order in filteredOrderList"
          :key="order.id"
          @click="navigateToDetail(order)"
          hover-class="order-item-hover"
          :class="{ 'order-item-animation': isLoadingAnimate }"
        >
          <view class="order-info">
            <view class="order-id">调解案件号: <text class="id-text">{{ order.case_number }}</text></view>
            <view
              class="status-label"
              :style="{
                backgroundColor:statusStyles[order.case_status]?.bgColor,
                color: '#fff',
              }"
              >{{ order.case_status_cn }}</view>
          </view>
          <view class="order-date-icon">
            <view class="order-date">发起日期: {{ order.initiate_date }}</view>
            <view class="arrow-icon">›</view>
          </view>
        </view>
      </view>

      <!-- 加载中状态 -->
      <view class="loading-state" v-if="isLoading && !isRefreshing">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredOrderList.length === 0 && !isLoading">
        <!-- <image
          class="empty-image"
          src="/static/icons/empty.png"
          mode="aspectFit"
        ></image> -->
        <text class="empty-text">{{
          searchKeyword ? "未找到相关调解案件号" : "暂无信息"
        }}</text>
        <button class="refresh-button" @click="handleRefresh">刷新</button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { onPullDownRefresh, onShow } from "@dcloudio/uni-app";
import { api } from "@/utils/api.js";
import userStore from "@/utils/user-store.js";

// 搜索关键词
const searchKeyword = ref("");
// 是否正在刷新
const isRefreshing = ref(false);
// 是否正在加载
const isLoading = ref(false);
// 加载动画效果
const isLoadingAnimate = ref(false);
// 用户认证状态
const isAuthenticated = ref(false);
// 调解案件数量
const mediationCase = ref('');
// 工单列表
const filteredOrderList = ref([]);

// 表单数据
const formData = reactive({
  name: '',           // 姓名
  card: ''            // 身份证号
});

// 状态样式配置draft待发起、initiated已发起、pending_confirm待确认、in_progress进行中、completed已完成、closed已关闭
const statusStyles = {
  draft: { bgColor: "#faad14" },
  pending_confirm: { bgColor: "#faad14" },
  initiated: { bgColor: "#1890ff" },
  in_progress: { bgColor: "#1890ff" },
  completed: { bgColor: "#52c41a" },
  closed: { bgColor: "#999" },
};

// 根据搜索关键词过滤的工单列表
// const filteredOrderList = computed(() => {
//   if (!searchKeyword.value) {
//     return orderList.value;
//   }
//   const keyword = searchKeyword.value.toLowerCase();
//   return orderList.value.filter((order) => {
//     // 支持多字段搜索：案件号、状态
//     const caseNumber = (order.case_number || order.id || '').toLowerCase();
//     const status = (order.case_status_cn || order.status || '').toLowerCase();
//     return caseNumber.includes(keyword) || status.includes(keyword);
//   });
// });

// 生命周期钩子
onMounted(() => {
  console.log("调解查询页面已加载");
  checkAuthStatus();
  setTimeout(() => {
    isLoadingAnimate.value = true;
  }, 200);
});

// 页面显示时触发（包括从其他页面返回）
onShow(() => {
  console.log("调解查询页面显示");
  // 只检查认证状态，不重复加载数据
  const detectAuthResult = uni.getStorageSync("detect_auth_result");
  isAuthenticated.value = detectAuthResult === true;
  console.log("用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
});

// 检查用户认证状态
const checkAuthStatus = async () => {
  const detectAuthResult = uni.getStorageSync("detect_auth_result");

  // 如果 detectAuthResult 为 false，需要调用 API 获取最新用户信息
  if (detectAuthResult === false) {
    console.log("检测到 detect_auth_result 为 false，正在获取最新用户信息...");

    // 调用 /user/user_info/ API 获取最新用户信息
    const result = await api.user.getUserInfo();

    if (result && result.success && result.data) {
      const userData = result.data;

      // 使用 userStore 更新用户信息，确保数据同步
      const updatedUserInfo = {
        ...userData,
        // 确保 isVerified 字段被正确更新
        isVerified: userData.isVerified || userData.is_verified || false
      };

      // 通过 userStore 更新用户信息，这会自动处理本地缓存
      userStore.updateUserInfo(updatedUserInfo);

      // 更新 detect_auth_result 状态（如果服务器返回了该字段）
      if (typeof userData.detect_auth_result !== 'undefined') {
        uni.setStorageSync('detect_auth_result', userData.detect_auth_result);
        isAuthenticated.value = userData.detect_auth_result === true;
      } else {
        isAuthenticated.value = detectAuthResult === true;
      }

      console.log("用户信息已更新:", updatedUserInfo);
      console.log("用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
    } else {
      // API 调用失败，使用原有逻辑
      isAuthenticated.value = detectAuthResult === true;
      console.log("获取用户信息失败，使用缓存状态:", isAuthenticated.value ? "已认证" : "未认证");
    }
  } else {
    // detectAuthResult 不为 false 时，使用原有逻辑
    isAuthenticated.value = detectAuthResult === true;
    console.log("用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
  }

  // 如果已认证，加载案件列表
  if (isAuthenticated.value) {
    fetchOrderList();
  }
};

// 下拉刷新处理
onPullDownRefresh(() => {
  console.log("触发下拉刷新");
  isRefreshing.value = true;
  checkAuthStatus();
});

// 获取工单列表 - 已认证用户
const fetchOrderList = async (caseNumber = '') => {
  isLoading.value = true;

  if (!isRefreshing.value) {
    uni.showLoading({ title: "加载中..." });
  }

  try {
    const params = {};
    if (caseNumber) {
      params.mediation_case_number = caseNumber;
    }

    const result = await api.mediationQuery.getAuthenticatedList(params);

    if (!isRefreshing.value) {
      uni.hideLoading();
    }

    if (result.state == 'success') {
      console.log(result.data,'=====data')
      // 恢复数据映射逻辑，确保数据结构与模板匹配
      filteredOrderList.value = result.data
      /* .map(item => ({
        id: item.case_number,
        case_number: item.case_number,
        case_status_cn: item.case_status_cn,
        case_status: item.case_status,
        initiate_date: item.initiate_date,
        rawData: item
      })); */
    } else {
      filteredOrderList.value = [];
      // 只有在有错误消息时才显示toast
      if (result && result.msg) {
        console.log(result.msg,'====result.msg')
        uni.showToast({
          title: result.msg,
          icon: "none",
          duration: 2000,
        });
      }
    }

    isLoading.value = false;

    if (isRefreshing.value) {
      uni.stopPullDownRefresh();
      isRefreshing.value = false;
      uni.showToast({
        title: "刷新成功",
        icon: "success",
        duration: 1500,
      });
    }

  } catch (error) {
    if (!isRefreshing.value) {
      uni.hideLoading();
    }

    console.error("获取案件列表失败:", error);
    orderList.value = [];
    isLoading.value = false;

    if (isRefreshing.value) {
      uni.stopPullDownRefresh();
      isRefreshing.value = false;
    }

    // 显示错误信息
    uni.showToast({
      title: error.msg || "获取数据失败",
      icon: "none",
      duration: 2000,
    });
  }
};

// 处理搜索
const handleSearch = async () => {
  if (isAuthenticated.value) {
    // 已认证用户 - 搜索案件列表
    await fetchOrderList(searchKeyword.value.trim());
  } else {
    // 未认证用户 - 身份信息查询
    await handleIdentitySearch();
  }
};

// 处理身份信息查询 - 未认证用户
const handleIdentitySearch = async () => {
  const name = formData.name.trim();
  const idCard = formData.card.trim();

  if (!name || !idCard) {
    uni.showToast({
      title: "请输入完整的姓名和身份证号",
      icon: "none",
      duration: 1500,
    });
    return;
  }

  uni.showLoading({ title: "查询中..." });

  const result = await api.mediationQuery.getCaseCountByIdentity({
    name: name,
    id_card: idCard
  });

  uni.hideLoading();

  if (result.state == "success") {
    const count = result.data;
    mediationCase.value = count;
  } else {
    mediationCase.value = null;
    uni.showToast({
      title: result.msg,
      icon: "none",
      duration: 2000,
    });
  }
};

// 跳转到认证页面 - 未认证用户点击查询结果
const navigateToAuth = () => {
  const name = formData.name.trim();
  const idCard = formData.card.trim();
  
  uni.navigateTo({
    url: `/pages/auth/auth?name=${encodeURIComponent(name)}&idCard=${encodeURIComponent(idCard)}`,
    success: () => {
      console.log("跳转到认证页面");
    },
    fail: (err) => {
      console.error("跳转失败", err);
      uni.showToast({
        title: "跳转失败",
        icon: "none",
      });
    },
  });
};

// 手动刷新
const handleRefresh = () => {
  if (isAuthenticated.value) {
    fetchOrderList();
  }
};

// 工具函数：根据案件状态中文获取状态码
const getStatusCode = (statusCn) => {
  const statusMap = {
    '待确认': 'pending',
    '进行中': 'processing', 
    '已完成': 'completed',
    '已关闭': 'closed',
    '待处理': 'pending',
    '处理中': 'processing',
    '已结案': 'completed',
    '已撤销': 'closed'
  };
  return statusMap[statusCn] || 'pending';
};

// 工具函数：格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 跳转到工单详情页 - 已认证用户
const navigateToDetail = (order) => {
  console.log('跳转到详情页，订单信息:', order);

  // 从 order 对象中提取所需参数
  const caseNumber = order.case_number; // case_number (调解案件号)
  const orderStatus = order.case_status_cn; // case_status_cn (案件状态中文)
  const initiateDate = order.initiate_date; // initiate_date (发起日期)
  const closeDate = order.close_date; // initiate_date (关闭日期)
  const mediationProgress = order.mediation_progress; // mediation_progress (步骤)

  // 手动构建 URL 参数字符串（兼容小程序环境）
  const buildUrlParams = (paramsObj) => {
    const params = [];
    for (const [key, value] of Object.entries(paramsObj)) {
      if (value !== null && value !== undefined && value !== '') {
        params.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }
    return params.join('&');
  };

  const paramsString = buildUrlParams({
    case_number: caseNumber,
    initiate_date: initiateDate,
    close_date: closeDate,
    case_status_cn: orderStatus,
    mediation_progress: mediationProgress
  });

  console.log('跳转参数:', {
    case_number: caseNumber,
    initiate_date: initiateDate,
    case_status_cn: orderStatus,
    mediation_progress: mediationProgress
  });
  // 如果mediation_progress进度状态=调解确认、方案确认、协议签署、完成、已关闭、未知状态
  if(mediationProgress === "调解确认"){
    uni.navigateTo({
      url: `/pages/work_order_detail/work_order_detail?${paramsString}`,
    });
  }else if(mediationProgress === "方案确认"){
    uni.navigateTo({
      url: `/pages/solution_confirm/solution_confirm?${paramsString}`,
    });
  }else if(mediationProgress === "协议签署"){
    uni.navigateTo({
      url: `/pages/agreement_signing/agreement_signing?${paramsString}`,
    });
  }else if(mediationProgress === "完成"){
    uni.navigateTo({
      url: `/pages/case_completed/case_completed?${paramsString}`,
    });
  }else if(mediationProgress === "已关闭"){
    uni.navigateTo({
      url: `/pages/work_order_detail/work_order_detail?${paramsString}`,
    });
  }else{
    uni.showToast({
      title: "跳转失败",
      icon: "none",
    });
  }
};
</script>

<style lang="scss" scoped>
:root {
	--primary-color: #3b7eeb;      // 主题蓝色
	--primary-light: #e6f0ff;      // 浅蓝色背景
	--success-color: #52c41a;
	--text-color:#333;
}
.mediation-query-container {
  min-height: 100vh;
  // height: calc(100% - 94px);
  overflow-y: auto;
  background-color: rgb(248, 250, 252);
  padding: 30rpx 30rpx 140rpx;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  flex: 1;
}

/* 调整uni-easyinput的样式 */
:deep(.uni-easyinput__content) {
  height: 80rpx;
  background-color: #fff;
  border: 2rpx solid #eee;
  border-radius: 40rpx;
  padding: 0 20rpx;
}

:deep(.uni-icons) {
  color: #999;
}

:deep(.uni-easyinput__placeholder-class) {
  font-size: 28rpx;
}

:deep(.uni-easyinput__content-input) {
  height: 80rpx;
  font-size: 28rpx;
}

/* 未认证状态提示样式 */
.auth-required {
  // flex-direction: column;
  // align-items: center;
  // justify-content: center;
  // padding: 100rpx 50rpx;
  .filter-tabs {
    display: flex;
    text-align: center;
    // gap: 20rpx;
    overflow-x: auto;

    .type-card {
      // padding: 15rpx 30rpx;
      // background-color: #f8f8f8;
      // color: #666;
      // border-radius: 30rpx;
      // font-size: 26rpx;
      // white-space: nowrap;
      // transition: all 0.3s ease;

      background-color: #fff;
      color: #666;
      position: relative;
      flex: 1 1 0%;
      border-width: 2rpx;
      border-style: solid;
      border-color: #fff;
      box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;
      border-image: initial;
      font-size: 28rpx;
      padding: 16rpx 32rpx;
      border-radius: 12rpx;
    }

    .type-card.active {
      background-color: #2979ff;
      color: #ffffff;
    }
  }
	--card-background: #ffffff;    // 卡片背景色

	.form-container {
		display: flex;
		flex-direction: column;
		.form-card {
			overflow: hidden;
			.card-header {
			padding: 30rpx 30rpx 20rpx 10rpx;
			// border-bottom: 1rpx solid #f0f0f0;
			display: flex;
			align-items: center;

			.fas {
				margin-right: 8px;
				color: var(--primary-color);
			}

			.card-title {
				font-size: 30rpx;
				color: var(--text-primary);
				font-weight: 800;
				// flex: 1;
			}
			}
		}
	}
	.query-result-card {
		background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
		border: 2rpx solid #b7eb8f;
		border-radius: 24rpx;
		padding: 40rpx;
		margin-top: 40rpx;
		text-align: center;
		box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.1);
		animation: slideInUp 0.3s ease;
	}
  .query-tip{
    text-align: center; margin-top: 20px; font-size: 13px; color: var(--text-secondary);
  }
  .certification{
    color: var(--primary-color); text-decoration: none;
  }
	.query-result-icon {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%);
		display: flex	;
		align-items: center;
		justify-content: center;
		margin: 0 auto 32rpx;
		color: white;
		font-size: 40rpx;
	}
	.query-result-text {
		font-size: 32rpx;
		font-weight: 600;
		color: var(--text-color);
		margin-bottom: 16rpx;
	}
	.help-text{
		background-color: var(--primary-light);
		font-size: 26rpx;
		color: var(--primary-color);
		display: flex;
		align-items: center;
		border-width: 2rpx;
		border-style: solid;
		border-color: rgba(59, 126, 235, 0.2);
		border-image: initial;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		margin: 32rpx 0;
		gap: 16rpx;
		.fas {
			margin-right: 10rpx;
			padding-top: 4rpx;
		}
	}
	.auth-button {
		width: 690rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #2979ff;
		color: #fff;
		font-size: 32rpx;
		border-radius: 10rpx;
		margin-top: 40rpx;
	}
}
:deep(.uni-easyinput__content) {
	border-radius: 12rpx !important;
}
.order-list {
  margin-bottom: 20rpx;
}

.order-item {
  margin-bottom: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: block;
  unicode-bidi: isolate
}

.order-item-hover {
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}

.order-item-animation {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.order-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.order-id {
  font-size: 29rpx;
  color: #333;
  font-weight: 500;
}

.order-date {
  font-size: 26rpx;
  color: #666;
}

.order-date-icon {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-label {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 22rpx;
  white-space: nowrap;
}

.arrow-icon {
  margin-left: 10rpx;
  font-size: 50rpx;
  color: #666;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #2979ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.refresh-button {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  color: #2979ff;
  background-color: #fff;
  border: 2rpx solid #2979ff;
  border-radius: 35rpx;
}
</style>
