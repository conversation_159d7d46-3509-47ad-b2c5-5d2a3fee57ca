"use strict";
const common_vendor = require("../../common/vendor.js");
require("../../config/env.js");
require("../../utils/user-store.js");
if (!Array) {
  const _easycom_uni_easyinput2 = common_vendor.resolveComponent("uni-easyinput");
  _easycom_uni_easyinput2();
}
const _easycom_uni_easyinput = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.js";
if (!Math) {
  _easycom_uni_easyinput();
}
const _sfc_main = {
  __name: "contact_information",
  setup(__props) {
    const formData = common_vendor.reactive({
      phoneNumber: "18698990903",
      // 手机号码
      verificationCode: "557722"
      // 验证码
    });
    const countdown = common_vendor.ref(0);
    const isSubmitting = common_vendor.ref(false);
    const getVerificationCode = () => {
      common_vendor.index.__f__("log", "at pages/contact_information/contact_information.vue:106", formData.phoneNumber.value, "=====", formData.phoneNumber);
      if (!formData.phoneNumber) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return;
      }
      if (!/^1[3-9]\d{9}$/.test(formData.phoneNumber)) {
        common_vendor.index.showToast({
          title: "请输入正确的手机号码",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/contact_information/contact_information.vue:125", "调用发送验证码API，手机号:", formData.phoneNumber);
      countdown.value = 60;
      const timer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          clearInterval(timer);
        }
      }, 1e3);
      setTimeout(() => {
        common_vendor.index.__f__("log", "at pages/contact_information/contact_information.vue:138", "验证码已发送");
      }, 1e3);
    };
    const confirmContact = () => {
      if (!formData.phoneNumber) {
        common_vendor.index.showToast({
          title: "请输入手机号码",
          icon: "none"
        });
        return;
      }
      if (!formData.verificationCode) {
        common_vendor.index.showToast({
          title: "请输入验证码",
          icon: "none"
        });
        return;
      }
      if (formData.verificationCode.length !== 6) {
        common_vendor.index.showToast({
          title: "请输入6位验证码",
          icon: "none"
        });
        return;
      }
      isSubmitting.value = true;
      common_vendor.index.__f__("log", "at pages/contact_information/contact_information.vue:173", "调用确认联系方式API", {
        phone: formData.phoneNumber,
        code: formData.verificationCode
      });
      isSubmitting.value = false;
      common_vendor.index.showToast({
        title: "联系方式确认成功！",
        icon: "none"
      });
      common_vendor.index.navigateTo({
        url: "/pages/case_completed/case_completed",
        success: () => {
          common_vendor.index.__f__("log", "at pages/contact_information/contact_information.vue:191", "页面跳转成功");
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at pages/contact_information/contact_information.vue:194", "页面跳转失败：", error);
          common_vendor.index.showToast({
            title: "页面跳转失败，请重试",
            icon: "none"
          });
        }
      });
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/contact_information/contact_information.vue:204", "调解查询页面已加载");
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(($event) => formData.phoneNumber = $event),
        b: common_vendor.p({
          placeholder: "请输入手机号",
          modelValue: formData.phoneNumber
        }),
        c: common_vendor.o(($event) => formData.verificationCode = $event),
        d: common_vendor.p({
          placeholder: "请输入验证码",
          modelValue: formData.verificationCode
        }),
        e: common_vendor.t(countdown.value > 0 ? `${countdown.value}s后重新获取` : "获取验证码"),
        f: common_vendor.o(getVerificationCode),
        g: countdown.value > 0,
        h: common_vendor.n(countdown.value > 0 ? "bg-gray-200 text-gray-500" : "bg-blue-100 text-blue-600 hover:bg-blue-200"),
        i: common_vendor.o(confirmContact)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ad806119"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/contact_information/contact_information.js.map
