{"version": 3, "file": "mediation_complaint.js", "sources": ["pages/mediation_complaint/mediation_complaint.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbWVkaWF0aW9uX2NvbXBsYWludC9tZWRpYXRpb25fY29tcGxhaW50LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"mediation-complaint-container\">\r\n\t\t<web-view src=\"https://prod-9gwr0pqvc081f7f4-1370735801.tcloudbaseapp.com/\"></web-view>\r\n\t\t<!-- <web-view src=\"https://postpay-2g5hm2oxbbb721a4-1258211818.tcloudbaseapp.com/jump-mp.html\"></web-view> -->\r\n\t\t<view class=\"gradient-bg\">\r\n\t\t\t<view class=\"handle-box\">\r\n\t\t\t\t<view class=\"handle-icon\">\r\n\t\t\t\t\t<i class=\"fas fa-comments\"></i>\r\n\t\t\t\t</view>\r\n\t\t\t\t<h2 class=\"handle-title\">意见反馈与服务投诉</h2>\r\n\t\t\t\t<p class=\"handle-text\">您的意见是我们改进服务的动力，请告诉我们您的想法</p>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 反馈类型选择区域 -->\r\n\t\t<view class=\"feedback-types\">\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<!-- <text class=\"card-icon\">📋</text> -->\r\n\t\t\t\t\t<i class=\"fas fa-list-ul\"></i>\r\n\t\t\t\t\t<text class=\"card-title\">反馈类型</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"type-card-content\">\r\n\t\t\t\t\t<!-- 意见建议按钮 -->\r\n\t\t\t\t\t<view class=\"type-card\" \r\n\t\t\t\t\t\t:class=\"{ active: selectedType === 0 }\" \r\n\t\t\t\t\t\t@click=\"selectType(0)\">\r\n\t\t\t\t\t\t<view class=\"feedback-type-icon suggestion-icon\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-lightbulb\"></i>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"type-text\">意见建议</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 服务投诉按钮 -->\r\n\t\t\t\t\t<view class=\"type-card\" \r\n\t\t\t\t\t\t:class=\"{ active: selectedType === 1 }\" \r\n\t\t\t\t\t\t@click=\"selectType(1)\">\r\n\t\t\t\t\t\t<view class=\"feedback-type-icon complaint-icon\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-exclamation-triangle\"></i>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"type-text\">服务投诉</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 表单区域 - 每个区域都是独立卡片 -->\r\n\t\t<view class=\"form-container\" v-if=\"selectedType !== null\">\r\n\t\t\t\r\n\t\t\t<!-- 关联案件信息卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<!-- <text class=\"card-icon\">📋</text> -->\r\n\t\t\t\t\t<i class=\"fas fa-file-alt\"></i>\r\n\t\t\t\t\t<text class=\"card-title\">关联案件信息</text>\r\n\t\t\t\t\t<text class=\"optional-tag\">（可选）</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<uni-easyinput \r\n\t\t\t\t\t    prefixIcon=\"search\"\r\n\t\t\t\t\t\tv-model=\"formData.caseNumber\"\r\n\t\t\t\t\t\tplaceholder=\"请输入调解案件编号\"\r\n\t\t\t\t\t\tclass=\"input-field\">\r\n\t\t\t\t\t</uni-easyinput>\r\n\t\t\t\t\t<view class=\"help-text\">\r\n\t\t\t\t\t\t<i class=\"fas fa-info-circle\"></i>填写案件编号有助于我们更快定位和处理您的反馈\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 具体类别卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<!-- <text class=\"card-icon\">🏷️</text> -->\r\n\t\t\t\t\t<i class=\"fas fa-tags\"></i>\r\n\t\t\t\t\t<text class=\"card-title\">具体类别</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"category-grid\">\r\n\t\t\t\t\t\t<view class=\"category-card\" \r\n\t\t\t\t\t\t\tv-for=\"(category, index) in currentCategories\" \r\n\t\t\t\t\t\t\t:key=\"category.value\"\r\n\t\t\t\t\t\t\t:class=\"{ active: selectedCategory === category.value }\"\r\n\t\t\t\t\t\t\t@click=\"selectCategory(category)\">\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 左侧图标 -->\r\n\t\t\t\t\t\t\t<view class=\"category-icon\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas\" :class=\"category.icon\" :style=\"{ color: category.color }\"></i>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 中间文字区域 -->\r\n\t\t\t\t\t\t\t<view class=\"category-info\">\r\n\t\t\t\t\t\t\t\t<text class=\"category-title\">{{ category.text }}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"category-desc\">{{ category.desc }}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t<!-- 右侧单选框 -->\r\n\t\t\t\t\t\t\t<view class=\"category-radio\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-inner\" :class=\"{ checked: selectedCategory === category.value }\">\r\n\t\t\t\t\t\t\t\t\t<text class=\"radio-dot\" v-if=\"selectedCategory === category.value\">●</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 详细描述卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<!-- <text class=\"card-icon\">✏️</text> -->\r\n\t\t\t\t\t<i class=\"fas fa-edit\"></i>\r\n\t\t\t\t\t<text class=\"card-title\">详细描述</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<view class=\"textarea-container\">\r\n\t\t\t\t\t\t<uni-easyinput \r\n\t\t\t\t\t\t\ttype=\"textarea\" \r\n\t\t\t\t\t\t\tv-model=\"formData.description\"\r\n\t\t\t\t\t\t\t:placeholder=\"currentPlaceholder\"\r\n\t\t\t\t\t\t\tautoHeight\r\n\t\t\t\t\t\t\tclass=\"textarea-field\">\r\n\t\t\t\t\t\t</uni-easyinput>\r\n\t\t\t\t\t\t<text class=\"char-count\">{{ formData.description.length }}/500</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"help-text\">\r\n\t\t\t\t\t\t<i class=\"fas fa-info-circle\"></i>详细的描述有助于我们更好地理解和解决问题\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 联系方式卡片 -->\r\n\t\t\t<view class=\"form-card\">\r\n\t\t\t\t<view class=\"card-header\">\r\n\t\t\t\t\t<!-- <text class=\"card-icon\">📱</text> -->\r\n\t\t\t\t\t<i class=\"fas fa-phone\"></i>\r\n\t\t\t\t\t<text class=\"card-title\">联系方式</text>\r\n\t\t\t\t\t<text class=\"optional-tag\">（可选）</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"card-content\">\r\n\t\t\t\t\t<uni-easyinput \r\n\t\t\t\t\t    prefixIcon=\"phone\"\r\n\t\t\t\t\t\tv-model=\"formData.contactPhone\"\r\n\t\t\t\t\t\tplaceholder=\"请输入您的手机号码\"\r\n\t\t\t\t\t\ttype=\"number\"\r\n\t\t\t\t\t\tmaxlength=\"11\"\r\n\t\t\t\t\t\tclass=\"input-field\">\r\n\t\t\t\t\t</uni-easyinput>\r\n\t\t\t\t\t<view class=\"help-text\">\r\n\t\t\t\t\t\t<i class=\"fas fa-shield-alt\"></i>您的联系方式仅用于发送调解相关信息，我们承诺严格保护您的隐私\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 提交按钮卡片 -->\r\n\t\t\t<button class=\"submit-btn\" @click=\"handleSubmit\">\r\n\t\t\t\t<!-- <text class=\"submit-icon\">📤</text> -->\r\n\t\t\t\t<i class=\"fas fa-paper-plane\"></i>\r\n\t\t\t\t<text class=\"submit-text\">提交反馈</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted } from 'vue';\r\n// Tab状态管理（当前只有一个反馈类型tab）\r\nconst activeTab = ref(0)\r\n\r\n// 选中的反馈类型（0: 意见建议, 1: 服务投诉）\r\nconst selectedType = ref(0)\r\n\r\n// 选中的具体类别\r\nconst selectedCategory = ref('')\r\n\r\n// 表单数据\r\nconst formData = reactive({\r\n\tcaseNumber: '',      // 关联案件编号\r\n\tdescription: '',     // 详细描述\r\n\tcontactPhone: ''     // 联系电话\r\n})\r\n\r\n// ==================== 配置数据 ====================\r\n\r\n// 类别配置：不同反馈类型对应不同的具体类别选项，包含图标和颜色\r\nconst categoryConfig = {\r\n\tsuggestion: [\r\n\t\t{ \r\n\t\t\tvalue: 'process_optimization', \r\n\t\t\ttext: '流程优化建议', \r\n\t\t\tdesc: '对调解流程改进的建议', \r\n\t\t\ticon: 'fa-cog', \r\n\t\t\tcolor: '#52c41a' \r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'improvement', \r\n\t\t\ttext: '功能改进建议', \r\n\t\t\tdesc: '对系统功能的优化建议', \r\n\t\t\ticon: 'fa-mobile-alt', \r\n\t\t\tcolor: '#1890ff' \r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'service_praise', \r\n\t\t\ttext: '服务表扬', \r\n\t\t\tdesc: '对优质服务的表扬', \r\n\t\t\ticon: 'fa-heart', \r\n\t\t\tcolor: '#eb2f96'\r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'other_suggestion', \r\n\t\t\ttext: '其他建议', \r\n\t\t\tdesc: '其他类型的意见建议', \r\n\t\t\ticon: 'fa-plus-circle', \r\n\t\t\tcolor: '#999'\r\n\t\t}\r\n\t],\r\n\tcomplaint: [\r\n\t\t{ \r\n\t\t\tvalue: 'service_attitude', \r\n\t\t\ttext: '服务态度问题', \r\n\t\t\tdesc: '工作人员态度不佳', \r\n\t\t\ticon: 'fa-user-times', \r\n\t\t\tcolor: '#f5222d',\r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'processing_time', \r\n\t\t\ttext: '处理时间过长', \r\n\t\t\tdesc: '调解处理效率低下', \r\n\t\t\ticon: 'fa-clock',\r\n\t\t\tcolor: '#faad14',\r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'plan_unreasonable', \r\n\t\t\ttext: '方案不合理', \r\n\t\t\tdesc: '调解方案存在问题', \r\n\t\t\ticon: 'fa-ban', \r\n\t\t\tcolor: '#722ed1',\r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'system_technology', \r\n\t\t\ttext: '系统技术问题', \r\n\t\t\tdesc: '页面异常、功能故障等', \r\n\t\t\ticon: 'fa-bug', \r\n\t\t\tcolor: '#fa541c',\r\n\t\t},\r\n\t\t{ \r\n\t\t\tvalue: 'other_complaint', \r\n\t\t\ttext: '其他投诉', \r\n\t\t\tdesc: '其他类型的服务投诉', \r\n\t\t\ticon: 'fa-ellipsis-h', \r\n\t\t\tcolor: '#999',\r\n\t\t}\r\n\t]\r\n}\r\n\r\n// ==================== 计算属性 ====================\r\n\r\n// 根据当前选中的反馈类型返回对应的类别选项\r\nconst currentCategories = computed(() => {\r\n\tif (selectedType.value === 0) {\r\n\t\treturn categoryConfig.suggestion\r\n\t} else if (selectedType.value === 1) {\r\n\t\treturn categoryConfig.complaint\r\n\t}\r\n\treturn []\r\n})\r\n\r\n// 根据当前反馈类型返回对应的描述占位符\r\nconst currentPlaceholder = computed(() => {\r\n\tif (selectedType.value === 0) {\r\n\t\treturn '请详细描述您的意见或问题，我们会认真对待每一条反馈...'\r\n\t} else if (selectedType.value === 1) {\r\n\t\treturn '请详细描述您的投诉或问题，我们承诺对待每一条反馈...'\r\n\t}\r\n\treturn '请详细描述您的反馈内容...'\r\n})\r\n\r\n// ==================== 事件处理方法 ====================\r\n\r\n// Tab切换处理（当前版本暂时保留，未来可扩展）\r\n/* const switchTab = (tabIndex) => {\r\n\tconsole.log('切换Tab:', tabIndex)\r\n\tactiveTab.value = tabIndex\r\n} */\r\n\r\n// 反馈类型选择处理\r\nconst selectType = (typeIndex) => {\r\n\tconsole.log('选择反馈类型:', typeIndex === 0 ? '意见建议' : '服务投诉')\r\n\tselectedType.value = typeIndex\r\n\t// 切换类型时重置类别选择\r\n\tselectedCategory.value = ''\r\n}\r\n\r\n// 具体类别选择处理\r\nconst selectCategory = (category) => {\r\n\tconsole.log('选择类别:', category.text)\r\n\tselectedCategory.value = category.value\r\n}\r\n\r\n// 返回按钮处理\r\nconst handleGoBack = () => {\r\n\tuni.navigateBack()\r\n}\r\n\r\n// ==================== 表单验证逻辑 ====================\r\n\r\n// 表单验证方法\r\nconst validateForm = () => {\r\n\t// 验证反馈类型\r\n\tif (selectedType.value === null) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择反馈类型',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\r\n\t// 验证具体类别\r\n\tif (!selectedCategory.value) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请选择具体类别',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\r\n\t// 验证详细描述\r\n\tif (!formData.description.trim()) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '请填写详细描述',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\r\n\tif (formData.description.trim().length < 10) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '描述内容至少10个字符',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\r\n\tif (formData.description.length > 500) {\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '描述内容不能超过500字符',\r\n\t\t\ticon: 'none'\r\n\t\t})\r\n\t\treturn false\r\n\t}\r\n\r\n\t// 验证联系电话（可选，如果填写则验证格式）\r\n\tif (formData.contactPhone && formData.contactPhone.trim()) {\r\n\t\tconst phoneReg = /^1[3-9]\\d{9}$/\r\n\t\tif (!phoneReg.test(formData.contactPhone)) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请输入正确的手机号码',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn false\r\n\t\t}\r\n\t}\r\n\r\n\treturn true\r\n}\r\n\r\n// ==================== 表单提交处理 ====================\r\n\r\n// 提交表单处理\r\nconst handleSubmit = () => {\r\n\tconsole.log('开始提交表单')\r\n\t\r\n\t// 表单验证\r\n\tif (!validateForm()) {\r\n\t\treturn\r\n\t}\r\n\r\n\t// 构建提交数据\r\n\tconst submitData = {\r\n\t\ttype: selectedType.value === 0 ? 'suggestion' : 'complaint',\r\n\t\tcategory: selectedCategory.value,\r\n\t\tcaseNumber: formData.caseNumber.trim() || null,\r\n\t\tdescription: formData.description.trim(),\r\n\t\tcontactPhone: formData.contactPhone.trim() || null,\r\n\t\tsubmitTime: new Date().toISOString()\r\n\t}\r\n\r\n\tconsole.log('提交数据:', submitData)\r\n\r\n\t// 显示加载状态\r\n\tuni.showLoading({\r\n\t\ttitle: '提交中...',\r\n\t\tmask: true\r\n\t})\r\n\r\n\t// 模拟API提交（实际项目中替换为真实API调用）\r\n\tsetTimeout(() => {\r\n\t\tuni.hideLoading()\r\n\t\t\r\n\t\t// 提交成功反馈\r\n\t\tconst successTitle = selectedType.value === 0 ? '意见已提交' : '投诉已提交'\r\n\t\tuni.showToast({\r\n\t\t\ttitle: successTitle,\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 2000,\r\n\t\t\tsuccess: () => {\r\n\t\t\t\t// 延迟返回上一页\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}, 1500)\r\n\t\t\t}\r\n\t\t})\r\n\t}, 1500)\r\n}\r\n\r\n// ==================== 生命周期钩子 ====================\r\n\r\nonMounted(() => {\r\n\tconsole.log('投诉建议页面已加载')\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// ==================== CSS变量定义 ====================\r\n:root {\r\n\t--primary-color: #3b7eeb;      // 主题蓝色\r\n\t--primary-light: #e6f0ff;      // 浅蓝色背景\r\n\t--text-primary: #333;          // 主要文字颜色\r\n\t--text-secondary: #666;        // 次要文字颜色\r\n\t--text-placeholder: #999;      // 占位符文字颜色\r\n\t--border-color: #e0e0e0;       // 边框颜色\r\n\t--background-color: #f5f5f5;   // 页面背景色\r\n\t--card-background: #ffffff;    // 卡片背景色\r\n\t--shadow-light: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);  // 轻阴影\r\n\t--shadow-card: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);   // 卡片阴影\r\n\t--border-radius: 16rpx;        // 卡片圆角\r\n\t--primary-dark: #2c62c9; // tab选中颜色\r\n\t--text-light:#999;\r\n\t--transition-normal:all 0.3s ease;\r\n}\r\n\r\n// ==================== 页面整体布局 ====================\r\n.mediation-complaint-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: var(--background-color);\r\n\tpadding: 30rpx 20rpx;\r\n}\r\n\r\n// ==================== 页面头部样式 ====================\r\n.gradient-bg {\r\n\tbackground: linear-gradient(135deg, #e6f0ff 0%, #e6f0ff 100%);\r\n\tborder: none;\r\n    margin-bottom: 40rpx;\r\n\tbox-shadow: rgba(0, 0, 0, 0.04) 0 4rpx 16rpx;\r\n    margin-bottom: 30rpx;\r\n    border-radius: 24rpx;\r\n    padding: 36rpx;\r\n    transition: var(--transition-normal);\r\n\t.handle-box{\r\n\t\ttext-align: center;\r\n\t\tpadding: 20rpx 10rpx 40rpx;\r\n\t}\r\n\t.handle-icon{\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tbackground-color: var(--primary-color);\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin: 0 auto 30rpx;\r\n\t\t.fas{\r\n\t\t\tfont-size: 44rpx;\r\n\t\t\tcolor: white;\r\n\t\t}\r\n\t}\r\n\t.handle-title{\r\n\t\tmargin: 0 0 16rpx 0;\r\n\t\tcolor: var(--primary-color);\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: 600;\r\n\t}\r\n\t.handle-text{\r\n\t\tmargin: 0;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: var(--text-secondary);\r\n\t\tline-height: 1.5;\r\n\t}\r\n}\r\n// ==================== 反馈类型选择区域样式 ====================\r\n.feedback-types {\r\n\tpadding: 0 0 30rpx;\r\n\tdisplay: flex;\r\n\tgap: 20rpx;\r\n\t.form-card{\r\n\t\tflex: 1;\r\n\t}\r\n\t.type-card-content{\r\n\t\tdisplay: grid;\r\n\t\tgrid-template-columns: 1fr 1fr;\r\n\t\tgap: 28rpx; \r\n    \tpadding: 36rpx;\r\n\t}\r\n\t.type-card {\r\n\t\tflex: 1;\r\n\t\tbackground-color: var(--card-background);\r\n\t\tborder-radius: var(--border-radius);\r\n\t\tborder-radius: 16rpx;\r\n\t\tpadding: 40rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tgap: 20rpx;\r\n\t\tborder: 2rpx solid var(--border-color);\r\n\t\tbox-shadow: var(--shadow-light);\r\n\t\ttransition: var(--transition-normal);\r\n\t\t\r\n\t\t&.active {\r\n\t\t\tborder-color: var(--primary-color);\r\n\t\t\tbackground-color: var(--primary-light);\r\n    \t\tborder-top: 8rpx solid var(--primary-color);\r\n\t\t\t\r\n\t\t\t.feedback-type-icon {\r\n\t\t\t\tbackground-color: var(--primary-color);\r\n\t\t\t\t\r\n\t\t\t\t.fas {\r\n\t\t\t\t\ttransform: scale(1.1);\r\n\t\t\t\t\tcolor: white;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.type-text {\r\n\t\t\t\tcolor: var(--primary-color);\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.feedback-type-icon {\r\n\t\t\twidth: 80rpx;\r\n\t\t\theight: 80rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground: var(--primary-light);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tmargin: 0px auto 20rpx;\r\n\t\t\ttransition: 0.3s;\r\n\t\t}\r\n\t\t.feedback-type-icon .fas {\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tcolor: var(--primary-color);\r\n\t\t\ttransition: 0.3s;\r\n\t\t}\r\n\t\t.type-text {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tcolor: var(--text-primary);\r\n\t\t\tfont-weight: 500;\r\n\t\t\ttransition: var(--transition-normal);\r\n\t\t}\r\n\t}\r\n}\r\n// ==================== 表单容器样式 ====================\r\n.form-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n// ==================== 表单卡片样式 ====================\r\n.form-card {\r\n\t// margin: 20rpx 30rpx 0;\r\n\tbackground-color: var(--card-background);\r\n\tborder-radius: var(--border-radius);\r\n\tbox-shadow: var(--shadow-light);\r\n\t// border-radius: 16rpx;\r\n\toverflow: hidden;\r\n\t.card-header {\r\n\t\tpadding: 30rpx 30rpx 20rpx 30rpx;\r\n\t\t// border-bottom: 1rpx solid #f0f0f0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t\r\n\t\t.fas {\r\n\t\t\tmargin-right: 8px;\r\n    \t\tcolor: var(--primary-color);\r\n\t\t}\r\n\t\t\r\n\t\t.card-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: var(--text-primary);\r\n\t\t\tfont-weight: 800;\r\n\t\t\t// flex: 1;\r\n\t\t}\r\n\t\t\r\n\t\t.optional-tag {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: var(--text-placeholder);\r\n\t\t\t// background-color: #f0f0f0;\r\n\t\t\tpadding: 4rpx 12rpx;\r\n\t\t\t// border-radius: 12rpx;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.card-content {\r\n\t\tpadding: 30rpx;\r\n\t\t\r\n\t\t.help-text {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: var(--text-light);\r\n\t\t\tmargin-top: 15rpx;\r\n    \t\tmargin-bottom: 0;\r\n\t\t\tdisplay: flex;\r\n\t\t\t\r\n\t\t\t.fas{\r\n\t\t\t\tmargin-right: 10rpx;\r\n\t\t\t\tpadding-top: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// ==================== 输入框样式 ====================\r\n.input-field {\r\n\tborder-radius: 12rpx;\r\n\twidth: 100%;\r\n\theight: 96rpx;\r\n\tline-height: 96rpx;\r\n}\r\n\r\n// ==================== 类别网格布局样式 ====================\r\n.category-grid {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 24rpx;\r\n}\r\n\r\n.category-card {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tpadding: 32rpx;\r\n\tbackground-color: white;\r\n\tborder-radius: 24rpx;\r\n\t// border: 2rpx solid transparent;\r\n\tborder-width: 1px;\r\n    border-style: solid;\r\n    border-color: rgba(0, 0, 0, 0.06);\r\n\ttransition: 0.3s;\r\n\tposition: relative;\r\n\t\r\n\t// 中间文字信息区域\r\n\t.category-info {\r\n\t\tflex: 1;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tmargin-right: 20rpx;\r\n\t\t\r\n\t\t.category-title {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t\tcolor: var(--text-primary);\r\n\t\t\tmargin-bottom: 6rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\tline-height: 1.2;\r\n\t\t}\r\n\t\t\r\n\t\t.category-desc {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: var(--text-secondary);\r\n\t\t\tline-height: 1.3;\r\n\t\t}\r\n\t}\r\n\t// 选中状态的左边框加粗效果\r\n\t&.active {\r\n\t\tbackground-color: var(--primary-light);\r\n\t\tborder-left: 10rpx solid var(--primary-color);\r\n\t\tbox-shadow: rgba(59, 126, 235, 0.15) 0px 4px 12px;\r\n\t\tborder-color: var(--primary-color);\r\n\t\tpadding-left: 21rpx; // 减少左内边距以保持对齐\r\n\t\t\r\n\t\t.category-title {\r\n\t\t\tcolor: var(--primary-color);\r\n\t\t\tfont-weight: bold;\r\n\t\t}\r\n\t\t\r\n\t\t.category-desc {\r\n\t\t\tcolor: var(--primary-color);\r\n\t\t\topacity: 0.8;\r\n\t\t}\r\n\t\t\r\n\t\t.radio-inner {\r\n\t\t\tborder-color: var(--primary-color);\r\n\t\t\tbackground-color: var(--primary-color);\r\n\t\t\t\r\n\t\t\t.radio-dot {\r\n\t\t\t\tcolor: white;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 左侧图标区域\r\n\t.category-icon {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 20rpx;\r\n\t\tflex-shrink: 0;\r\n\t\tbackground: rgb(248, 249, 250);\r\n\t\tborder-radius: 10rpx;\r\n\t\ttransition: 0.3s;\r\n\t\t\r\n\t\t.fas {\r\n\t\t\tfont-size: 36rpx;\r\n    \t\ttransition: 0.3s;\r\n\t\t}\r\n\t}\r\n\t\r\n\t// 右侧单选框区域\r\n\t.category-radio {\r\n\t\tflex-shrink: 0;\r\n\t\t\r\n\t\t.radio-inner {\r\n\t\t\twidth: 40rpx;\r\n\t\t\theight: 40rpx;\r\n\t\t\tborder: 2rpx solid var(--border-color);\r\n\t\t\tborder-radius: 50%;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransition: all 0.3s ease;\r\n\t\t\t\r\n\t\t\t&.checked {\r\n\t\t\t\tborder-color: var(--primary-color);\r\n\t\t\t\tbackground-color: var(--primary-color);\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t.radio-dot {\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: white;\r\n\t\t\t\ttransition: 0.3s;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n\r\n// ==================== 文本域样式 ====================\r\n.textarea-container {\r\n\tposition: relative;\r\n\t\r\n\t.textarea-field {\r\n\t\tmin-height: 200rpx;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 100%;\r\n\t}\r\n\t\r\n\t.char-count {\r\n\t\tposition: absolute;\r\n\t\tbottom: 20rpx;\r\n\t\tright: 20rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: var(--text-placeholder);\r\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\r\n\t\tpadding: 4rpx 8rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n}\r\n\r\n// ==================== 提交按钮样式 ====================\r\n.submit-btn {\r\n\tmargin-top: 60rpx;\r\n\twidth: 100%;\r\n\theight: 88rpx;\r\n\tbackground: linear-gradient(135deg, var(--primary-color) 0%, #2979ff 100%);\r\n\tcolor: white;\r\n\tborder-radius: 16rpx;\r\n\tborder: none;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 12rpx;\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 500;\r\n\tbox-shadow: 0 8rpx 20rpx rgba(59, 126, 235, 0.3);\r\n\ttransition: all 0.3s ease;\r\n\t\r\n\t&:active {\r\n\t\ttransform: translateY(2rpx);\r\n\t\tbox-shadow: 0 4rpx 12rpx rgba(59, 126, 235, 0.3);\r\n\t}\r\n\t\r\n\t.submit-icon {\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\t\r\n\t.submit-text {\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n}\r\n\r\n// ==================== 全局组件样式覆盖 ====================\r\n:deep(.uni-easyinput__content) {\r\n\tborder-color: var(--border-color) !important;\r\n\tborder-radius: 12rpx !important;\r\n\tbackground-color: #fafafa !important;\r\n}\r\n\r\n:deep(.uni-easyinput__content-input) {\r\n\tfont-size: 28rpx !important;\r\n\tcolor: var(--text-primary) !important;\r\n}\r\n\r\n:deep(.uni-easyinput__placeholder-class) {\r\n\tcolor: var(--text-placeholder) !important;\r\n}\r\n\r\n:deep(.uni-easyinput__content-textarea) {\r\n\tfont-size: 28rpx !important;\r\n\tcolor: var(--text-primary) !important;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/mediation_complaint/mediation_complaint.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "reactive", "computed", "uni", "onMounted"], "mappings": ";;;;;;;;;;;;;AAuKkBA,kBAAG,IAAC,CAAC;AAGvB,UAAM,eAAeA,cAAG,IAAC,CAAC;AAG1B,UAAM,mBAAmBA,cAAG,IAAC,EAAE;AAG/B,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACzB,YAAY;AAAA;AAAA,MACZ,aAAa;AAAA;AAAA,MACb,cAAc;AAAA;AAAA,IACf,CAAC;AAKD,UAAM,iBAAiB;AAAA,MACtB,YAAY;AAAA,QACX;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,MACD;AAAA,MACD,WAAW;AAAA,QACV;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,QACD;AAAA,UACC,OAAO;AAAA,UACP,MAAM;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACP;AAAA,MACD;AAAA,IACF;AAKA,UAAM,oBAAoBC,cAAQ,SAAC,MAAM;AACxC,UAAI,aAAa,UAAU,GAAG;AAC7B,eAAO,eAAe;AAAA,MACxB,WAAY,aAAa,UAAU,GAAG;AACpC,eAAO,eAAe;AAAA,MACtB;AACD,aAAO,CAAE;AAAA,IACV,CAAC;AAGD,UAAM,qBAAqBA,cAAQ,SAAC,MAAM;AACzC,UAAI,aAAa,UAAU,GAAG;AAC7B,eAAO;AAAA,MACT,WAAY,aAAa,UAAU,GAAG;AACpC,eAAO;AAAA,MACP;AACD,aAAO;AAAA,IACR,CAAC;AAWD,UAAM,aAAa,CAAC,cAAc;AACjCC,0BAAA,MAAA,OAAA,4DAAY,WAAW,cAAc,IAAI,SAAS,MAAM;AACxD,mBAAa,QAAQ;AAErB,uBAAiB,QAAQ;AAAA,IAC1B;AAGA,UAAM,iBAAiB,CAAC,aAAa;AACpCA,oBAAA,MAAA,MAAA,OAAA,4DAAY,SAAS,SAAS,IAAI;AAClC,uBAAiB,QAAQ,SAAS;AAAA,IACnC;AAUA,UAAM,eAAe,MAAM;AAE1B,UAAI,aAAa,UAAU,MAAM;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO;AAAA,MACP;AAGD,UAAI,CAAC,iBAAiB,OAAO;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO;AAAA,MACP;AAGD,UAAI,CAAC,SAAS,YAAY,QAAQ;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO;AAAA,MACP;AAED,UAAI,SAAS,YAAY,KAAI,EAAG,SAAS,IAAI;AAC5CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO;AAAA,MACP;AAED,UAAI,SAAS,YAAY,SAAS,KAAK;AACtCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD,eAAO;AAAA,MACP;AAGD,UAAI,SAAS,gBAAgB,SAAS,aAAa,KAAI,GAAI;AAC1D,cAAM,WAAW;AACjB,YAAI,CAAC,SAAS,KAAK,SAAS,YAAY,GAAG;AAC1CA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD,iBAAO;AAAA,QACP;AAAA,MACD;AAED,aAAO;AAAA,IACR;AAKA,UAAM,eAAe,MAAM;AAC1BA,oBAAAA,MAAA,MAAA,OAAA,4DAAY,QAAQ;AAGpB,UAAI,CAAC,aAAY,GAAI;AACpB;AAAA,MACA;AAGD,YAAM,aAAa;AAAA,QAClB,MAAM,aAAa,UAAU,IAAI,eAAe;AAAA,QAChD,UAAU,iBAAiB;AAAA,QAC3B,YAAY,SAAS,WAAW,KAAM,KAAI;AAAA,QAC1C,aAAa,SAAS,YAAY,KAAM;AAAA,QACxC,cAAc,SAAS,aAAa,KAAM,KAAI;AAAA,QAC9C,aAAY,oBAAI,KAAM,GAAC,YAAa;AAAA,MACpC;AAEDA,oBAAAA,MAAA,MAAA,OAAA,4DAAY,SAAS,UAAU;AAG/BA,oBAAAA,MAAI,YAAY;AAAA,QACf,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAE;AAGD,iBAAW,MAAM;AAChBA,sBAAAA,MAAI,YAAa;AAGjB,cAAM,eAAe,aAAa,UAAU,IAAI,UAAU;AAC1DA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM;AAEd,uBAAW,MAAM;AAChBA,4BAAAA,MAAI,aAAc;AAAA,YAClB,GAAE,IAAI;AAAA,UACP;AAAA,QACJ,CAAG;AAAA,MACD,GAAE,IAAI;AAAA,IACR;AAIAC,kBAAAA,UAAU,MAAM;AACfD,oBAAAA,MAAY,MAAA,OAAA,4DAAA,WAAW;AAAA,IACxB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClaD,GAAG,WAAW,eAAe;"}