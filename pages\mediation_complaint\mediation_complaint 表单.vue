<template>
	<view class="mediation-complaint-container">
		<view class="complaint-form-card">
			<view class="form-header">
				<view class="form-header-title">提交投诉</view>
				<text class="form-header-text">如您对调解服务有任何不满意的地方，请填写以下信息提交投诉</text>
			</view>
			
			<view class="form-item">
				<view class="form-label">工单编号</view>
				<uni-easyinput
					class="form-input"
					v-model="formData.orderNumber"
					placeholder="请输入工单编号"
				></uni-easyinput>
			</view>
			
			<view class="form-item">
				<view class="form-label">投诉类型</view>
					<uni-data-select
						v-model="typeIndex"
						:localdata="complaintTypes"
						@change="onTypeChange"
					></uni-data-select>
			</view>
			
			<view class="form-item">
				<view class="form-label">投诉内容</view>
				<!-- <textarea
					class="form-textarea"
					v-model="formData.content"
					placeholder="请详细描述您的投诉内容..."
					maxlength="500"
				></textarea>
				<view class="textarea-counter">{{ formData.content.length }}/500</view> -->
				<uni-easyinput type="textarea" autoHeight v-model="formData.content" placeholder="请详细描述您的投诉内容..."></uni-easyinput>
				
			</view>
			
			<view class="form-item">
				<view class="form-label">联系方式</view>
				<uni-easyinput
					class="form-input"
					v-model="formData.contactPhone"
					placeholder="请输入您的联系电话"
					type="number"
					maxlength="11"
				></uni-easyinput>
			</view>
			
			<view class="form-actions">
				<button class="submit-button" @click="handleSubmit">提交</button>
				<button class="cancel-button" @click="handleCancel">取消</button>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';

// 表单数据
const formData = reactive({
	orderNumber: '',
	complaintType: '',
	content: '',
	contactPhone: ''
});

// 投诉类型选项
const typeIndex = ref(-1);
const complaintTypes = [
	{ value: 0, text: "服务态度" },
	{ value: 1, text: "处理速度" },
	{ value: 2, text: "调解方案" },
	{ value: 3, text: "其他问题" }
];

// 选择器变化处理
const onTypeChange = (e) => {
	console.log(e);
	typeIndex.value = e.detail.value;
	formData.complaintType = complaintTypes[typeIndex.value];
};

// 生命周期钩子
onMounted(() => {
	console.log('调解投诉页面已加载');
});

// 提交表单
const handleSubmit = () => {
	// 表单验证
	if (!validateForm()) {
		return;
	}
	
	// 显示加载中
	uni.showLoading({
		title: '提交中...'
	});
	
	// 模拟API提交
	setTimeout(() => {
		uni.hideLoading();
		
		// 提交成功
		uni.showToast({
			title: '投诉已提交',
			icon: 'success',
			duration: 2000,
			success: () => {
				// 延迟返回
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		});
	}, 1500);
};

// 表单验证
const validateForm = () => {
	// 验证投诉类型
	if (typeIndex.value === -1) {
		uni.showToast({
			title: '请选择投诉类型',
			icon: 'none'
		});
		return false;
	}
	
	// 验证投诉内容
	if (!formData.content.trim()) {
		uni.showToast({
			title: '请填写投诉内容',
			icon: 'none'
		});
		return false;
	}
	
	if (formData.content.trim().length < 10) {
		uni.showToast({
			title: '投诉内容至少10个字符',
			icon: 'none'
		});
		return false;
	}
	
	// 验证联系电话
	if (!formData.contactPhone) {
		uni.showToast({
			title: '请填写联系电话',
			icon: 'none'
		});
		return false;
	}
	
	const phoneReg = /^1\d{10}$/;
	if (!phoneReg.test(formData.contactPhone)) {
		uni.showToast({
			title: '请输入正确的手机号',
			icon: 'none'
		});
		return false;
	}
	
	return true;
};

// 取消表单
const handleCancel = () => {
	uni.navigateBack();
};
</script>

<style lang="scss" scoped>
.mediation-complaint-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx 20rpx;
}

.complaint-form-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-header {
	margin-bottom: 30rpx;
}
.form-header-title{
	color: #000;
	font-size: 32rpx;
	margin-bottom: 15rpx;
}
.form-header-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
}

.form-item {
	margin-bottom: 30rpx;
}

.form-label {
	font-size: 30rpx;
	color: #333;
	margin-bottom: 15rpx;
	font-weight: 500;
}

.form-input {
	border-radius: 8rpx;
}

.form-textarea {
	width: 100%;
	height: 240rpx;
	background-color: #f8f8f8;
	border-radius: 8rpx;
	padding: 20rpx;
	font-size: 28rpx;
	box-sizing: border-box;
}

.textarea-counter {
	text-align: right;
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.form-actions {
	display: flex;
	margin-top: 50rpx;
}

.submit-button {
	flex: 1;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #2979ff;
	color: #fff;
	font-size: 32rpx;
	border-radius: 45rpx;
	margin-right: 15rpx;
}

.cancel-button {
	flex: 1;
	height: 90rpx;
	line-height: 90rpx;
	background-color: #fff;
	color: #666;
	font-size: 32rpx;
	border-radius: 45rpx;
	border: 2rpx solid #ddd;
	margin-left: 15rpx;
}
.section--uni-section-header{
	padding: 0 !important;
}
</style>