{"version": 3, "file": "solution_confirm.js", "sources": ["pages/solution_confirm/solution_confirm.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc29sdXRpb25fY29uZmlybS9zb2x1dGlvbl9jb25maXJtLnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"solution-confirm-container\">\r\n\t\t<!-- 基本信息 -->\r\n\t\t<view class=\"work-order-card\">\r\n\t\t\t<view class=\"work-order-header\">\r\n\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t<text>调解案件号: </text>\r\n\t\t\t\t\t<text class=\"work-order-id\">{{basicInfo.case_number}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"work-order-status\">\r\n\t\t\t\t\t<text class=\"status-label\" :class=\"{'status-processing': (basicInfo.case_status_cn || workOrderData.case_status_cn) === '进行中'}\">{{basicInfo.case_status_cn || workOrderData.case_status_cn}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"work-order-date\">发起日期: {{basicInfo.initiate_date || workOrderData.createDate}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 进度条 -->\r\n\t\t<view class=\"progress-bar\">\r\n\t\t\t<view class=\"progress-steps\">\r\n\t\t\t\t<view class=\"progress-step completed\">\r\n\t\t\t\t\t<view class=\"step-circle\">1</view>\r\n\t\t\t\t\t<view class=\"step-line completed\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">调解确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step active\">\r\n\t\t\t\t\t<view class=\"step-circle\">2</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">方案确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">3</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">协议签署</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">4</view>\r\n\t\t\t\t\t<view class=\"step-label\">完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\r\n\t\t<!-- 工作方案详情区域 -->\r\n\t\t<!-- <view v-if=\"workPlanData.length > 0\" class=\"work-plan-section\">\r\n\t\t\t<view class=\"section-title\">工作方案详情</view>\r\n\t\t\t<view v-for=\"plan in workPlanData\" :key=\"plan.plan_id\" class=\"plan-detail-card\">\r\n\t\t\t\t<view class=\"plan-header\">\r\n\t\t\t\t\t<text class=\"plan-name\">{{ plan.plan_name }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"plan-content\">\r\n\t\t\t\t\t<view v-for=\"config in plan.plan_config\" :key=\"config.id\" class=\"config-item\">\r\n\t\t\t\t\t\t<view class=\"config-title\">{{ config.title }}</view>\r\n\t\t\t\t\t\t<view class=\"config-value\">{{ config.value }}</view>\r\n\t\t\t\t\t\t<view v-if=\"config.expression\" class=\"config-expression\">\r\n\t\t\t\t\t\t\t<text class=\"expression-label\">计算公式:</text>\r\n\t\t\t\t\t\t\t<text class=\"expression-text\">{{ config.expression }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<!-- 方案列表 -->\r\n\t\t<view class=\"solutions-container\">\r\n\t\t\t<!-- 显示workPlanData中的方案 -->\r\n\t\t\t<view v-if=\"workPlanData.length > 0\">\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-for=\"(plan, planIndex) in workPlanData\"\r\n\t\t\t\t\t:key=\"plan.plan_id\"\r\n\t\t\t\t\tclass=\"solution-card\"\r\n\t\t\t\t\t:class=\"{'selected': selectedPlanId === plan.plan_id}\"\r\n\t\t\t\t\t@click=\"selectPlan(plan.plan_id)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"solution-header\">\r\n\t\t\t\t\t\t<view class=\"solution-title-wrap\">\r\n\t\t\t\t\t\t\t<text class=\"solution-title\">{{plan.plan_name}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"solution-select\">\r\n\t\t\t\t\t\t\t<view class=\"radio-button\" :class=\"{'selected': selectedPlanId === plan.plan_id}\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-inner\" v-if=\"selectedPlanId === plan.plan_id\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"solution-content\">\r\n\t\t\t\t\t\t<view v-for=\"config in plan.plan_config\" :key=\"config.id\" class=\"config-detail\">\r\n\t\t\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"solution-label\">{{config.title}}</text>\r\n\t\t\t\t\t\t\t\t<text class=\"solution-value\">{{config.value}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!--<view v-if=\"config.expression\" class=\"config-formula\">\r\n\t\t\t\t\t\t\t\t<text class=\"formula-label\">计算公式:</text>\r\n\t\t\t\t\t\t\t\t<text class=\"formula-text\">{{config.expression}}</text>\r\n\t\t\t\t\t\t\t</view> -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"solution-footer\">\r\n\t\t\t\t\t\t<text class=\"view-detail\" @click.stop=\"viewPlanDetail(plan)\">查看详情</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 显示默认方案（当workPlanData为空时） \r\n\t\t\t<view v-else>\r\n\t\t\t\t<view\r\n\t\t\t\t\tv-for=\"(solution, index) in solutions\"\r\n\t\t\t\t\t:key=\"solution.id\"\r\n\t\t\t\t\tclass=\"solution-card\"\r\n\t\t\t\t\t:class=\"{'selected': selectedSolutionIndex === index}\"\r\n\t\t\t\t\t@click=\"selectSolution(index)\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<view class=\"solution-header\">\r\n\t\t\t\t\t\t<view class=\"solution-title-wrap\">\r\n\t\t\t\t\t\t\t<text class=\"solution-title\">{{solution.title}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"solution-select\">\r\n\t\t\t\t\t\t\t<view class=\"radio-button\" :class=\"{'selected': selectedSolutionIndex === index}\">\r\n\t\t\t\t\t\t\t\t<view class=\"radio-inner\" v-if=\"selectedSolutionIndex === index\"></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"solution-content\">\r\n\t\t\t\t\t\t<text class=\"solution-label\">方案内容</text>\r\n\t\t\t\t\t\t<text>{{solution.content}}</text>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"solution-details\">\r\n\t\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t\t<text class=\"solution-label\">还款总额</text>\r\n\t\t\t\t\t\t\t<text class=\"solution-value\">¥{{solution.totalAmount.toFixed(2)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"solution-item\">\r\n\t\t\t\t\t\t\t<text class=\"solution-label\">月还款额</text>\r\n\t\t\t\t\t\t\t<text class=\"solution-value\">¥{{solution.monthlyPayment.toFixed(2)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<view class=\"solution-footer\">\r\n\t\t\t\t\t\t<text class=\"discount-amount\">减免金额: ¥{{solution.discountAmount.toFixed(2)}}</text>\r\n\t\t\t\t\t\t<text class=\"view-detail\" @click.stop=\"viewSolutionDetail(solution)\">查看详情</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>-->\r\n\r\n\t\t\t<!-- 空状态提示 -->\r\n\t\t\t<view v-if=\"workPlanData.length === 0\" class=\"empty-state\">\r\n\t\t\t\t<text class=\"empty-text\">暂无方案数据</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<button class=\"confirm-button\" @click=\"handleConfirm\">\r\n\t\t\t\t<text v-if=\"workPlanData.length > 0 && selectedPlanId\">\r\n\t\t\t\t\t确认选择{{getSelectedPlan()?.plan_name || '方案'}}\r\n\t\t\t\t</text>\r\n\t\t\t\t<text v-else-if=\"solutions.length > 0\">\r\n\t\t\t\t\t确认选择{{solutions[selectedSolutionIndex]?.title || '方案'}}\r\n\t\t\t\t</text>\r\n\t\t\t\t<text v-else>\r\n\t\t\t\t\t确认选择\r\n\t\t\t\t</text>\r\n\t\t\t</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 添加联系客服悬浮按钮 -->\r\n\t\t<!-- <contact-fab></contact-fab> -->\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { api } from '@/utils/api.js';\r\n// import ContactFab from '@/components/contact-fab/contact-fab.vue';\r\n\r\n/**\r\n * 调解方案确认页面\r\n *\r\n * 此页面用于展示和选择多个调解方案，支持方案对比和确认功能\r\n * 用户可以查看方案详情，选择方案并确认选择\r\n */\r\n\r\n// 接收页面参数 - 调解案件编号\r\nconst caseNumber = ref('');\r\n\r\n// 基本数据\r\nconst workOrderData = ref({});\r\n\r\n// 页面参数相关\r\nconst basicInfo = ref({       // 基本信息\r\n  case_number: '',\r\n  initiate_date: '',\r\n  case_status_cn: '',\r\n  mediation_progress: ''\r\n});\r\nconst workPlanData = ref([]);      // 工作方案详情数据\r\n\r\n// 已完成工单数据\r\nconst completedWorkOrderData = ref({\r\n  protocol_notarization_status: '',  // 协议公证状态\r\n  mediation_info: {},                 // 调解信息\r\n  repayment_plan: {}                  // 还款方案\r\n});\r\n\r\n// 选中的方案索引（默认选中方案一）\r\nconst selectedSolutionIndex = ref(0);\r\n\r\n// 选中的方案ID（单选）\r\nconst selectedPlanId = ref('');\r\n\r\n// 调解方案数组 - 包含多个方案\r\nconst solutions = ref([]);\r\n\r\n// 计算当前选中的方案\r\nconst selectedSolution = computed(() => {\r\n\treturn solutions.value[selectedSolutionIndex.value];\r\n});\r\n\r\n// 获取选中的方案数据\r\nconst getSelectedPlan = () => {\r\n\treturn workPlanData.value.find(plan => plan.plan_id === selectedPlanId.value);\r\n};\r\n\r\n// 页面加载时处理URL参数\r\nonLoad((options) => {\r\n  console.log(\"方案确认页面加载，参数:\", options);\r\n\r\n  // 处理URL参数\r\n  if (options && options.case_number) {\r\n    handleUrlParams(options);\r\n  }\r\n});\r\n\r\n// 处理URL参数\r\nconst handleUrlParams = async (options) => {\r\n  console.log(\"处理URL参数:\", options);\r\n\r\n  const { case_number, initiate_date, case_status_cn, mediation_progress } = options;\r\n\r\n  // 检查是否有完整参数\r\n  const hasCompleteParams = case_number && initiate_date && case_status_cn && mediation_progress;\r\n\r\n  try {\r\n    uni.showLoading({ title: \"加载中...\" });\r\n\r\n    if (hasCompleteParams) {\r\n      // 有完整参数，直接显示基本信息\r\n      console.log(\"检测到完整参数，直接显示基本信息\");\r\n      basicInfo.value.case_number = decodeURIComponent(case_number);\r\n      basicInfo.value.initiate_date = decodeURIComponent(initiate_date);\r\n      basicInfo.value.case_status_cn = decodeURIComponent(case_status_cn);\r\n      basicInfo.value.mediation_progress = decodeURIComponent(mediation_progress);\r\n\r\n      // 设置caseNumber用于后续API调用\r\n      caseNumber.value = basicInfo.value.case_number;\r\n\r\n      // 并行获取数据以提高加载速度\r\n      await Promise.all([\r\n        fetchWorkPlanDetailWithoutLoading(basicInfo.value.case_number),\r\n        fetchCompletedWorkOrderWithoutLoading(basicInfo.value.case_number)\r\n      ]);\r\n    } else if (case_number) {\r\n      // 仅有案件编号，调用接口获取详细信息\r\n      console.log(\"仅有案件编号，调用接口获取详细信息\");\r\n      const decodedCaseNumber = decodeURIComponent(case_number);\r\n      caseNumber.value = decodedCaseNumber;\r\n\r\n      // 先获取案件基本信息\r\n      await fetchMediationSingleDetailWithoutLoading(decodedCaseNumber);\r\n\r\n      // 然后并行获取其他数据\r\n      await Promise.all([\r\n        fetchWorkPlanDetailWithoutLoading(decodedCaseNumber),\r\n        fetchCompletedWorkOrderWithoutLoading(decodedCaseNumber)\r\n      ]);\r\n    }\r\n  } catch (error) {\r\n    console.error(\"处理URL参数失败:\", error);\r\n    uni.showToast({\r\n      title: \"页面加载失败\",\r\n      icon: \"none\",\r\n      duration: 2000,\r\n    });\r\n  } finally {\r\n    uni.hideLoading();\r\n  }\r\n};\r\n\r\n// 获取单条调解数据基本信息\r\nconst fetchMediationSingleDetail = async (caseNumber) => {\r\n  try {\r\n    uni.showLoading({ title: \"加载案件信息...\" });\r\n\r\n    const result = await api.mediationQuery.getSingleDetail(caseNumber);\r\n\r\n    if (result.state === \"success\" && result.data) {\r\n      const data = result.data;\r\n      basicInfo.value.case_number = data.case_number || caseNumber;\r\n      basicInfo.value.initiate_date = data.initiate_date || '';\r\n      basicInfo.value.case_status_cn = data.case_status_cn || '';\r\n      basicInfo.value.mediation_progress = data.mediation_progress || '';\r\n      console.log(\"案件基本信息获取成功:\", basicInfo.value);\r\n    } else {\r\n      console.log(\"获取案件基本信息失败:\", result.msg);\r\n      uni.showToast({\r\n        title: result.msg || \"获取案件信息失败\",\r\n        icon: \"none\",\r\n        duration: 2000,\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error(\"获取案件详情失败:\", error);\r\n    uni.showToast({\r\n      title: \"获取案件信息失败\",\r\n      icon: \"none\",\r\n      duration: 2000,\r\n    });\r\n  } finally {\r\n    uni.hideLoading();\r\n  }\r\n};\r\n\r\n// 获取方案详情\r\nconst fetchWorkPlanDetail = async (caseNumber) => {\r\n\ttry {\r\n\t\tuni.showLoading({ title: \"加载方案详情...\" });\r\n\r\n\t\tconst result = await api.solution.getPlanDetail(caseNumber);\r\n\r\n\t\tif (result.state === \"success\" && result.data) {\r\n\t\t\tworkPlanData.value = result.data;\r\n\t\t\tconsole.log(\"方案详情获取成功:\", result.data);\r\n\r\n\t\t\t// 默认选择第一个方案\r\n\t\t\tif (result.data.length > 0) {\r\n\t\t\t\tselectedPlanId.value = result.data[0].plan_id;\r\n\t\t\t\tconsole.log(\"默认选择第一个方案:\", result.data[0].plan_name);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.log(\"工作方案详情获取失败:\", result.msg);\r\n\t\t\tworkPlanData.value = [];\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: result.msg || \"获取方案详情失败\",\r\n\t\t\t\ticon: \"none\",\r\n\t\t\t\tduration: 2000,\r\n\t\t\t});\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"获取方案详情失败:\", error);\r\n\t\tworkPlanData.value = [];\r\n\t\tuni.showToast({\r\n\t\t\ttitle: \"获取方案详情失败\",\r\n\t\t\ticon: \"none\",\r\n\t\t\tduration: 2000,\r\n\t\t});\r\n\t} finally {\r\n\t\tuni.hideLoading();\r\n\t}\r\n};\r\n\r\n// 获取已完成工单信息（协议公证状态、调解信息、还款方案）\r\nconst fetchCompletedWorkOrder = async (caseNumber) => {\r\n\ttry {\r\n\t\tuni.showLoading({ title: \"加载工单信息...\" });\r\n\r\n\t\tconst result = await api.workOrder.getCompletedWorkOrder(caseNumber);\r\n\r\n\t\tif (result.state === \"success\" && result.data) {\r\n\t\t\tcompletedWorkOrderData.value = {\r\n\t\t\t\tprotocol_notarization_status: result.data.protocol_notarization_status || '',\r\n\t\t\t\tmediation_info: result.data.mediation_info || {},\r\n\t\t\t\trepayment_plan: result.data.repayment_plan || {}\r\n\t\t\t};\r\n\t\t\tconsole.log(\"已完成工单信息获取成功:\", completedWorkOrderData.value);\r\n\t\t} else {\r\n\t\t\tconsole.log(\"获取已完成工单信息失败:\", result.msg);\r\n\t\t\t// 工单信息获取失败不显示错误提示，因为可能是正常情况（工单未完成）\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"获取已完成工单信息失败:\", error);\r\n\t\t// 工单信息获取失败不显示错误提示，因为可能是正常情况（工单未完成）\r\n\t} finally {\r\n\t\tuni.hideLoading();\r\n\t}\r\n};\r\n\r\n// 不带加载提示的版本 - 用于并行调用\r\nconst fetchMediationSingleDetailWithoutLoading = async (caseNumber) => {\r\n  try {\r\n    const result = await api.mediationQuery.getSingleDetail(caseNumber);\r\n\r\n    if (result.state === \"success\" && result.data) {\r\n      const data = result.data;\r\n      basicInfo.value.case_number = data.case_number || caseNumber;\r\n      basicInfo.value.initiate_date = data.initiate_date || '';\r\n      basicInfo.value.case_status_cn = data.case_status_cn || '';\r\n      basicInfo.value.mediation_progress = data.mediation_progress || '';\r\n      console.log(\"案件基本信息获取成功:\", basicInfo.value);\r\n    } else {\r\n      console.log(\"获取案件基本信息失败:\", result.msg);\r\n      throw new Error(result.msg || \"获取案件信息失败\");\r\n    }\r\n  } catch (error) {\r\n    console.error(\"获取案件详情失败:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nconst fetchWorkPlanDetailWithoutLoading = async (caseNumber) => {\r\n\ttry {\r\n\t\tconst result = await api.solution.getPlanDetail(caseNumber);\r\n\r\n\t\tif (result.state === \"success\" && result.data) {\r\n\t\t\tworkPlanData.value = result.data;\r\n\t\t\tconsole.log(\"方案详情获取成功:\", result.data);\r\n\r\n\t\t\t// 默认选择第一个方案\r\n\t\t\tif (result.data.length > 0) {\r\n\t\t\t\tselectedPlanId.value = result.data[0].plan_id;\r\n\t\t\t\tconsole.log(\"默认选择第一个方案:\", result.data[0].plan_name);\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.log(\"工作方案详情获取失败:\", result.msg);\r\n\t\t\tworkPlanData.value = [];\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"获取方案详情失败:\", error);\r\n\t\tworkPlanData.value = [];\r\n\t}\r\n};\r\n\r\nconst fetchCompletedWorkOrderWithoutLoading = async (caseNumber) => {\r\n\ttry {\r\n\t\tconst result = await api.workOrder.getCompletedWorkOrder(caseNumber);\r\n\r\n\t\tif (result.state === \"success\" && result.data) {\r\n\t\t\tcompletedWorkOrderData.value = {\r\n\t\t\t\tprotocol_notarization_status: result.data.protocol_notarization_status || '',\r\n\t\t\t\tmediation_info: result.data.mediation_info || {},\r\n\t\t\t\trepayment_plan: result.data.repayment_plan || {}\r\n\t\t\t};\r\n\t\t\tconsole.log(\"已完成工单信息获取成功:\", completedWorkOrderData.value);\r\n\t\t} else {\r\n\t\t\tconsole.log(\"获取已完成工单信息失败:\", result.msg);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error(\"获取已完成工单信息失败:\", error);\r\n\t}\r\n};\r\n\r\n// 生命周期钩子 - 页面加载时执行\r\nonMounted(() => {\r\n\tconsole.log('使用默认方案数据');\r\n});\r\n\r\n/**\r\n * 获取方案详情数据\r\n * @param {String} id - ID\r\n */\r\nconst fetchSolutionDetail = (id) => {\r\n\tif (id) {\r\n\t\t// 使用API获取数据\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '加载中...'\r\n\t\t});\r\n\r\n\t\tapi.solution.getDetail(id)\r\n\t\t\t.then(res => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t// 设置基本信息\r\n\t\t\t\t\tworkOrderData.value = res.data.workOrder;\r\n\r\n\t\t\t\t\t// 如果API返回了多个方案，则更新方案数组\r\n\t\t\t\t\tif (res.data.solutions && res.data.solutions.length > 0) {\r\n\t\t\t\t\t\tsolutions.value = res.data.solutions;\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg || '获取方案详情失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t.catch(err => {\r\n\t\t\t\tuni.hideLoading();\r\n\t\t\t\tconsole.error('获取方案详情失败', err);\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '获取方案详情失败',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t} else {\r\n\t\t// 使用模拟数据\r\n\t\tsetTimeout(() => {\r\n\t\t\tconsole.log('调解方案数据已加载（模拟）');\r\n\t\t}, 500);\r\n\t}\r\n};\r\n\r\n/**\r\n * 选择方案\r\n * @param {Number} index - 方案索引\r\n */\r\nconst selectSolution = (index) => {\r\n\tselectedSolutionIndex.value = index;\r\n\tconsole.log('选择了方案:', solutions.value[index].title);\r\n};\r\n\r\n/**\r\n * 选择工作方案（单选）\r\n * @param {String} planId - 方案ID\r\n */\r\nconst selectPlan = (planId) => {\r\n\tselectedPlanId.value = planId;\r\n\tconsole.log('当前选中的方案ID:', selectedPlanId.value);\r\n};\r\n\r\n/**\r\n * 查看工作方案详情\r\n * @param {Object} plan - 方案对象\r\n */\r\nconst viewPlanDetail = (plan) => {\r\n\tconst configDetails = plan.plan_config.map(config => {\r\n\t\tlet detail = `${config.title}: ${config.value}`;\r\n\t\tif (config.expression) {\r\n\t\t\tdetail += `\\n计算公式: ${config.expression}`;\r\n\t\t}\r\n\t\treturn detail;\r\n\t}).join('\\n\\n');\r\n\r\n\tuni.showModal({\r\n\t\ttitle: `${plan.plan_name}详情`,\r\n\t\tcontent: configDetails,\r\n\t\tshowCancel: false\r\n\t});\r\n};\r\n\r\n/**\r\n * 查看方案详情\r\n * @param {Object} solution - 方案对象\r\n */\r\nconst viewSolutionDetail = (solution) => {\r\n\t/* uni.showModal({\r\n\t\ttitle: `${solution.title}详情`,\r\n\t\tcontent: `${solution.content}\\n\\n还款总额: ¥${solution.totalAmount.toFixed(2)}\\n月还款额: ¥${solution.monthlyPayment.toFixed(2)}\\n还款期数: ${solution.periods}期\\n减免金额: ¥${solution.discountAmount.toFixed(2)}`,\r\n\t\tshowCancel: false\r\n\t}); */\r\n};\r\n\r\n/**\r\n * 处理确认方案\r\n * 确认用户选择的方案并提交到服务器\r\n */\r\nconst handleConfirm = () => {\r\n\t// 判断是工作方案还是默认方案\r\n\tif (workPlanData.value.length > 0) {\r\n\t\t// 处理工作方案确认\r\n\t\tif (!selectedPlanId.value) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请选择一个方案',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconst selectedPlan = getSelectedPlan();\r\n\t\tconst planName = selectedPlan?.plan_name || '未知方案';\r\n\r\n\t\t// 显示确认对话框\r\n\t\tuni.showModal({\r\n\t\t\ttitle: '确认方案',\r\n\t\t\tcontent: `您确定要选择\"${planName}\"吗？`,\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t// 用户点击确定\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t\t});\r\n\t\t\t\t\tconsole.log(selectedPlan,'===selectedPlan')\r\n\t\t\t\t\t// 调用API确认方案\r\n\t\t\t\t\tif (caseNumber.value) {\r\n\t\t\t\t\t\tapi.solution.confirmSolution(caseNumber.value, {\r\n\t\t\t\t\t\t\tmediation_plan : selectedPlanId.value,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tif (res.state === \"success\") {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '方案确认成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t// 成功后跳转到协议签署页面\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/agreement_signing/agreement_signing',\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到协议签署页面');\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '操作失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tconsole.error('确认方案失败', err);\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '确认方案失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '缺少调解方案编号',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\t} else {\r\n\t\t// 处理默认方案确认\r\n\t\tconst selectedSolution = solutions.value[selectedSolutionIndex.value];\r\n\r\n\t\tif (!selectedSolution) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请选择一个方案',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// 显示确认对话框\r\n\t\tuni.showModal({\r\n\t\t\ttitle: '确认方案',\r\n\t\t\tcontent: `您确定要选择\"${selectedSolution.title}\"吗？`,\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t// 用户点击确定\r\n\t\t\t\t\tuni.showLoading({\r\n\t\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\t// 调用API确认方案\r\n\t\t\t\t\tif (caseNumber.value) {\r\n\t\t\t\t\t\tapi.solution.confirmSolution(caseNumber.value, { solutionId: selectedSolution.id })\r\n\t\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tif (res.state === \"success\") {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '方案确认成功',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t\t});\r\n\r\n\t\t\t\t\t\t\t\t\t// 成功后跳转到协议签署页面\r\n\t\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\t\turl: '/pages/agreement_signing/agreement_signing',\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到协议签署页面');\r\n\t\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: res.msg || '操作失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\t\tconsole.error('确认方案失败', err);\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '确认方案失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '缺少调解方案编号',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t});\r\n\t}\r\n};\r\n\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:root {\r\n\t--primary-color: #3b7eeb;\r\n}\r\n.solution-confirm-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n/* 卡片样式 */\r\n.work-order-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.work-order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n\tfont-size: 29rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n\tpadding: 6rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n\tfont-size: 26rpx;\r\n\tpadding: 8rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: #fff;\r\n}\r\n\r\n.status-processing {\r\n\tbackground-color: #1890ff;\r\n\tcolor: #fff;\r\n}\r\n\r\n.work-order-date {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* 工作方案详情样式 */\r\n.work-plan-section {\r\n\tmargin: 30rpx 0;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding: 0 30rpx;\r\n}\r\n\r\n.plan-detail-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 16rpx;\r\n\tmargin: 0 30rpx 20rpx;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.plan-header {\r\n\tpadding: 25rpx 30rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n\tborder-radius: 16rpx 16rpx 0 0;\r\n}\r\n\r\n.plan-name {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #fff;\r\n}\r\n\r\n.plan-content {\r\n\tpadding: 20rpx 30rpx 30rpx;\r\n}\r\n\r\n.config-item {\r\n\tpadding: 20rpx 0;\r\n\tborder-bottom: 1rpx solid #f5f5f5;\r\n}\r\n\r\n.config-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.config-title {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 600;\r\n\tmargin-bottom: 10rpx;\r\n}\r\n\r\n.config-value {\r\n\tfont-size: 32rpx;\r\n\tcolor: #007aff;\r\n\tfont-weight: 700;\r\n\tmargin-bottom: 8rpx;\r\n}\r\n\r\n.config-expression {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmargin-top: 8rpx;\r\n}\r\n\r\n.expression-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n.expression-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tfont-family: 'Courier New', monospace;\r\n\tbackground-color: #f8f9fa;\r\n\tpadding: 4rpx 8rpx;\r\n\tborder-radius: 6rpx;\r\n}\r\n\r\n/* 进度条样式 */\r\n/* .progress-bar {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n} */\r\n\r\n.progress-steps {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-step {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tposition: relative;\r\n}\r\n\r\n.step-circle {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #e0e0e0;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.step-line {\r\n\tposition: absolute;\r\n\ttop: 30rpx;\r\n\tleft: 50%;\r\n\tright: -50%;\r\n\theight: 4rpx;\r\n\tbackground-color: #e0e0e0;\r\n\tz-index: 1;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n\tdisplay: none;\r\n}\r\n\r\n.step-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\ttext-align: center;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.progress-step.active .step-label {\r\n\tcolor: #2979ff;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.progress-step.completed .step-circle {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.step-line.completed {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.progress-step.completed .step-label {\r\n\tcolor: #2979ff;\r\n}\r\n\r\n/* 方案容器 */\r\n.solutions-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n/* 方案卡片样式 */\r\n.solution-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tborder: 2rpx solid #eee;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s ease;\r\n\tmargin-bottom: 30rpx;\r\n}\r\n\r\n.solution-card.selected {\r\n\tborder-color: #2979ff;\r\n\tbox-shadow: 0 4rpx 20rpx rgba(41, 121, 255, 0.1);\r\n}\r\n\r\n.solution-card:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.solution-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n\tpadding-bottom: 20rpx;\r\n\tborder-bottom: 1px solid #eee;\r\n}\r\n\r\n.solution-title-wrap {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.solution-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.solution-tag {\r\n\tfont-size: 24rpx;\r\n\tcolor: #2979ff;\r\n\tmargin-left: 10rpx;\r\n}\r\n\r\n.solution-select {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.radio-button {\r\n\twidth: 40rpx;\r\n\theight: 40rpx;\r\n\tborder-radius: 50%;\r\n\tborder: 2rpx solid rgb(221, 221, 221);\r\n\tborder-image: initial;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\ttransition: all 0.3s ease;\r\n}\r\n\r\n.radio-button.selected {\r\n\tborder-color: #3b7eeb;\r\n\tbackground-color: var(--primary-color);\r\n}\r\n\r\n.radio-inner {\r\n\twidth: 16rpx;\r\n\theight: 16rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.solution-content {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n\tmargin-bottom: 30rpx;\r\n\tline-height: 1.5;\r\n\tgap: 16rpx;\r\n\tdisplay: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.solution-details {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.solution-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tpadding: 10rpx 0;\r\n\tgap: 8rpx;\r\n}\r\n\r\n.solution-label {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n}\r\n\r\n.solution-value {\r\n\tfont-size: 32rpx;\r\n\tcolor: #333;\r\n}\r\n\r\n.solution-footer {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding-top: 20rpx;\r\n\tborder-top: 1px solid #eee;\r\n}\r\n\r\n/* 配置项详情样式 */\r\n/* .config-detail {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n */\r\n.config-detail:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.config-formula {\r\n\tmargin-top: 8rpx;\r\n\tpadding: 8rpx 12rpx;\r\n\tbackground-color: #f8f9fa;\r\n\tborder-radius: 6rpx;\r\n\tborder-left: 3rpx solid #007aff;\r\n}\r\n\r\n.formula-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #666;\r\n\tmargin-right: 8rpx;\r\n}\r\n\r\n.formula-text {\r\n\tfont-size: 24rpx;\r\n\tcolor: #333;\r\n\tfont-family: 'Courier New', monospace;\r\n}\r\n\r\n/* 空状态样式 */\r\n.empty-state {\r\n\ttext-align: center;\r\n\tpadding: 100rpx 30rpx;\r\n}\r\n\r\n.empty-text {\r\n\tfont-size: 28rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.discount-amount {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n}\r\n\r\n.view-detail {\r\n\tfont-size: 26rpx;\r\n\tcolor: #2979ff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n.view-detail::before {\r\n\tcontent: 'i';\r\n\tdisplay: inline-block;\r\n\twidth: 30rpx;\r\n\theight: 30rpx;\r\n\tline-height: 30rpx;\r\n\ttext-align: center;\r\n\tfont-size: 22rpx;\r\n\tfont-style: italic;\r\n\tfont-weight: bold;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #2979ff;\r\n\tcolor: #fff;\r\n\tmargin-right: 10rpx;\r\n}\r\n\r\n/* 联系客服区域 */\r\n.contact-section {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.contact-button {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 10rpx;\r\n\tpadding: 20rpx;\r\n\tborder-radius: 12rpx;\r\n\tbackground-color: #fff;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.contact-button.phone {\r\n\tcolor: #2979ff;\r\n}\r\n\r\n.contact-button.wechat {\r\n\tcolor: #07c160;\r\n}\r\n\r\n.contact-button .fa,\r\n.contact-button .fab {\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n/* 底部按钮 */\r\n/* .action-buttons {\r\n\tpadding: 20rpx 0;\r\n} */\r\n\r\n.confirm-button {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\ttext-align: center;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 32rpx;\r\n\tbackground-color: #2979ff;\r\n\tcolor: #fff;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/solution_confirm/solution_confirm.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "onLoad", "uni", "caseNumber", "api", "onMounted", "res"], "mappings": ";;;;;;AAyLA,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAG5B,UAAM,YAAYA,cAAAA,IAAI;AAAA;AAAA,MACpB,aAAa;AAAA,MACb,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,oBAAoB;AAAA,IACtB,CAAC;AACD,UAAM,eAAeA,cAAAA,IAAI,CAAA,CAAE;AAG3B,UAAM,yBAAyBA,cAAAA,IAAI;AAAA,MACjC,8BAA8B;AAAA;AAAA,MAC9B,gBAAgB,CAAE;AAAA;AAAA,MAClB,gBAAgB,CAAE;AAAA;AAAA,IACpB,CAAC;AAGD,UAAM,wBAAwBA,cAAAA,IAAI,CAAC;AAGnC,UAAM,iBAAiBA,cAAAA,IAAI,EAAE;AAG7B,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AAGCC,kBAAAA,SAAS,MAAM;AACvC,aAAO,UAAU,MAAM,sBAAsB,KAAK;AAAA,IACnD,CAAC;AAGD,UAAM,kBAAkB,MAAM;AAC7B,aAAO,aAAa,MAAM,KAAK,UAAQ,KAAK,YAAY,eAAe,KAAK;AAAA,IAC7E;AAGAC,kBAAM,OAAC,CAAC,YAAY;AAClBC,oBAAY,MAAA,MAAA,OAAA,sDAAA,gBAAgB,OAAO;AAGnC,UAAI,WAAW,QAAQ,aAAa;AAClC,wBAAgB,OAAO;AAAA,MACxB;AAAA,IACH,CAAC;AAGD,UAAM,kBAAkB,OAAO,YAAY;AACzCA,6FAAY,YAAY,OAAO;AAE/B,YAAM,EAAE,aAAa,eAAe,gBAAgB,mBAAkB,IAAK;AAG3E,YAAM,oBAAoB,eAAe,iBAAiB,kBAAkB;AAE5E,UAAI;AACFA,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,YAAI,mBAAmB;AAErBA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,kBAAkB;AAC9B,oBAAU,MAAM,cAAc,mBAAmB,WAAW;AAC5D,oBAAU,MAAM,gBAAgB,mBAAmB,aAAa;AAChE,oBAAU,MAAM,iBAAiB,mBAAmB,cAAc;AAClE,oBAAU,MAAM,qBAAqB,mBAAmB,kBAAkB;AAG1E,qBAAW,QAAQ,UAAU,MAAM;AAGnC,gBAAM,QAAQ,IAAI;AAAA,YAChB,kCAAkC,UAAU,MAAM,WAAW;AAAA,YAC7D,sCAAsC,UAAU,MAAM,WAAW;AAAA,UACzE,CAAO;AAAA,QACF,WAAU,aAAa;AAEtBA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,mBAAmB;AAC/B,gBAAM,oBAAoB,mBAAmB,WAAW;AACxD,qBAAW,QAAQ;AAGnB,gBAAM,yCAAyC,iBAAiB;AAGhE,gBAAM,QAAQ,IAAI;AAAA,YAChB,kCAAkC,iBAAiB;AAAA,YACnD,sCAAsC,iBAAiB;AAAA,UAC/D,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,sDAAA,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACRA,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACH;AAqGA,UAAM,2CAA2C,OAAOC,gBAAe;AACrE,UAAI;AACF,cAAM,SAAS,MAAMC,UAAG,IAAC,eAAe,gBAAgBD,WAAU;AAElE,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC7C,gBAAM,OAAO,OAAO;AACpB,oBAAU,MAAM,cAAc,KAAK,eAAeA;AAClD,oBAAU,MAAM,gBAAgB,KAAK,iBAAiB;AACtD,oBAAU,MAAM,iBAAiB,KAAK,kBAAkB;AACxD,oBAAU,MAAM,qBAAqB,KAAK,sBAAsB;AAChED,iGAAY,eAAe,UAAU,KAAK;AAAA,QAChD,OAAW;AACLA,wBAAY,MAAA,MAAA,OAAA,sDAAA,eAAe,OAAO,GAAG;AACrC,gBAAM,IAAI,MAAM,OAAO,OAAO,UAAU;AAAA,QACzC;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAc,MAAA,MAAA,SAAA,sDAAA,aAAa,KAAK;AAChC,cAAM;AAAA,MACP;AAAA,IACH;AAEA,UAAM,oCAAoC,OAAOC,gBAAe;AAC/D,UAAI;AACH,cAAM,SAAS,MAAMC,UAAG,IAAC,SAAS,cAAcD,WAAU;AAE1D,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC9C,uBAAa,QAAQ,OAAO;AAC5BD,wBAAY,MAAA,MAAA,OAAA,sDAAA,aAAa,OAAO,IAAI;AAGpC,cAAI,OAAO,KAAK,SAAS,GAAG;AAC3B,2BAAe,QAAQ,OAAO,KAAK,CAAC,EAAE;AACtCA,gCAAA,MAAA,OAAA,sDAAY,cAAc,OAAO,KAAK,CAAC,EAAE,SAAS;AAAA,UAClD;AAAA,QACJ,OAAS;AACNA,wBAAY,MAAA,MAAA,OAAA,sDAAA,eAAe,OAAO,GAAG;AACrC,uBAAa,QAAQ;QACrB;AAAA,MACD,SAAQ,OAAO;AACfA,iGAAc,aAAa,KAAK;AAChC,qBAAa,QAAQ;MACrB;AAAA,IACF;AAEA,UAAM,wCAAwC,OAAOC,gBAAe;AACnE,UAAI;AACH,cAAM,SAAS,MAAMC,UAAG,IAAC,UAAU,sBAAsBD,WAAU;AAEnE,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC9C,iCAAuB,QAAQ;AAAA,YAC9B,8BAA8B,OAAO,KAAK,gCAAgC;AAAA,YAC1E,gBAAgB,OAAO,KAAK,kBAAkB,CAAE;AAAA,YAChD,gBAAgB,OAAO,KAAK,kBAAkB,CAAE;AAAA,UACpD;AACGD,wBAAY,MAAA,MAAA,OAAA,sDAAA,gBAAgB,uBAAuB,KAAK;AAAA,QAC3D,OAAS;AACNA,wBAAY,MAAA,MAAA,OAAA,sDAAA,gBAAgB,OAAO,GAAG;AAAA,QACtC;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,sDAAA,gBAAgB,KAAK;AAAA,MACnC;AAAA,IACF;AAGAG,kBAAAA,UAAU,MAAM;AACfH,oBAAAA,MAAY,MAAA,OAAA,sDAAA,UAAU;AAAA,IACvB,CAAC;AA4DD,UAAM,aAAa,CAAC,WAAW;AAC9B,qBAAe,QAAQ;AACvBA,6FAAY,cAAc,eAAe,KAAK;AAAA,IAC/C;AAMA,UAAM,iBAAiB,CAAC,SAAS;AAChC,YAAM,gBAAgB,KAAK,YAAY,IAAI,YAAU;AACpD,YAAI,SAAS,GAAG,OAAO,KAAK,KAAK,OAAO,KAAK;AAC7C,YAAI,OAAO,YAAY;AACtB,oBAAU;AAAA,QAAW,OAAO,UAAU;AAAA,QACtC;AACD,eAAO;AAAA,MACT,CAAE,EAAE,KAAK,MAAM;AAEdA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,GAAG,KAAK,SAAS;AAAA,QACxB,SAAS;AAAA,QACT,YAAY;AAAA,MACd,CAAE;AAAA,IACF;AAkBA,UAAM,gBAAgB,MAAM;AAE3B,UAAI,aAAa,MAAM,SAAS,GAAG;AAElC,YAAI,CAAC,eAAe,OAAO;AAC1BA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AAED,cAAM,eAAe;AACrB,cAAM,YAAW,6CAAc,cAAa;AAG5CA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,UAAU,QAAQ;AAAA,UAC3B,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAEhBA,4BAAAA,MAAI,YAAY;AAAA,gBACf,OAAO;AAAA,cACb,CAAM;AACDA,4BAAAA,MAAA,MAAA,OAAA,sDAAY,cAAa,iBAAiB;AAE1C,kBAAI,WAAW,OAAO;AACrBE,0BAAAA,IAAI,SAAS,gBAAgB,WAAW,OAAO;AAAA,kBAC9C,gBAAiB,eAAe;AAAA,gBACvC,CAAO,EACC,KAAK,CAAAE,SAAO;AACZJ,gCAAG,MAAC,YAAW;AACf,sBAAII,KAAI,UAAU,WAAW;AAC5BJ,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAOI,KAAI,OAAO;AAAA,sBAClB,MAAM;AAAA,sBACN,UAAU;AAAA,oBACpB,CAAU;AAGD,+BAAW,MAAM;AAChBJ,oCAAAA,MAAI,WAAW;AAAA,wBACd,KAAK;AAAA,wBACL,SAAS,MAAM;AACdA,wCAAAA,MAAA,MAAA,OAAA,sDAAY,WAAW;AAAA,wBACvB;AAAA,wBACD,MAAM,CAAC,QAAQ;AACdA,wCAAA,MAAA,MAAA,SAAA,sDAAc,QAAQ,GAAG;AACzBA,wCAAAA,MAAI,UAAU;AAAA,4BACb,OAAO;AAAA,4BACP,MAAM;AAAA,0BACnB,CAAa;AAAA,wBACD;AAAA,sBACZ,CAAW;AAAA,oBACD,GAAE,GAAI;AAAA,kBAChB,OAAe;AACNA,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAOI,KAAI,OAAO;AAAA,sBAClB,MAAM;AAAA,oBAChB,CAAU;AAAA,kBACD;AAAA,gBACT,CAAQ,EACA,MAAM,SAAO;AACbJ,gCAAG,MAAC,YAAW;AACfA,gCAAc,MAAA,MAAA,SAAA,sDAAA,UAAU,GAAG;AAC3BA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,MAAM;AAAA,kBACf,CAAS;AAAA,gBACT,CAAQ;AAAA,cACR,OAAY;AACNA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACH,OAAQ;AAEN,cAAM,mBAAmB,UAAU,MAAM,sBAAsB,KAAK;AAEpE,YAAI,CAAC,kBAAkB;AACtBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AAGDA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,SAAS,UAAU,iBAAiB,KAAK;AAAA,UACzC,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,SAAS;AAEhBA,4BAAAA,MAAI,YAAY;AAAA,gBACf,OAAO;AAAA,cACb,CAAM;AAGD,kBAAI,WAAW,OAAO;AACrBE,8BAAI,SAAS,gBAAgB,WAAW,OAAO,EAAE,YAAY,iBAAiB,IAAI,EAChF,KAAK,CAAAE,SAAO;AACZJ,gCAAG,MAAC,YAAW;AACf,sBAAII,KAAI,UAAU,WAAW;AAC5BJ,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAOI,KAAI,OAAO;AAAA,sBAClB,MAAM;AAAA,sBACN,UAAU;AAAA,oBACpB,CAAU;AAGD,+BAAW,MAAM;AAChBJ,oCAAAA,MAAI,WAAW;AAAA,wBACd,KAAK;AAAA,wBACL,SAAS,MAAM;AACdA,wCAAAA,MAAA,MAAA,OAAA,sDAAY,WAAW;AAAA,wBACvB;AAAA,wBACD,MAAM,CAAC,QAAQ;AACdA,wCAAA,MAAA,MAAA,SAAA,sDAAc,QAAQ,GAAG;AACzBA,wCAAAA,MAAI,UAAU;AAAA,4BACb,OAAO;AAAA,4BACP,MAAM;AAAA,0BACnB,CAAa;AAAA,wBACD;AAAA,sBACZ,CAAW;AAAA,oBACD,GAAE,GAAI;AAAA,kBAChB,OAAe;AACNA,kCAAAA,MAAI,UAAU;AAAA,sBACb,OAAOI,KAAI,OAAO;AAAA,sBAClB,MAAM;AAAA,oBAChB,CAAU;AAAA,kBACD;AAAA,gBACT,CAAQ,EACA,MAAM,SAAO;AACbJ,gCAAG,MAAC,YAAW;AACfA,gCAAc,MAAA,MAAA,SAAA,sDAAA,UAAU,GAAG;AAC3BA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAO;AAAA,oBACP,MAAM;AAAA,kBACf,CAAS;AAAA,gBACT,CAAQ;AAAA,cACR,OAAY;AACNA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACb,CAAO;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAAA,QACJ,CAAG;AAAA,MACD;AAAA,IACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzsBA,GAAG,WAAW,eAAe;"}