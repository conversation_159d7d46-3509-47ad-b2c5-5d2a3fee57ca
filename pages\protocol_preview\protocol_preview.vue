<template>
	<view class="protocol-preview-container">
		<!-- 预览选项 -->
		<!-- <view class="preview-options">
			<button class="option-btn image-preview" @click="viewAsImages">
				<i class="fas fa-file-text"></i>
				<text>在线预览</text>
				<text class="option-desc">长图连续阅读</text>
			</button>
		</view> -->
		
		<!-- 长图预览模式 -->
		<view v-if="showImagePreview" class="long-image-preview">
			<view class="preview-header">
				<view class="header-left">
					<!-- API数据状态指示器 -->
					<view class="api-status" v-if="solutionData">
						<text class="status-text">✓ 数据已加载</text>
					</view>
					<view class="api-status error" v-else-if="apiError">
						<text class="status-text">⚠ {{apiError}}</text>
					</view>
				</view>
				<view class="header-right">
					<text class="progress-text">{{Math.round(scrollProgress)}}%</text>
					<!-- 手动刷新按钮 -->
					<button class="refresh-btn" @click="initializeApiData" :disabled="isLoading">
						<text class="refresh-icon" :class="{'spinning': isLoading}">⟳</text>
					</button>
					<!-- 调试按钮（开发时使用） -->
					<button class="debug-btn" @click="showApiDataDebug">
						<text class="debug-text">调试</text>
					</button>
				</view>
			</view>
			
			<!-- 长图容器 -->
			<scroll-view 
				class="long-scroll-container"
				scroll-y="true"
				:scroll-top="scrollTop"
				@scroll="onScroll"
				enhanced
				:show-scrollbar="false">
				
				<view class="content-container">
					<!-- 加载状态 -->
					<view v-if="isLoading" class="loading-container">
						<view class="loading-icon">
							<i class="fas fa-spinner fa-spin"></i>
						</view>
						<text class="loading-text">
							{{ solutionData ? '正在加载PDF预览...' : '正在获取方案数据...' }}
						</text>
						<!-- API调用状态提示 -->
						<view v-if="!solutionData && !apiError" class="api-loading-tip">
							<text class="tip-text">正在调用 api.solution.getDetails() 接口</text>
						</view>
					</view>
					
					<!-- 错误状态显示 -->
					<view v-else-if="apiError && !solutionData" class="error-container">
						<view class="error-icon">⚠</view>
						<text class="error-title">数据获取失败</text>
						<text class="error-message">{{apiError}}</text>
						<button class="retry-btn" @click="initializeApiData">
							<text class="retry-text">重新获取</text>
						</button>
					</view>

					<!-- 协议图片列表 -->
					<view v-else class="images-container">
						<!-- SVG文件显示 -->
						<view
							v-for="(image, index) in svgImages"
							:key="`svg-${index}`"
							class="svg-container">
							<image
								:src="image.url"
								mode="widthFix"
								class="protocol-page svg-image"
								:class="{'first-page': index === 0}"
								@load="onImageLoad(image.originalIndex)"
								@error="onImageError(image.originalIndex)"
								lazy-load />
						</view>
						<!-- 普通图片显示 -->
						<image
							v-for="(image, index) in normalImages"
							:key="`img-${index}`"
							:src="image.url"
							mode="widthFix"
							class="protocol-page"
							:class="{'first-page': index === 0}"
							@load="onImageLoad(image.originalIndex)"
							@error="onImageError(image.originalIndex)"
							lazy-load />
					</view>
					
					<!-- 底部提示
					<view class="bottom-tip">
						<view class="tip-line"></view>
						<text class="tip-text">协议内容已全部加载完成</text>
						<view class="action-buttons">
							<button class="download-btn-small" @click="downloadFile">
								<i class="fas fa-download"></i>
								下载协议
							</button>
						</view>
					</view> -->
				</view>
			</scroll-view>
			
			<!-- 阅读进度指示器 -->
			<view class="progress-indicator">
				<view class="progress-bar">
					<view class="progress-fill" :style="{width: scrollProgress + '%'}"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
// 导入API工具类
import { api } from '@/utils/api.js';

// 接收的页面参数
const receivedFileUrl = ref('');
const receivedFileType = ref('');
const receivedFileName = ref('');
const receivedCaseNumber = ref('');

// 文件信息（默认值，如果没有传递参数则使用）
const fileUrl = ref('http://*************:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx');
const pdfImagesBaseUrl = ref('http://*************:10010/pdf_images/scheme/485/');
const fileType = ref('pdf'); // 文件类型：pdf, svg, docx, etc.
const fileName = ref('调解协议.pdf');

// API数据存储
const solutionData = ref(null);
const apiError = ref(null);

// 预览相关
const showImagePreview = ref(false);
const pdfImages = ref([]);
const isLoading = ref(false);
const loadedImages = ref(new Set());

// 滚动相关
const scrollTop = ref(0);
const scrollProgress = ref(0);
const containerHeight = ref(0);
const contentHeight = ref(0);

// 计算属性：分离SVG和普通图片
const svgImages = computed(() => {
	return pdfImages.value
		.map((image, index) => ({ ...image, originalIndex: index }))
		.filter(image => image.type === 'svg');
});

const normalImages = computed(() => {
	return pdfImages.value
		.map((image, index) => ({ ...image, originalIndex: index }))
		.filter(image => !image.type || image.type !== 'svg');
});

/**
 * 初始化API数据
 * 可以在页面加载时调用，用于预加载数据
 */
const initializeApiData = async () => {
	try {
		console.log('开始初始化API数据...');

		// 清除之前的错误状态
		apiError.value = null;

		// 显示加载提示
		uni.showLoading({ title: '获取数据中...' });

		await fetchSolutionDetails();

		uni.hideLoading();
		console.log('API数据初始化完成');

		uni.showToast({
			title: '数据获取成功',
			icon: 'success',
			duration: 1500
		});

		return true;
	} catch (error) {
		uni.hideLoading();
		console.error('API数据初始化失败:', error);

		uni.showToast({
			title: '数据获取失败',
			icon: 'none',
			duration: 2000
		});

		return false;
	}
};

/**
 * 处理PDF文件预览
 * 支持文件流处理和图片预览两种方式
 */
const handlePdfPreview = async () => {
	try {
		isLoading.value = true;
		showImagePreview.value = true;

		// 首先尝试获取API数据（只调用一次）
		if (!solutionData.value) {
			console.log('首次获取API数据...');
			await fetchSolutionDetails();
		} else {
			console.log('使用已缓存的API数据');
		}

		// 检查API是否返回了文件流或PDF URL
		if (solutionData.value) {
			// 情况1：API返回了直接的PDF文件URL
			if (solutionData.value.pdfUrl) {
				console.log('发现直接PDF文件URL，尝试直接预览');
				fileUrl.value = solutionData.value.pdfUrl;
				await handleDirectPdfPreview();
				return;
			}

			// 情况2：API返回了文件流数据
			if (solutionData.value.fileStream || solutionData.value.fileData) {
				console.log('发现文件流数据，处理文件流预览');
				await handleFileStreamPreview();
				return;
			}

			// 情况3：API返回了图片列表
			if (solutionData.value.pdfImages && solutionData.value.pdfImages.length > 0) {
				console.log('发现PDF图片列表，使用图片预览');
				pdfImages.value = solutionData.value.pdfImages;
				return;
			}
		}

		// 默认情况：使用图片预览模式
		console.log('使用默认图片预览模式');
		const images = await getPdfImages();
		if (images.length > 0) {
			pdfImages.value = images;
		} else {
			throw new Error('无法获取预览内容');
		}

	} catch (error) {
		console.error('PDF预览处理失败:', error);
		uni.showToast({
			title: '预览加载失败',
			icon: 'none',
			duration: 2000
		});
		closeImagePreview();
	} finally {
		isLoading.value = false;
	}
};

/**
 * 处理直接PDF文件预览
 */
const handleDirectPdfPreview = async () => {
	try {
		console.log('开始直接PDF预览，文件URL:', fileUrl.value);

		// 尝试直接打开PDF文件
		uni.downloadFile({
			url: fileUrl.value,
			success: (res) => {
				if (res.statusCode === 200) {
					uni.openDocument({
						filePath: res.tempFilePath,
						showMenu: true,
						success: () => {
							console.log('PDF文件直接预览成功');
							// 直接预览成功，关闭当前预览界面
							closeImagePreview();
						},
						fail: (error) => {
							console.log('PDF直接预览失败，转为图片预览模式:', error);
							// 如果直接预览失败，转为图片预览
							fallbackToImagePreview();
						}
					});
				} else {
					console.log('PDF下载失败，转为图片预览模式');
					fallbackToImagePreview();
				}
			},
			fail: (error) => {
				console.log('PDF下载失败，转为图片预览模式:', error);
				fallbackToImagePreview();
			}
		});
	} catch (error) {
		console.error('直接PDF预览失败:', error);
		fallbackToImagePreview();
	}
};

/**
 * 处理文件流预览
 */
const handleFileStreamPreview = async () => {
	try {
		console.log('开始处理文件流预览');
		console.log('API返回的数据结构:', solutionData.value);

		// 检查多种可能的文件流数据字段
		const fileData = solutionData.value.fileStream ||
		                 solutionData.value.fileData ||
		                 solutionData.value.data ||
		                 solutionData.value.file ||
		                 solutionData.value;

		if (!fileData) {
			console.log('未找到文件流数据，转为图片预览模式');
			fallbackToImagePreview();
			return;
		}

		console.log('文件流数据类型:', typeof fileData);
		console.log('文件流数据长度:', fileData.length || 'unknown');

		// 将文件流转换为临时文件或获取文件URL
		const filePath = await convertStreamToTempFile(fileData);

		// 如果返回的是URL，先下载再打开
		if (typeof filePath === 'string' && (filePath.startsWith('http') || filePath.startsWith('/'))) {
			console.log('获取到文件URL，开始下载:', filePath);

			uni.downloadFile({
				url: filePath,
				success: (res) => {
					if (res.statusCode === 200) {
						uni.openDocument({
							filePath: res.tempFilePath,
							showMenu: true,
							success: () => {
								console.log('文件流URL预览成功');
								closeImagePreview();
							},
							fail: (error) => {
								console.log('文件流URL预览失败，转为图片预览模式:', error);
								fallbackToImagePreview();
							}
						});
					} else {
						console.log('文件下载失败，转为图片预览模式');
						fallbackToImagePreview();
					}
				},
				fail: (error) => {
					console.log('文件下载失败，转为图片预览模式:', error);
					fallbackToImagePreview();
				}
			});
		} else {
			// 如果是本地临时文件路径，直接打开
			console.log('获取到临时文件路径，直接打开:', filePath);

			uni.openDocument({
				filePath: filePath,
				showMenu: true,
				success: () => {
					console.log('文件流预览成功');
					closeImagePreview();
				},
				fail: (error) => {
					console.log('文件流预览失败，转为图片预览模式:', error);
					fallbackToImagePreview();
				}
			});
		}

	} catch (error) {
		console.error('文件流预览失败:', error);
		fallbackToImagePreview();
	}
};

/**
 * 回退到图片预览模式
 */
const fallbackToImagePreview = async () => {
	try {
		console.log('回退到图片预览模式');
		const images = await getPdfImages();
		if (images.length > 0) {
			pdfImages.value = images;
		} else {
			throw new Error('无法获取预览图片');
		}
	} catch (error) {
		console.error('图片预览回退失败:', error);
		uni.showToast({
			title: '预览失败',
			icon: 'none'
		});
		closeImagePreview();
	}
};

/**
 * 将文件流转换为临时文件
 */
const convertStreamToTempFile = async (fileData) => {
	try {
		console.log('开始转换文件流，数据类型:', typeof fileData);

		// 情况1：base64格式的文件流
		if (typeof fileData === 'string' && fileData.startsWith('data:')) {
			console.log('检测到base64文件流数据');

			const base64Data = fileData.split(',')[1];
			const tempFileName = `temp_pdf_${Date.now()}.pdf`;
			const tempFilePath = `${uni.env.USER_DATA_PATH}/${tempFileName}`;

			return new Promise((resolve, reject) => {
				uni.getFileSystemManager().writeFile({
					filePath: tempFilePath,
					data: base64Data,
					encoding: 'base64',
					success: () => {
						console.log('base64文件写入成功:', tempFilePath);
						resolve(tempFilePath);
					},
					fail: (error) => {
						console.error('base64文件写入失败:', error);
						reject(new Error(`文件写入失败: ${error.errMsg || error.message}`));
					}
				});
			});
		}

		// 情况2：纯base64字符串（无data:前缀）
		if (typeof fileData === 'string' && /^[A-Za-z0-9+/]+=*$/.test(fileData)) {
			console.log('检测到纯base64字符串数据');

			const tempFileName = `temp_pdf_${Date.now()}.pdf`;
			const tempFilePath = `${uni.env.USER_DATA_PATH}/${tempFileName}`;

			return new Promise((resolve, reject) => {
				uni.getFileSystemManager().writeFile({
					filePath: tempFilePath,
					data: fileData,
					encoding: 'base64',
					success: () => {
						console.log('纯base64文件写入成功:', tempFilePath);
						resolve(tempFilePath);
					},
					fail: (error) => {
						console.error('纯base64文件写入失败:', error);
						reject(new Error(`文件写入失败: ${error.errMsg || error.message}`));
					}
				});
			});
		}

		// 情况3：ArrayBuffer格式
		if (fileData instanceof ArrayBuffer) {
			console.log('检测到ArrayBuffer文件流数据');

			const tempFileName = `temp_pdf_${Date.now()}.pdf`;
			const tempFilePath = `${uni.env.USER_DATA_PATH}/${tempFileName}`;

			return new Promise((resolve, reject) => {
				uni.getFileSystemManager().writeFile({
					filePath: tempFilePath,
					data: fileData,
					success: () => {
						console.log('ArrayBuffer文件写入成功:', tempFilePath);
						resolve(tempFilePath);
					},
					fail: (error) => {
						console.error('ArrayBuffer文件写入失败:', error);
						reject(new Error(`文件写入失败: ${error.errMsg || error.message}`));
					}
				});
			});
		}

		// 情况4：如果是字符串但不是base64，可能是文件URL
		if (typeof fileData === 'string' && (fileData.startsWith('http') || fileData.startsWith('/'))) {
			console.log('检测到文件URL，直接返回:', fileData);
			return fileData;
		}

		// 情况5：如果是对象，可能包含文件URL或其他信息
		if (typeof fileData === 'object' && fileData !== null) {
			console.log('检测到对象类型的文件数据:', fileData);

			// 尝试从对象中提取文件URL
			const possibleUrl = fileData.url || fileData.fileUrl || fileData.downloadUrl || fileData.path;
			if (possibleUrl && typeof possibleUrl === 'string') {
				console.log('从对象中提取到文件URL:', possibleUrl);
				return possibleUrl;
			}

			// 尝试从对象中提取base64数据
			const possibleBase64 = fileData.base64 || fileData.data || fileData.content;
			if (possibleBase64 && typeof possibleBase64 === 'string') {
				console.log('从对象中提取到base64数据');
				return await convertStreamToTempFile(possibleBase64);
			}
		}

		// 不支持的格式
		console.error('不支持的文件流格式:', typeof fileData, fileData);
		throw new Error(`不支持的文件流格式: ${typeof fileData}`);

	} catch (error) {
		console.error('文件流转换失败:', error);
		throw error;
	}
};

/**
 * 图片预览模式
 */
const viewAsImages = async () => {
	try {
		isLoading.value = true;
		showImagePreview.value = true;
		
		// 获取PDF转换的图片列表
		const images = await getPdfImages();
		if (images.length > 0) {
			pdfImages.value = images;
		} else {
			uni.showToast({
				title: '暂无可预览内容',
				icon: 'none'
			});
			closeImagePreview();
		}
	} catch (error) {
		console.error('加载图片预览失败:', error);
		uni.showToast({
			title: '加载失败，请稍后重试',
			icon: 'error'
		});
		closeImagePreview();
	} finally {
		isLoading.value = false;
	}
};

/**
 * 调用API获取方案详情数据
 */
const fetchSolutionDetails = async () => {
	try {
		console.log('开始调用 api.solution.getDetails() 接口...');

		// 调用API获取数据
		const result = await api.solution.getDetails();

		if (result.state === "success" && result.data) {
			solutionData.value = result.data;
			console.log('方案详情数据获取成功:', result.data);

			// 详细日志：分析API返回的数据结构
			console.log('=== API数据结构分析 ===');
			console.log('数据类型:', typeof result.data);
			console.log('数据键值:', Object.keys(result.data));

			// 检查可能的文件相关字段
			const fileFields = ['fileStream', 'fileData', 'data', 'file', 'pdfUrl', 'url', 'downloadUrl', 'pdfInfo', 'pdfImages'];
			fileFields.forEach(field => {
				if (result.data[field] !== undefined) {
					console.log(`发现字段 ${field}:`, typeof result.data[field], result.data[field]);
				}
			});
			console.log('=== 数据结构分析结束 ===');

			// 如果API返回了PDF相关信息，更新文件信息
			if (result.data.pdfInfo) {
				const pdfInfo = result.data.pdfInfo;
				if (pdfInfo.fileUrl) {
					fileUrl.value = pdfInfo.fileUrl;
				}
				if (pdfInfo.fileName) {
					fileName.value = pdfInfo.fileName;
				}
				if (pdfInfo.baseUrl) {
					pdfImagesBaseUrl.value = pdfInfo.baseUrl;
				}
			}

			return result.data;
		} else {
			const errorMsg = result.msg || '获取方案详情失败';
			console.error('API调用失败:', errorMsg);
			apiError.value = errorMsg;

			uni.showToast({
				title: errorMsg,
				icon: 'none',
				duration: 2000
			});

			throw new Error(errorMsg);
		}
	} catch (error) {
		console.error('调用 api.solution.getDetails() 失败:', error);
		apiError.value = error.message || '网络请求失败';

		uni.showToast({
			title: '获取数据失败，请检查网络连接',
			icon: 'none',
			duration: 2000
		});

		throw error;
	}
};

/**
 * 获取PDF转换的图片
 * 注意：此函数不再调用API，假设API数据已经通过其他方式获取
 */
const getPdfImages = async () => {
	try {
		// 如果API返回了图片列表，使用API数据
		if (solutionData.value && solutionData.value.pdfImages) {
			console.log('使用API返回的PDF图片列表');
			return solutionData.value.pdfImages;
		}

		// 否则使用默认的图片列表生成逻辑
		console.log('使用默认PDF图片列表生成逻辑');

		// 模拟加载延时
		await new Promise(resolve => setTimeout(resolve, 1000));

		// 生成默认图片列表（基于案件号或默认值）
		const pageCount = solutionData.value?.pageCount || 5;
		const images = [];

		for (let i = 1; i <= pageCount; i++) {
			images.push({
				url: `${pdfImagesBaseUrl.value}page_${i}.jpg`,
				page: i
			});
		}

		return images;
	} catch (error) {
		console.error('获取PDF图片失败:', error);

		// 如果出错，使用模拟数据
		console.log('使用模拟数据');
		await new Promise(resolve => setTimeout(resolve, 500));

		return [
			{ url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },
			{ url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },
			{ url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },
			{ url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },
			{ url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }
		];
	}
};

/**
 * 滚动事件处理
 */
const onScroll = (e) => {
	const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;
	
	// 更新滚动位置
	scrollTop.value = currentScrollTop;
	
	// 计算阅读进度
	if (scrollHeight > clientHeight) {
		scrollProgress.value = (currentScrollTop / (scrollHeight - clientHeight)) * 100;
	}
};

/**
 * 图片加载成功
 */
const onImageLoad = (index) => {
	loadedImages.value.add(index);
	console.log(`图片 ${index + 1} 加载成功`);
};

/**
 * 图片加载失败
 */
const onImageError = (index) => {
	console.error(`图片 ${index + 1} 加载失败`);
	uni.showToast({
		title: `第${index + 1}页加载失败`,
		icon: 'none',
		duration: 2000
	});
};

/**
 * 关闭图片预览
 */
const closeImagePreview = () => {
	showImagePreview.value = false;
	scrollTop.value = 0;
	scrollProgress.value = 0;
	loadedImages.value.clear();
};

/**
 * 显示API数据调试信息
 */
const showApiDataDebug = () => {
	console.log('=== 显示API调试信息 ===');

	let debugInfo = '';

	if (solutionData.value) {
		debugInfo = `API数据已获取\n`;
		debugInfo += `数据类型: ${typeof solutionData.value}\n`;
		debugInfo += `数据键值: ${Object.keys(solutionData.value).join(', ')}\n\n`;

		// 检查文件相关字段
		const fileFields = ['fileStream', 'fileData', 'data', 'file', 'pdfUrl', 'url', 'downloadUrl'];
		fileFields.forEach(field => {
			if (solutionData.value[field] !== undefined) {
				const value = solutionData.value[field];
				debugInfo += `${field}: ${typeof value} (${value?.length || 'unknown'})\n`;
			}
		});
	} else if (apiError.value) {
		debugInfo = `API调用失败\n错误信息: ${apiError.value}`;
	} else {
		debugInfo = 'API数据尚未获取';
	}

	uni.showModal({
		title: 'API数据调试信息',
		content: debugInfo,
		showCancel: true,
		cancelText: '关闭',
		confirmText: '重新获取',
		success: (res) => {
			if (res.confirm) {
				initializeApiData();
			}
		}
	});

	console.log('=== API调试信息显示完成 ===');
};

/**
 * SVG文件预览
 */
const showSvgPreview = () => {
	try {
		isLoading.value = true;
		showImagePreview.value = true;

		// 对于SVG文件，直接显示
		pdfImages.value = [{
			url: fileUrl.value,
			page: 1,
			type: 'svg'
		}];

		console.log('SVG文件预览准备完成:', fileUrl.value);
	} catch (error) {
		console.error('SVG预览失败:', error);
		uni.showToast({
			title: 'SVG预览失败',
			icon: 'error'
		});
	} finally {
		isLoading.value = false;
	}
};

/**
 * 检测文件类型
 */
const detectFileType = (url) => {
	if (!url) return 'unknown';

	const extension = url.split('.').pop()?.toLowerCase();
	switch (extension) {
		case 'svg':
			return 'svg';
		case 'pdf':
			return 'pdf';
		case 'docx':
		case 'doc':
			return 'docx';
		case 'jpg':
		case 'jpeg':
		case 'png':
		case 'gif':
			return 'image';
		default:
			return 'unknown';
	}
};

/**
 * 下载文件
 */
const downloadFile = () => {
	uni.showLoading({ title: '正在下载...' });

	uni.downloadFile({
		url: fileUrl.value,
		success: (res) => {
			if (res.statusCode === 200) {
				// 对于SVG文件，可以直接保存到相册（如果是图片格式）
				if (fileType.value === 'svg') {
					uni.saveImageToPhotosAlbum({
						filePath: res.tempFilePath,
						success: () => {
							uni.hideLoading();
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							});
						},
						fail: (error) => {
							console.error('保存SVG失败:', error);
							// 如果保存失败，尝试用文档方式打开
							openDocument(res.tempFilePath);
						}
					});
				} else {
					// 其他文件类型用文档方式打开
					openDocument(res.tempFilePath);
				}
			}
		},
		fail: (error) => {
			uni.hideLoading();
			console.error('下载失败:', error);
			uni.showToast({
				title: '下载失败，请检查网络连接',
				icon: 'error'
			});
		}
	});
};

/**
 * 打开文档
 */
const openDocument = (filePath) => {
	uni.openDocument({
		filePath: filePath,
		showMenu: true,
		success: () => {
			uni.hideLoading();
			uni.showToast({
				title: '打开成功',
				icon: 'success'
			});
		},
		fail: (error) => {
			uni.hideLoading();
			console.error('打开文档失败:', error);
			uni.showToast({
				title: '打开失败，请检查是否安装相应的阅读器',
				icon: 'none',
				duration: 3000
			});
		}
	});
};

// 页面加载时获取参数
onLoad((options) => {
	console.log('预览页面参数:', options);

	// 获取文件URL参数
	if (options.fileUrl) {
		try {
			receivedFileUrl.value = decodeURIComponent(options.fileUrl);
			fileUrl.value = receivedFileUrl.value;
			console.log('接收到文件URL:', fileUrl.value);
		} catch (error) {
			console.error('fileUrl参数解码失败:', error);
			receivedFileUrl.value = options.fileUrl;
			fileUrl.value = options.fileUrl;
		}
	}

	// 获取文件类型参数
	if (options.fileType) {
		try {
			receivedFileType.value = decodeURIComponent(options.fileType);
			fileType.value = receivedFileType.value;
			console.log('接收到文件类型:', fileType.value);
		} catch (error) {
			console.error('fileType参数解码失败:', error);
			receivedFileType.value = options.fileType;
			fileType.value = options.fileType;
		}
	}

	// 获取文件名参数
	if (options.fileName) {
		try {
			receivedFileName.value = decodeURIComponent(options.fileName);
			fileName.value = receivedFileName.value;
			console.log('接收到文件名:', fileName.value);
		} catch (error) {
			console.error('fileName参数解码失败:', error);
			receivedFileName.value = options.fileName;
			fileName.value = options.fileName;
		}
	}

	// 获取案件号参数
	if (options.caseNumber) {
		try {
			receivedCaseNumber.value = decodeURIComponent(options.caseNumber);
			console.log('接收到案件号:', receivedCaseNumber.value);
			// 根据案件号构建PDF图片基础URL
			pdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${receivedCaseNumber.value}/`;
		} catch (error) {
			console.error('caseNumber参数解码失败:', error);
			receivedCaseNumber.value = options.caseNumber;
			pdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${options.caseNumber}/`;
		}
	}
});

onMounted(async () => {
	// 根据文件类型设置页面标题
	const title = fileName.value ? `预览 - ${fileName.value}` : '文件预览';
	uni.setNavigationBarTitle({ title });

	try {
		// 页面加载时先获取API数据（统一调用一次）
		console.log('页面加载完成，开始获取方案详情数据...');
		await fetchSolutionDetails();

		// 根据文件类型选择预览方式
		if (fileType.value === 'svg') {
			// SVG文件直接显示
			showSvgPreview();
		} else if (fileType.value === 'pdf' || fileType.value === 'docx') {
			// PDF文件使用增强的预览处理（此时API数据已获取）
			handlePdfPreview();
		} else {
			// 其他文件类型尝试图片预览
			viewAsImages();
		}
	} catch (error) {
		console.error('页面初始化过程中发生错误:', error);
		// 即使API调用失败，也继续显示预览功能
		console.log('API调用失败，使用默认预览方式');

		if (fileType.value === 'svg') {
			showSvgPreview();
		} else {
			// 对于PDF，直接使用图片预览模式
			viewAsImages();
		}
	}
});
</script>

<style lang="scss" scoped>
.protocol-preview-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx;
}

.preview-options {
	display: flex;
	gap: 30rpx;
	margin-bottom: 40rpx;
}

.option-btn {
	flex: 1;
	background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	padding: 40rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
	}
	
	.fas {
		font-size: 48rpx;
		color: #3b7eeb;
		margin-bottom: 16rpx;
	}
	
	text {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.option-desc {
		font-size: 24rpx !important;
		color: #666 !important;
		font-weight: normal !important;
		margin-bottom: 0 !important;
	}
}

/* 长图预览样式 */
.long-image-preview {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: #fff;
	z-index: 9999;
}

.preview-header {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 88rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-bottom: 2rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	z-index: 10000;
}

.header-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.api-status {
	padding: 8rpx 16rpx;
	border-radius: 12rpx;
	background-color: #e8f5e8;

	&.error {
		background-color: #ffeaea;
	}

	.status-text {
		font-size: 22rpx;
		color: #4caf50;

		.error & {
			color: #f44336;
		}
	}
}

.header-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.refresh-btn {
	background: transparent;
	border: 2rpx solid #e0e0e0;
	border-radius: 50%;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	&:disabled {
		opacity: 0.6;
	}

	&:active:not(:disabled) {
		transform: scale(0.95);
	}
}

.refresh-icon {
	font-size: 28rpx;
	color: #666;
	transition: transform 0.3s ease;

	&.spinning {
		animation: spin 1s linear infinite;
	}
}

@keyframes spin {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

.debug-btn {
	background-color: #ff9800;
	border: none;
	border-radius: 8rpx;
	padding: 8rpx 16rpx;
	margin-left: 10rpx;

	.debug-text {
		font-size: 22rpx;
		color: #fff;
	}

	&:active {
		transform: scale(0.95);
	}
}

/* .header-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.back-btn {
	background: transparent;
	border: none;
	color: #333;
	font-size: 32rpx;
	padding: 8rpx;
}

.doc-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
} */

.progress-text {
	font-size: 26rpx;
	color: #666;
}

.long-scroll-container {
	width: 100%;
	height: 100vh;
	padding-top: 88rpx;
}

.content-container {
	min-height: calc(100vh - 88rpx);
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 60vh;
	
	.loading-icon {
		margin-bottom: 30rpx;
		
		.fas {
			font-size: 48rpx;
			color: #3b7eeb;
		}
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 20rpx;
	}

	.api-loading-tip {
		padding: 20rpx;
		background-color: #f0f8ff;
		border-radius: 12rpx;
		border: 2rpx solid #e3f2fd;

		.tip-text {
			font-size: 24rpx;
			color: #1976d2;
			text-align: center;
		}
	}
}

.error-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 60vh;
	padding: 40rpx;

	.error-icon {
		font-size: 80rpx;
		color: #f44336;
		margin-bottom: 30rpx;
	}

	.error-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.error-message {
		font-size: 26rpx;
		color: #666;
		text-align: center;
		margin-bottom: 40rpx;
		line-height: 1.5;
	}

	.retry-btn {
		background-color: #3b7eeb;
		color: #fff;
		border: none;
		border-radius: 12rpx;
		padding: 24rpx 48rpx;
		font-size: 28rpx;

		&:active {
			transform: scale(0.98);
		}

		.retry-text {
			color: #fff;
		}
	}
}

.images-container {
	background-color: #fff;
}

.protocol-page {
	width: 100%;
	display: block;
	border-bottom: 2rpx solid #f0f0f0;

	&.first-page {
		border-top: none;
	}
}

/* SVG容器样式 */
.svg-container {
	width: 100%;
	background-color: #fff;
	border-bottom: 2rpx solid #f0f0f0;
}

.svg-image {
	width: 100%;
	height: auto;
	display: block;
}

.bottom-tip {
	padding: 60rpx 40rpx;
	text-align: center;
	background-color: #f9f9f9;
	
	.tip-line {
		width: 100rpx;
		height: 4rpx;
		background-color: #e0e0e0;
		margin: 0 auto 30rpx;
		border-radius: 2rpx;
	}
	
	.tip-text {
		display: block;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
	}
}

.action-buttons {
	display: flex;
	justify-content: center;
}

.download-btn-small {
	background-color: #3b7eeb;
	color: #fff;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
	
	&:active {
		transform: scale(0.98);
	}
}

/* 进度指示器 */
.progress-indicator {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 6rpx;
	background-color: rgba(0, 0, 0, 0.1);
	z-index: 10000;
}

.progress-bar {
	width: 100%;
	height: 100%;
	position: relative;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #3b7eeb 0%, #2c62c9 100%);
	transition: width 0.3s ease;
}
</style>