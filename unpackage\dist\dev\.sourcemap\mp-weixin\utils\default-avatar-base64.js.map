{"version": 3, "file": "default-avatar-base64.js", "sources": ["utils/default-avatar-base64.js"], "sourcesContent": ["// 默认头像的base64编码数据\n// 这些是简单的SVG头像，转换为base64格式以确保兼容性\n\n/**\n * 默认头像 - 通用\n */\nexport const DEFAULT_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNFNUU3RUIiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iIzlDQTNBRiIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiM5Q0EzQUYiLz48L2c+PC9zdmc+';\n\n/**\n * 男性默认头像\n */\nexport const MALE_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNEREY0RkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iIzM5OEVGNyIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiMzOThFRjciLz48L2c+PC9zdmc+';\n\n/**\n * 女性默认头像\n */\nexport const FEMALE_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGRUY3RkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iI0VDNEE5OSIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiNFQzRBOTkiLz48L2c+PC9zdmc+';\n\n/**\n * 占位符头像 - 加载中\n */\nexport const PLACEHOLDER_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGM0Y0RjYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjMwIiBmaWxsPSIjRDFENURCIiBvcGFjaXR5PSIwLjUiLz48L3N2Zz4=';\n\n/**\n * 错误头像 - 加载失败时显示\n */\nexport const ERROR_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGRUY3RkYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjQwIiBmaWxsPSIjRkVGMkYyIiBzdHJva2U9IiNGODcxNzEiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNGODcxNzEiPj88L3RleHQ+PC9zdmc+';\n\n/**\n * 获取base64格式的默认头像\n * @param {string} type 头像类型\n * @param {string|number} gender 性别\n * @returns {string} base64头像数据\n */\nexport function getBase64Avatar(type = 'default', gender = null) {\n  // 根据性别返回对应头像\n  if (type === 'default' && gender !== null) {\n    switch (parseInt(gender)) {\n      case 0: // 男性\n        return MALE_AVATAR_BASE64;\n      case 1: // 女性\n        return FEMALE_AVATAR_BASE64;\n      default: // 保密或其他\n        return DEFAULT_AVATAR_BASE64;\n    }\n  }\n\n  // 根据类型返回头像\n  switch (type) {\n    case 'male':\n      return MALE_AVATAR_BASE64;\n    case 'female':\n      return FEMALE_AVATAR_BASE64;\n    case 'placeholder':\n    case 'loading':\n      return PLACEHOLDER_AVATAR_BASE64;\n    case 'error':\n      return ERROR_AVATAR_BASE64;\n    default:\n      return DEFAULT_AVATAR_BASE64;\n  }\n}\n\n/**\n * 检查是否为base64头像\n * @param {string} url 头像URL\n * @returns {boolean} 是否为base64格式\n */\nexport function isBase64Avatar(url) {\n  return url && url.startsWith('data:image/');\n}\n\n/**\n * 获取头像的显示名称\n * @param {string} type 头像类型\n * @returns {string} 显示名称\n */\nexport function getAvatarDisplayName(type) {\n  const names = {\n    default: '默认头像',\n    male: '男性头像',\n    female: '女性头像',\n    placeholder: '占位符头像',\n    loading: '加载中头像',\n    error: '错误头像'\n  };\n\n  return names[type] || '未知头像';\n}"], "names": [], "mappings": ";AAMO,MAAM,wBAAwB;AAK9B,MAAM,qBAAqB;AAK3B,MAAM,uBAAuB;AAK7B,MAAM,4BAA4B;AAKlC,MAAM,sBAAsB;AAQ5B,SAAS,gBAAgB,OAAO,WAAW,SAAS,MAAM;AAE/D,MAAI,SAAS,aAAa,WAAW,MAAM;AACzC,YAAQ,SAAS,MAAM,GAAC;AAAA,MACtB,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AACH,eAAO;AAAA,MACT;AACE,eAAO;AAAA,IACV;AAAA,EACF;AAGD,UAAQ,MAAI;AAAA,IACV,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACV;AACH;;"}