/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.privacy-policy-page.data-v-79e9aead {
  min-height: 100vh;
  background-color: #ffffff;
}
.content.data-v-79e9aead {
  height: 100vh;
  padding: 0 30rpx;
}
.policy-content.data-v-79e9aead {
  padding: 40rpx 0;
}
.header-section.data-v-79e9aead {
  text-align: center;
  margin-bottom: 60rpx;
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #e5e5e5;
}
.main-title.data-v-79e9aead {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}
.update-info.data-v-79e9aead {
  display: block;
  font-size: 24rpx;
  color: #999999;
}
.intro.data-v-79e9aead {
  margin-bottom: 60rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}
.intro-text.data-v-79e9aead {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.8;
  text-align: justify;
}
.section.data-v-79e9aead {
  margin-bottom: 50rpx;
}
.section-title.data-v-79e9aead {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}
.subsection-title.data-v-79e9aead {
  display: block;
  font-size: 30rpx;
  font-weight: 500;
  color: #555555;
  margin: 30rpx 0 15rpx 0;
  line-height: 1.5;
}
.section-text.data-v-79e9aead {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.8;
  text-align: justify;
  margin-bottom: 20rpx;
  text-indent: 2em;
}