{"version": 3, "file": "performance-optimizer.js", "sources": ["utils/performance-optimizer.js"], "sourcesContent": ["// 性能优化工具\nimport { isDebug } from '@/config/env.js';\nimport { assetPreloader } from '@/config/assets.js';\n\n/**\n * 性能监控器\n */\nclass PerformanceMonitor {\n  constructor() {\n    this.metrics = new Map();\n    this.observers = [];\n    this.isEnabled = isDebug();\n  }\n\n  /**\n   * 开始性能测量\n   * @param {string} name 测量名称\n   */\n  start(name) {\n    if (!this.isEnabled) return;\n    \n    this.metrics.set(name, {\n      startTime: Date.now(),\n      startMemory: this.getMemoryUsage()\n    });\n  }\n\n  /**\n   * 结束性能测量\n   * @param {string} name 测量名称\n   */\n  end(name) {\n    if (!this.isEnabled) return;\n    \n    const metric = this.metrics.get(name);\n    if (!metric) return;\n\n    const endTime = Date.now();\n    const endMemory = this.getMemoryUsage();\n    \n    const result = {\n      name,\n      duration: endTime - metric.startTime,\n      memoryDelta: endMemory - metric.startMemory,\n      timestamp: endTime\n    };\n\n    console.log(`[性能] ${name}: ${result.duration}ms, 内存变化: ${result.memoryDelta}KB`);\n    \n    // 通知观察者\n    this.observers.forEach(observer => observer(result));\n    \n    this.metrics.delete(name);\n    return result;\n  }\n\n  /**\n   * 获取内存使用情况（简化版本）\n   */\n  getMemoryUsage() {\n    // 微信小程序中无法直接获取内存使用情况\n    // 这里返回一个模拟值\n    return 0;\n  }\n\n  /**\n   * 添加性能观察者\n   * @param {Function} observer 观察者函数\n   */\n  addObserver(observer) {\n    this.observers.push(observer);\n  }\n\n  /**\n   * 移除性能观察者\n   * @param {Function} observer 观察者函数\n   */\n  removeObserver(observer) {\n    const index = this.observers.indexOf(observer);\n    if (index > -1) {\n      this.observers.splice(index, 1);\n    }\n  }\n}\n\n/**\n * 资源加载优化器\n */\nclass ResourceOptimizer {\n  constructor() {\n    this.loadQueue = [];\n    this.loadingResources = new Set();\n    this.loadedResources = new Set();\n    this.maxConcurrent = 3; // 最大并发加载数\n  }\n\n  /**\n   * 预加载关键资源\n   */\n  async preloadCriticalResources() {\n    const monitor = performanceMonitor;\n    monitor.start('preload-critical');\n\n    try {\n      // 预加载关键头像资源\n      const result = await assetPreloader.preloadCriticalAssets();\n      \n      if (isDebug()) {\n        console.log('关键资源预加载完成:', result);\n      }\n\n      monitor.end('preload-critical');\n      return result;\n    } catch (error) {\n      monitor.end('preload-critical');\n      console.error('关键资源预加载失败:', error);\n      return { success: [], failed: [], total: 0, successRate: 0 };\n    }\n  }\n\n  /**\n   * 延迟加载非关键资源\n   * @param {Array} resources 资源列表\n   */\n  async lazyLoadResources(resources) {\n    if (!Array.isArray(resources)) return;\n\n    const monitor = performanceMonitor;\n    monitor.start('lazy-load');\n\n    try {\n      // 分批加载资源\n      const batches = this.createBatches(resources, this.maxConcurrent);\n      const results = [];\n\n      for (const batch of batches) {\n        const batchResults = await Promise.allSettled(\n          batch.map(resource => this.loadResource(resource))\n        );\n        results.push(...batchResults);\n      }\n\n      monitor.end('lazy-load');\n      return results;\n    } catch (error) {\n      monitor.end('lazy-load');\n      console.error('延迟加载失败:', error);\n      return [];\n    }\n  }\n\n  /**\n   * 创建资源加载批次\n   * @param {Array} resources 资源列表\n   * @param {number} batchSize 批次大小\n   */\n  createBatches(resources, batchSize) {\n    const batches = [];\n    for (let i = 0; i < resources.length; i += batchSize) {\n      batches.push(resources.slice(i, i + batchSize));\n    }\n    return batches;\n  }\n\n  /**\n   * 加载单个资源\n   * @param {string} resource 资源URL\n   */\n  loadResource(resource) {\n    return new Promise((resolve, reject) => {\n      if (this.loadedResources.has(resource)) {\n        resolve(resource);\n        return;\n      }\n\n      if (this.loadingResources.has(resource)) {\n        // 如果正在加载，等待加载完成\n        const checkLoaded = () => {\n          if (this.loadedResources.has(resource)) {\n            resolve(resource);\n          } else {\n            setTimeout(checkLoaded, 100);\n          }\n        };\n        checkLoaded();\n        return;\n      }\n\n      this.loadingResources.add(resource);\n\n      uni.getImageInfo({\n        src: resource,\n        success: () => {\n          this.loadingResources.delete(resource);\n          this.loadedResources.add(resource);\n          resolve(resource);\n        },\n        fail: (error) => {\n          this.loadingResources.delete(resource);\n          reject(error);\n        }\n      });\n    });\n  }\n}\n\n/**\n * 内存优化器\n */\nclass MemoryOptimizer {\n  constructor() {\n    this.cleanupTasks = [];\n    this.cleanupInterval = null;\n  }\n\n  /**\n   * 启动内存清理\n   */\n  startCleanup() {\n    if (this.cleanupInterval) return;\n\n    // 每5分钟执行一次清理\n    this.cleanupInterval = setInterval(() => {\n      this.performCleanup();\n    }, 5 * 60 * 1000);\n  }\n\n  /**\n   * 停止内存清理\n   */\n  stopCleanup() {\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n      this.cleanupInterval = null;\n    }\n  }\n\n  /**\n   * 执行内存清理\n   */\n  performCleanup() {\n    const monitor = performanceMonitor;\n    monitor.start('memory-cleanup');\n\n    try {\n      // 执行所有清理任务\n      this.cleanupTasks.forEach(task => {\n        try {\n          task();\n        } catch (error) {\n          console.error('清理任务执行失败:', error);\n        }\n      });\n\n      // 清理过期的缓存\n      this.clearExpiredCache();\n\n      if (isDebug()) {\n        console.log('内存清理完成');\n      }\n\n      monitor.end('memory-cleanup');\n    } catch (error) {\n      monitor.end('memory-cleanup');\n      console.error('内存清理失败:', error);\n    }\n  }\n\n  /**\n   * 添加清理任务\n   * @param {Function} task 清理任务函数\n   */\n  addCleanupTask(task) {\n    if (typeof task === 'function') {\n      this.cleanupTasks.push(task);\n    }\n  }\n\n  /**\n   * 移除清理任务\n   * @param {Function} task 清理任务函数\n   */\n  removeCleanupTask(task) {\n    const index = this.cleanupTasks.indexOf(task);\n    if (index > -1) {\n      this.cleanupTasks.splice(index, 1);\n    }\n  }\n\n  /**\n   * 清理过期缓存\n   */\n  clearExpiredCache() {\n    try {\n      const now = Date.now();\n      const keys = ['token_expire_time', 'login_time'];\n      \n      keys.forEach(key => {\n        try {\n          const expireTime = uni.getStorageSync(key);\n          if (expireTime && now > expireTime) {\n            uni.removeStorageSync(key);\n          }\n        } catch (error) {\n          // 忽略单个key的清理错误\n        }\n      });\n    } catch (error) {\n      console.error('清理过期缓存失败:', error);\n    }\n  }\n}\n\n// 创建全局实例\nexport const performanceMonitor = new PerformanceMonitor();\nexport const resourceOptimizer = new ResourceOptimizer();\nexport const memoryOptimizer = new MemoryOptimizer();\n\n/**\n * 初始化性能优化\n */\nexport function initPerformanceOptimization() {\n  if (isDebug()) {\n    console.log('初始化性能优化...');\n  }\n\n  // 启动内存清理\n  memoryOptimizer.startCleanup();\n\n  // 预加载关键资源\n  resourceOptimizer.preloadCriticalResources().catch(error => {\n    console.error('关键资源预加载失败:', error);\n  });\n\n  // 添加页面卸载时的清理任务\n  memoryOptimizer.addCleanupTask(() => {\n    // 清理临时文件\n    try {\n      // 这里可以添加具体的清理逻辑\n    } catch (error) {\n      console.error('页面卸载清理失败:', error);\n    }\n  });\n}\n\n/**\n * 销毁性能优化\n */\nexport function destroyPerformanceOptimization() {\n  memoryOptimizer.stopCleanup();\n  \n  if (isDebug()) {\n    console.log('性能优化已销毁');\n  }\n}\n"], "names": ["isDebug", "uni", "assetPreloader"], "mappings": ";;;;AAOA,MAAM,mBAAmB;AAAA,EACvB,cAAc;AACZ,SAAK,UAAU,oBAAI;AACnB,SAAK,YAAY;AACjB,SAAK,YAAYA,WAAAA;EAClB;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,MAAM;AACV,QAAI,CAAC,KAAK;AAAW;AAErB,SAAK,QAAQ,IAAI,MAAM;AAAA,MACrB,WAAW,KAAK,IAAK;AAAA,MACrB,aAAa,KAAK,eAAgB;AAAA,IACxC,CAAK;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,IAAI,MAAM;AACR,QAAI,CAAC,KAAK;AAAW;AAErB,UAAM,SAAS,KAAK,QAAQ,IAAI,IAAI;AACpC,QAAI,CAAC;AAAQ;AAEb,UAAM,UAAU,KAAK;AACrB,UAAM,YAAY,KAAK;AAEvB,UAAM,SAAS;AAAA,MACb;AAAA,MACA,UAAU,UAAU,OAAO;AAAA,MAC3B,aAAa,YAAY,OAAO;AAAA,MAChC,WAAW;AAAA,IACjB;AAEIC,kBAAA,MAAA,MAAA,OAAA,wCAAY,QAAQ,IAAI,KAAK,OAAO,QAAQ,aAAa,OAAO,WAAW,IAAI;AAG/E,SAAK,UAAU,QAAQ,cAAY,SAAS,MAAM,CAAC;AAEnD,SAAK,QAAQ,OAAO,IAAI;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AAGf,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,YAAY,UAAU;AACpB,SAAK,UAAU,KAAK,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,eAAe,UAAU;AACvB,UAAM,QAAQ,KAAK,UAAU,QAAQ,QAAQ;AAC7C,QAAI,QAAQ,IAAI;AACd,WAAK,UAAU,OAAO,OAAO,CAAC;AAAA,IAC/B;AAAA,EACF;AACH;AAKA,MAAM,kBAAkB;AAAA,EACtB,cAAc;AACZ,SAAK,YAAY;AACjB,SAAK,mBAAmB,oBAAI;AAC5B,SAAK,kBAAkB,oBAAI;AAC3B,SAAK,gBAAgB;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,2BAA2B;AAC/B,UAAM,UAAU;AAChB,YAAQ,MAAM,kBAAkB;AAEhC,QAAI;AAEF,YAAM,SAAS,MAAMC,6BAAe;AAEpC,UAAIF,WAAO,QAAA,GAAI;AACbC,sBAAA,MAAA,MAAA,OAAA,yCAAY,cAAc,MAAM;AAAA,MACjC;AAED,cAAQ,IAAI,kBAAkB;AAC9B,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,IAAI,kBAAkB;AAC9BA,oBAAc,MAAA,MAAA,SAAA,yCAAA,cAAc,KAAK;AACjC,aAAO,EAAE,SAAS,CAAE,GAAE,QAAQ,CAAA,GAAI,OAAO,GAAG,aAAa;IAC1D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,kBAAkB,WAAW;AACjC,QAAI,CAAC,MAAM,QAAQ,SAAS;AAAG;AAE/B,UAAM,UAAU;AAChB,YAAQ,MAAM,WAAW;AAEzB,QAAI;AAEF,YAAM,UAAU,KAAK,cAAc,WAAW,KAAK,aAAa;AAChE,YAAM,UAAU,CAAA;AAEhB,iBAAW,SAAS,SAAS;AAC3B,cAAM,eAAe,MAAM,QAAQ;AAAA,UACjC,MAAM,IAAI,cAAY,KAAK,aAAa,QAAQ,CAAC;AAAA,QAC3D;AACQ,gBAAQ,KAAK,GAAG,YAAY;AAAA,MAC7B;AAED,cAAQ,IAAI,WAAW;AACvB,aAAO;AAAA,IACR,SAAQ,OAAO;AACd,cAAQ,IAAI,WAAW;AACvBA,oBAAc,MAAA,MAAA,SAAA,yCAAA,WAAW,KAAK;AAC9B,aAAO;IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,cAAc,WAAW,WAAW;AAClC,UAAM,UAAU,CAAA;AAChB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK,WAAW;AACpD,cAAQ,KAAK,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC;AAAA,IAC/C;AACD,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa,UAAU;AACrB,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,UAAI,KAAK,gBAAgB,IAAI,QAAQ,GAAG;AACtC,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAED,UAAI,KAAK,iBAAiB,IAAI,QAAQ,GAAG;AAEvC,cAAM,cAAc,MAAM;AACxB,cAAI,KAAK,gBAAgB,IAAI,QAAQ,GAAG;AACtC,oBAAQ,QAAQ;AAAA,UAC5B,OAAiB;AACL,uBAAW,aAAa,GAAG;AAAA,UAC5B;AAAA,QACX;AACQ;AACA;AAAA,MACD;AAED,WAAK,iBAAiB,IAAI,QAAQ;AAElCA,oBAAAA,MAAI,aAAa;AAAA,QACf,KAAK;AAAA,QACL,SAAS,MAAM;AACb,eAAK,iBAAiB,OAAO,QAAQ;AACrC,eAAK,gBAAgB,IAAI,QAAQ;AACjC,kBAAQ,QAAQ;AAAA,QACjB;AAAA,QACD,MAAM,CAAC,UAAU;AACf,eAAK,iBAAiB,OAAO,QAAQ;AACrC,iBAAO,KAAK;AAAA,QACb;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAAA,EACF;AACH;AAKA,MAAM,gBAAgB;AAAA,EACpB,cAAc;AACZ,SAAK,eAAe;AACpB,SAAK,kBAAkB;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACb,QAAI,KAAK;AAAiB;AAG1B,SAAK,kBAAkB,YAAY,MAAM;AACvC,WAAK,eAAc;AAAA,IACzB,GAAO,IAAI,KAAK,GAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKD,cAAc;AACZ,QAAI,KAAK,iBAAiB;AACxB,oBAAc,KAAK,eAAe;AAClC,WAAK,kBAAkB;AAAA,IACxB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,iBAAiB;AACf,UAAM,UAAU;AAChB,YAAQ,MAAM,gBAAgB;AAE9B,QAAI;AAEF,WAAK,aAAa,QAAQ,UAAQ;AAChC,YAAI;AACF;QACD,SAAQ,OAAO;AACdA,wBAAA,MAAA,MAAA,SAAA,yCAAc,aAAa,KAAK;AAAA,QACjC;AAAA,MACT,CAAO;AAGD,WAAK,kBAAiB;AAEtB,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAA,MAAA,OAAA,yCAAY,QAAQ;AAAA,MACrB;AAED,cAAQ,IAAI,gBAAgB;AAAA,IAC7B,SAAQ,OAAO;AACd,cAAQ,IAAI,gBAAgB;AAC5BA,oBAAc,MAAA,MAAA,SAAA,yCAAA,WAAW,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,eAAe,MAAM;AACnB,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK,aAAa,KAAK,IAAI;AAAA,IAC5B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB,MAAM;AACtB,UAAM,QAAQ,KAAK,aAAa,QAAQ,IAAI;AAC5C,QAAI,QAAQ,IAAI;AACd,WAAK,aAAa,OAAO,OAAO,CAAC;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AAClB,QAAI;AACF,YAAM,MAAM,KAAK;AACjB,YAAM,OAAO,CAAC,qBAAqB,YAAY;AAE/C,WAAK,QAAQ,SAAO;AAClB,YAAI;AACF,gBAAM,aAAaA,cAAAA,MAAI,eAAe,GAAG;AACzC,cAAI,cAAc,MAAM,YAAY;AAClCA,gCAAI,kBAAkB,GAAG;AAAA,UAC1B;AAAA,QACF,SAAQ,OAAO;AAAA,QAEf;AAAA,MACT,CAAO;AAAA,IACF,SAAQ,OAAO;AACdA,kFAAc,aAAa,KAAK;AAAA,IACjC;AAAA,EACF;AACH;AAGY,MAAC,qBAAqB,IAAI,mBAAqB;AACpD,MAAM,oBAAoB,IAAI;AACzB,MAAC,kBAAkB,IAAI,gBAAkB;AAK9C,SAAS,8BAA8B;AAC5C,MAAID,WAAO,QAAA,GAAI;AACbC,kBAAAA,MAAY,MAAA,OAAA,yCAAA,YAAY;AAAA,EACzB;AAGD,kBAAgB,aAAY;AAG5B,oBAAkB,yBAAwB,EAAG,MAAM,WAAS;AAC1DA,kBAAc,MAAA,MAAA,SAAA,yCAAA,cAAc,KAAK;AAAA,EACrC,CAAG;AAGD,kBAAgB,eAAe,MAAM;AAAA,EAOvC,CAAG;AACH;AAKO,SAAS,iCAAiC;AAC/C,kBAAgB,YAAW;AAE3B,MAAID,WAAO,QAAA,GAAI;AACbC,kBAAAA,MAAA,MAAA,OAAA,yCAAY,SAAS;AAAA,EACtB;AACH;;;;;"}