"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const utils_userStore = require("../../utils/user-store.js");
if (!Array) {
  const _easycom_uni_easyinput2 = common_vendor.resolveComponent("uni-easyinput");
  _easycom_uni_easyinput2();
}
const _easycom_uni_easyinput = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.js";
if (!Math) {
  _easycom_uni_easyinput();
}
const _sfc_main = {
  __name: "mediation_query",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const isRefreshing = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const isLoadingAnimate = common_vendor.ref(false);
    const isAuthenticated = common_vendor.ref(false);
    const mediationCase = common_vendor.ref("");
    const filteredOrderList = common_vendor.ref([]);
    const formData = common_vendor.reactive({
      name: "",
      // 姓名
      card: ""
      // 身份证号
    });
    const statusStyles = {
      draft: { bgColor: "#faad14" },
      pending_confirm: { bgColor: "#faad14" },
      initiated: { bgColor: "#1890ff" },
      in_progress: { bgColor: "#1890ff" },
      completed: { bgColor: "#52c41a" },
      closed: { bgColor: "#999" }
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:168", "调解查询页面已加载");
      checkAuthStatus();
      setTimeout(() => {
        isLoadingAnimate.value = true;
      }, 200);
    });
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:177", "调解查询页面显示");
      const detectAuthResult = common_vendor.index.getStorageSync("detect_auth_result");
      isAuthenticated.value = detectAuthResult === true;
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:181", "用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
    });
    const checkAuthStatus = async () => {
      const detectAuthResult = common_vendor.index.getStorageSync("detect_auth_result");
      if (detectAuthResult === false) {
        common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:190", "检测到 detect_auth_result 为 false，正在获取最新用户信息...");
        const result = await utils_api.api.user.getUserInfo();
        if (result && result.success && result.data) {
          const userData = result.data;
          const updatedUserInfo = {
            ...userData,
            // 确保 isVerified 字段被正确更新
            isVerified: userData.isVerified || userData.is_verified || false
          };
          utils_userStore.userStore.updateUserInfo(updatedUserInfo);
          if (typeof userData.detect_auth_result !== "undefined") {
            common_vendor.index.setStorageSync("detect_auth_result", userData.detect_auth_result);
            isAuthenticated.value = userData.detect_auth_result === true;
          } else {
            isAuthenticated.value = detectAuthResult === true;
          }
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:216", "用户信息已更新:", updatedUserInfo);
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:217", "用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
        } else {
          isAuthenticated.value = detectAuthResult === true;
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:221", "获取用户信息失败，使用缓存状态:", isAuthenticated.value ? "已认证" : "未认证");
        }
      } else {
        isAuthenticated.value = detectAuthResult === true;
        common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:226", "用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
      }
      if (isAuthenticated.value) {
        fetchOrderList();
      }
    };
    common_vendor.onPullDownRefresh(() => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:237", "触发下拉刷新");
      isRefreshing.value = true;
      checkAuthStatus();
    });
    const fetchOrderList = async (caseNumber = "") => {
      isLoading.value = true;
      if (!isRefreshing.value) {
        common_vendor.index.showLoading({ title: "加载中..." });
      }
      try {
        const params = {};
        if (caseNumber) {
          params.mediation_case_number = caseNumber;
        }
        const result = await utils_api.api.mediationQuery.getAuthenticatedList(params);
        if (!isRefreshing.value) {
          common_vendor.index.hideLoading();
        }
        if (result.state == "success") {
          filteredOrderList.value = result.data;
        } else {
          filteredOrderList.value = [];
          if (result && result.msg) {
            common_vendor.index.showToast({
              title: result.msg,
              icon: "none",
              duration: 2e3
            });
          }
        }
        isLoading.value = false;
        if (isRefreshing.value) {
          common_vendor.index.stopPullDownRefresh();
          isRefreshing.value = false;
          common_vendor.index.showToast({
            title: "刷新成功",
            icon: "success",
            duration: 1500
          });
        }
      } catch (error) {
        if (!isRefreshing.value) {
          common_vendor.index.hideLoading();
        }
        common_vendor.index.__f__("error", "at pages/mediation_query/mediation_query.vue:302", "获取案件列表失败:", error);
        orderList.value = [];
        isLoading.value = false;
        if (isRefreshing.value) {
          common_vendor.index.stopPullDownRefresh();
          isRefreshing.value = false;
        }
        common_vendor.index.showToast({
          title: error.msg || "获取数据失败",
          icon: "none",
          duration: 2e3
        });
      }
    };
    const handleSearch = async () => {
      if (isAuthenticated.value) {
        await fetchOrderList(searchKeyword.value.trim());
      } else {
        await handleIdentitySearch();
      }
    };
    const handleIdentitySearch = async () => {
      const name = formData.name.trim();
      const idCard = formData.card.trim();
      if (!name || !idCard) {
        common_vendor.index.showToast({
          title: "请输入完整的姓名和身份证号",
          icon: "none",
          duration: 1500
        });
        return;
      }
      common_vendor.index.showLoading({ title: "查询中..." });
      const result = await utils_api.api.mediationQuery.getCaseCountByIdentity({
        name,
        id_card: idCard
      });
      common_vendor.index.hideLoading();
      if (result.state == "success") {
        const count = result.data;
        mediationCase.value = count;
      } else {
        mediationCase.value = null;
        common_vendor.index.showToast({
          title: result.msg,
          icon: "none",
          duration: 2e3
        });
      }
    };
    const navigateToAuth = () => {
      const name = formData.name.trim();
      const idCard = formData.card.trim();
      common_vendor.index.navigateTo({
        url: `/pages/auth/auth?name=${encodeURIComponent(name)}&idCard=${encodeURIComponent(idCard)}`,
        success: () => {
          common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:375", "跳转到认证页面");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/mediation_query/mediation_query.vue:378", "跳转失败", err);
          common_vendor.index.showToast({
            title: "跳转失败",
            icon: "none"
          });
        }
      });
    };
    const handleRefresh = () => {
      if (isAuthenticated.value) {
        fetchOrderList();
      }
    };
    const navigateToDetail = (order) => {
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:418", "跳转到详情页，订单信息:", order);
      const caseNumber = order.case_number;
      const orderStatus = order.case_status_cn;
      const initiateDate = order.initiate_date;
      const closeDate = order.close_date;
      const mediationProgress = order.mediation_progress;
      const buildUrlParams = (paramsObj) => {
        const params = [];
        for (const [key, value] of Object.entries(paramsObj)) {
          if (value !== null && value !== void 0 && value !== "") {
            params.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
          }
        }
        return params.join("&");
      };
      const paramsString = buildUrlParams({
        case_number: caseNumber,
        initiate_date: initiateDate,
        close_date: closeDate,
        case_status_cn: orderStatus,
        mediation_progress: mediationProgress
      });
      common_vendor.index.__f__("log", "at pages/mediation_query/mediation_query.vue:446", "跳转参数:", {
        case_number: caseNumber,
        initiate_date: initiateDate,
        case_status_cn: orderStatus,
        mediation_progress: mediationProgress
      });
      if (mediationProgress === "调解确认") {
        common_vendor.index.navigateTo({
          url: `/pages/work_order_detail/work_order_detail?${paramsString}`
        });
      } else if (mediationProgress === "方案确认") {
        common_vendor.index.navigateTo({
          url: `/pages/solution_confirm/solution_confirm?${paramsString}`
        });
      } else if (mediationProgress === "协议签署") {
        common_vendor.index.navigateTo({
          url: `/pages/agreement_signing/agreement_signing?${paramsString}`
        });
      } else if (mediationProgress === "完成") {
        common_vendor.index.navigateTo({
          url: `/pages/case_completed/case_completed?${paramsString}`
        });
      } else if (mediationProgress === "已关闭") {
        common_vendor.index.navigateTo({
          url: `/pages/work_order_detail/work_order_detail?${paramsString}`
        });
      } else {
        common_vendor.index.showToast({
          title: "跳转失败",
          icon: "none"
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !isAuthenticated.value
      }, !isAuthenticated.value ? common_vendor.e({
        b: common_vendor.o(($event) => formData.name = $event),
        c: common_vendor.p({
          placeholder: "请输入真实姓名",
          modelValue: formData.name
        }),
        d: common_vendor.o(($event) => formData.card = $event),
        e: common_vendor.p({
          placeholder: "请输入身份证号",
          modelValue: formData.card
        }),
        f: common_vendor.o(handleSearch),
        g: mediationCase.value !== "" && (formData.name || formData.card)
      }, mediationCase.value !== "" && (formData.name || formData.card) ? {
        h: common_vendor.t(mediationCase.value),
        i: common_vendor.o(navigateToAuth),
        j: common_vendor.o(navigateToAuth)
      } : {}) : {}, {
        k: isAuthenticated.value
      }, isAuthenticated.value ? common_vendor.e({
        l: common_vendor.o(handleSearch),
        m: common_vendor.o(($event) => searchKeyword.value = $event),
        n: common_vendor.p({
          placeholder: "请输入调解案件号进行查询",
          confirmType: "search",
          suffixIcon: "search",
          clearable: true,
          modelValue: searchKeyword.value
        }),
        o: filteredOrderList.value.length > 0
      }, filteredOrderList.value.length > 0 ? {
        p: common_vendor.f(filteredOrderList.value, (order, k0, i0) => {
          var _a;
          return {
            a: common_vendor.t(order.case_number),
            b: common_vendor.t(order.case_status_cn),
            c: (_a = statusStyles[order.case_status]) == null ? void 0 : _a.bgColor,
            d: common_vendor.t(order.initiate_date),
            e: order.id,
            f: common_vendor.o(($event) => navigateToDetail(order), order.id)
          };
        }),
        q: isLoadingAnimate.value ? 1 : ""
      } : {}, {
        r: isLoading.value && !isRefreshing.value
      }, isLoading.value && !isRefreshing.value ? {} : {}, {
        s: filteredOrderList.value.length === 0 && !isLoading.value
      }, filteredOrderList.value.length === 0 && !isLoading.value ? {
        t: common_vendor.t(searchKeyword.value ? "未找到相关调解案件号" : "暂无信息"),
        v: common_vendor.o(handleRefresh)
      } : {}) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d56de527"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mediation_query/mediation_query.js.map
