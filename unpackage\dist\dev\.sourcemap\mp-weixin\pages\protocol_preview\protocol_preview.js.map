{"version": 3, "file": "protocol_preview.js", "sources": ["pages/protocol_preview/protocol_preview.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvdG9jb2xfcHJldmlldy9wcm90b2NvbF9wcmV2aWV3LnZ1ZQ"], "sourcesContent": ["<template>\r\n\t<view class=\"protocol-preview-container\">\r\n\t\t<!-- 预览选项 -->\r\n\t\t<!-- <view class=\"preview-options\">\r\n\t\t\t<button class=\"option-btn image-preview\" @click=\"viewAsImages\">\r\n\t\t\t\t<i class=\"fas fa-file-text\"></i>\r\n\t\t\t\t<text>在线预览</text>\r\n\t\t\t\t<text class=\"option-desc\">长图连续阅读</text>\r\n\t\t\t</button>\r\n\t\t</view> -->\r\n\t\t\r\n\t\t<!-- 长图预览模式 -->\r\n\t\t<view v-if=\"showImagePreview\" class=\"long-image-preview\">\r\n\t\t\t<view class=\"preview-header\">\r\n\t\t\t\t<view class=\"header-left\">\r\n\t\t\t\t\t<!-- API数据状态指示器 -->\r\n\t\t\t\t\t<view class=\"api-status\" v-if=\"solutionData\">\r\n\t\t\t\t\t\t<text class=\"status-text\">✓ 数据已加载</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"api-status error\" v-else-if=\"apiError\">\r\n\t\t\t\t\t\t<text class=\"status-text\">⚠ {{apiError}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"header-right\">\r\n\t\t\t\t\t<text class=\"progress-text\">{{Math.round(scrollProgress)}}%</text>\r\n\t\t\t\t\t<!-- 手动刷新按钮 -->\r\n\t\t\t\t\t<button class=\"refresh-btn\" @click=\"initializeApiData\" :disabled=\"isLoading\">\r\n\t\t\t\t\t\t<text class=\"refresh-icon\" :class=\"{'spinning': isLoading}\">⟳</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<!-- 调试按钮（开发时使用） -->\r\n\t\t\t\t\t<button class=\"debug-btn\" @click=\"showApiDataDebug\">\r\n\t\t\t\t\t\t<text class=\"debug-text\">调试</text>\r\n\t\t\t\t\t</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t\r\n\t\t\t<!-- 长图容器 -->\r\n\t\t\t<scroll-view \r\n\t\t\t\tclass=\"long-scroll-container\"\r\n\t\t\t\tscroll-y=\"true\"\r\n\t\t\t\t:scroll-top=\"scrollTop\"\r\n\t\t\t\t@scroll=\"onScroll\"\r\n\t\t\t\tenhanced\r\n\t\t\t\t:show-scrollbar=\"false\">\r\n\t\t\t\t\r\n\t\t\t\t<view class=\"content-container\">\r\n\t\t\t\t\t<!-- 加载状态 -->\r\n\t\t\t\t\t<view v-if=\"isLoading\" class=\"loading-container\">\r\n\t\t\t\t\t\t<view class=\"loading-icon\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-spinner fa-spin\"></i>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text class=\"loading-text\">\r\n\t\t\t\t\t\t\t{{ solutionData ? '正在加载PDF预览...' : '正在获取方案数据...' }}\r\n\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t<!-- API调用状态提示 -->\r\n\t\t\t\t\t\t<view v-if=\"!solutionData && !apiError\" class=\"api-loading-tip\">\r\n\t\t\t\t\t\t\t<text class=\"tip-text\">正在调用 api.solution.getDetails() 接口</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 错误状态显示 -->\r\n\t\t\t\t\t<view v-else-if=\"apiError && !solutionData\" class=\"error-container\">\r\n\t\t\t\t\t\t<view class=\"error-icon\">⚠</view>\r\n\t\t\t\t\t\t<text class=\"error-title\">数据获取失败</text>\r\n\t\t\t\t\t\t<text class=\"error-message\">{{apiError}}</text>\r\n\t\t\t\t\t\t<button class=\"retry-btn\" @click=\"initializeApiData\">\r\n\t\t\t\t\t\t\t<text class=\"retry-text\">重新获取</text>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 协议图片列表 -->\r\n\t\t\t\t\t<view v-else class=\"images-container\">\r\n\t\t\t\t\t\t<!-- SVG文件显示 -->\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(image, index) in svgImages\"\r\n\t\t\t\t\t\t\t:key=\"`svg-${index}`\"\r\n\t\t\t\t\t\t\tclass=\"svg-container\">\r\n\t\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\t\t:src=\"image.url\"\r\n\t\t\t\t\t\t\t\tmode=\"widthFix\"\r\n\t\t\t\t\t\t\t\tclass=\"protocol-page svg-image\"\r\n\t\t\t\t\t\t\t\t:class=\"{'first-page': index === 0}\"\r\n\t\t\t\t\t\t\t\t@load=\"onImageLoad(image.originalIndex)\"\r\n\t\t\t\t\t\t\t\t@error=\"onImageError(image.originalIndex)\"\r\n\t\t\t\t\t\t\t\tlazy-load />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 普通图片显示 -->\r\n\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t\tv-for=\"(image, index) in normalImages\"\r\n\t\t\t\t\t\t\t:key=\"`img-${index}`\"\r\n\t\t\t\t\t\t\t:src=\"image.url\"\r\n\t\t\t\t\t\t\tmode=\"widthFix\"\r\n\t\t\t\t\t\t\tclass=\"protocol-page\"\r\n\t\t\t\t\t\t\t:class=\"{'first-page': index === 0}\"\r\n\t\t\t\t\t\t\t@load=\"onImageLoad(image.originalIndex)\"\r\n\t\t\t\t\t\t\t@error=\"onImageError(image.originalIndex)\"\r\n\t\t\t\t\t\t\tlazy-load />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t\r\n\t\t\t\t\t<!-- 底部提示\r\n\t\t\t\t\t<view class=\"bottom-tip\">\r\n\t\t\t\t\t\t<view class=\"tip-line\"></view>\r\n\t\t\t\t\t\t<text class=\"tip-text\">协议内容已全部加载完成</text>\r\n\t\t\t\t\t\t<view class=\"action-buttons\">\r\n\t\t\t\t\t\t\t<button class=\"download-btn-small\" @click=\"downloadFile\">\r\n\t\t\t\t\t\t\t\t<i class=\"fas fa-download\"></i>\r\n\t\t\t\t\t\t\t\t下载协议\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</view>\r\n\t\t\t</scroll-view>\r\n\t\t\t\r\n\t\t\t<!-- 阅读进度指示器 -->\r\n\t\t\t<view class=\"progress-indicator\">\r\n\t\t\t\t<view class=\"progress-bar\">\r\n\t\t\t\t\t<view class=\"progress-fill\" :style=\"{width: scrollProgress + '%'}\"></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n// 导入API工具类\r\nimport { api } from '@/utils/api.js';\r\n\r\n// 接收的页面参数\r\nconst receivedFileUrl = ref('');\r\nconst receivedFileType = ref('');\r\nconst receivedFileName = ref('');\r\nconst receivedCaseNumber = ref('');\r\n\r\n// 文件信息（默认值，如果没有传递参数则使用）\r\nconst fileUrl = ref('http://*************:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx');\r\nconst pdfImagesBaseUrl = ref('http://*************:10010/pdf_images/scheme/485/');\r\nconst fileType = ref('pdf'); // 文件类型：pdf, svg, docx, etc.\r\nconst fileName = ref('调解协议.pdf');\r\n\r\n// API数据存储\r\nconst solutionData = ref(null);\r\nconst apiError = ref(null);\r\n\r\n// 预览相关\r\nconst showImagePreview = ref(false);\r\nconst pdfImages = ref([]);\r\nconst isLoading = ref(false);\r\nconst loadedImages = ref(new Set());\r\n\r\n// 滚动相关\r\nconst scrollTop = ref(0);\r\nconst scrollProgress = ref(0);\r\nconst containerHeight = ref(0);\r\nconst contentHeight = ref(0);\r\n\r\n// 计算属性：分离SVG和普通图片\r\nconst svgImages = computed(() => {\r\n\treturn pdfImages.value\r\n\t\t.map((image, index) => ({ ...image, originalIndex: index }))\r\n\t\t.filter(image => image.type === 'svg');\r\n});\r\n\r\nconst normalImages = computed(() => {\r\n\treturn pdfImages.value\r\n\t\t.map((image, index) => ({ ...image, originalIndex: index }))\r\n\t\t.filter(image => !image.type || image.type !== 'svg');\r\n});\r\n\r\n/**\r\n * 初始化API数据\r\n * 可以在页面加载时调用，用于预加载数据\r\n */\r\nconst initializeApiData = async () => {\r\n\ttry {\r\n\t\tconsole.log('开始初始化API数据...');\r\n\r\n\t\t// 清除之前的错误状态\r\n\t\tapiError.value = null;\r\n\r\n\t\t// 显示加载提示\r\n\t\tuni.showLoading({ title: '获取数据中...' });\r\n\r\n\t\tawait fetchSolutionDetails();\r\n\r\n\t\tuni.hideLoading();\r\n\t\tconsole.log('API数据初始化完成');\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '数据获取成功',\r\n\t\t\ticon: 'success',\r\n\t\t\tduration: 1500\r\n\t\t});\r\n\r\n\t\treturn true;\r\n\t} catch (error) {\r\n\t\tuni.hideLoading();\r\n\t\tconsole.error('API数据初始化失败:', error);\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '数据获取失败',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t});\r\n\r\n\t\treturn false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 处理PDF文件预览\r\n * 支持文件流处理和图片预览两种方式\r\n */\r\nconst handlePdfPreview = async () => {\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\tshowImagePreview.value = true;\r\n\r\n\t\t// 首先尝试获取API数据（只调用一次）\r\n\t\tif (!solutionData.value) {\r\n\t\t\tconsole.log('首次获取API数据...');\r\n\t\t\tawait fetchSolutionDetails();\r\n\t\t} else {\r\n\t\t\tconsole.log('使用已缓存的API数据');\r\n\t\t}\r\n\r\n\t\t// 检查API是否返回了文件流或PDF URL\r\n\t\tif (solutionData.value) {\r\n\t\t\t// 情况1：API返回了直接的PDF文件URL\r\n\t\t\tif (solutionData.value.pdfUrl) {\r\n\t\t\t\tconsole.log('发现直接PDF文件URL，尝试直接预览');\r\n\t\t\t\tfileUrl.value = solutionData.value.pdfUrl;\r\n\t\t\t\tawait handleDirectPdfPreview();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 情况2：API返回了文件流数据\r\n\t\t\tif (solutionData.value.fileStream || solutionData.value.fileData) {\r\n\t\t\t\tconsole.log('发现文件流数据，处理文件流预览');\r\n\t\t\t\tawait handleFileStreamPreview();\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\r\n\t\t\t// 情况3：API返回了图片列表\r\n\t\t\tif (solutionData.value.pdfImages && solutionData.value.pdfImages.length > 0) {\r\n\t\t\t\tconsole.log('发现PDF图片列表，使用图片预览');\r\n\t\t\t\tpdfImages.value = solutionData.value.pdfImages;\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 默认情况：使用图片预览模式\r\n\t\tconsole.log('使用默认图片预览模式');\r\n\t\tconst images = await getPdfImages();\r\n\t\tif (images.length > 0) {\r\n\t\t\tpdfImages.value = images;\r\n\t\t} else {\r\n\t\t\tthrow new Error('无法获取预览内容');\r\n\t\t}\r\n\r\n\t} catch (error) {\r\n\t\tconsole.error('PDF预览处理失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '预览加载失败',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t});\r\n\t\tcloseImagePreview();\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 处理直接PDF文件预览\r\n */\r\nconst handleDirectPdfPreview = async () => {\r\n\ttry {\r\n\t\tconsole.log('开始直接PDF预览，文件URL:', fileUrl.value);\r\n\r\n\t\t// 尝试直接打开PDF文件\r\n\t\tuni.downloadFile({\r\n\t\t\turl: fileUrl.value,\r\n\t\t\tsuccess: (res) => {\r\n\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\tshowMenu: true,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tconsole.log('PDF文件直接预览成功');\r\n\t\t\t\t\t\t\t// 直接预览成功，关闭当前预览界面\r\n\t\t\t\t\t\t\tcloseImagePreview();\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\tconsole.log('PDF直接预览失败，转为图片预览模式:', error);\r\n\t\t\t\t\t\t\t// 如果直接预览失败，转为图片预览\r\n\t\t\t\t\t\t\tfallbackToImagePreview();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.log('PDF下载失败，转为图片预览模式');\r\n\t\t\t\t\tfallbackToImagePreview();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfail: (error) => {\r\n\t\t\t\tconsole.log('PDF下载失败，转为图片预览模式:', error);\r\n\t\t\t\tfallbackToImagePreview();\r\n\t\t\t}\r\n\t\t});\r\n\t} catch (error) {\r\n\t\tconsole.error('直接PDF预览失败:', error);\r\n\t\tfallbackToImagePreview();\r\n\t}\r\n};\r\n\r\n/**\r\n * 处理文件流预览\r\n */\r\nconst handleFileStreamPreview = async () => {\r\n\ttry {\r\n\t\tconsole.log('开始处理文件流预览');\r\n\t\tconsole.log('API返回的数据结构:', solutionData.value);\r\n\r\n\t\t// 检查多种可能的文件流数据字段\r\n\t\tconst fileData = solutionData.value.fileStream ||\r\n\t\t                 solutionData.value.fileData ||\r\n\t\t                 solutionData.value.data ||\r\n\t\t                 solutionData.value.file ||\r\n\t\t                 solutionData.value;\r\n\r\n\t\tif (!fileData) {\r\n\t\t\tconsole.log('未找到文件流数据，转为图片预览模式');\r\n\t\t\tfallbackToImagePreview();\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\tconsole.log('文件流数据类型:', typeof fileData);\r\n\t\tconsole.log('文件流数据长度:', fileData.length || 'unknown');\r\n\r\n\t\t// 将文件流转换为临时文件或获取文件URL\r\n\t\tconst filePath = await convertStreamToTempFile(fileData);\r\n\r\n\t\t// 如果返回的是URL，先下载再打开\r\n\t\tif (typeof filePath === 'string' && (filePath.startsWith('http') || filePath.startsWith('/'))) {\r\n\t\t\tconsole.log('获取到文件URL，开始下载:', filePath);\r\n\r\n\t\t\tuni.downloadFile({\r\n\t\t\t\turl: filePath,\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t\t\tuni.openDocument({\r\n\t\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\t\tshowMenu: true,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tconsole.log('文件流URL预览成功');\r\n\t\t\t\t\t\t\t\tcloseImagePreview();\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\t\tconsole.log('文件流URL预览失败，转为图片预览模式:', error);\r\n\t\t\t\t\t\t\t\tfallbackToImagePreview();\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.log('文件下载失败，转为图片预览模式');\r\n\t\t\t\t\t\tfallbackToImagePreview();\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (error) => {\r\n\t\t\t\t\tconsole.log('文件下载失败，转为图片预览模式:', error);\r\n\t\t\t\t\tfallbackToImagePreview();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\t// 如果是本地临时文件路径，直接打开\r\n\t\t\tconsole.log('获取到临时文件路径，直接打开:', filePath);\r\n\r\n\t\t\tuni.openDocument({\r\n\t\t\t\tfilePath: filePath,\r\n\t\t\t\tshowMenu: true,\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log('文件流预览成功');\r\n\t\t\t\t\tcloseImagePreview();\r\n\t\t\t\t},\r\n\t\t\t\tfail: (error) => {\r\n\t\t\t\t\tconsole.log('文件流预览失败，转为图片预览模式:', error);\r\n\t\t\t\t\tfallbackToImagePreview();\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t} catch (error) {\r\n\t\tconsole.error('文件流预览失败:', error);\r\n\t\tfallbackToImagePreview();\r\n\t}\r\n};\r\n\r\n/**\r\n * 回退到图片预览模式\r\n */\r\nconst fallbackToImagePreview = async () => {\r\n\ttry {\r\n\t\tconsole.log('回退到图片预览模式');\r\n\t\tconst images = await getPdfImages();\r\n\t\tif (images.length > 0) {\r\n\t\t\tpdfImages.value = images;\r\n\t\t} else {\r\n\t\t\tthrow new Error('无法获取预览图片');\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('图片预览回退失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '预览失败',\r\n\t\t\ticon: 'none'\r\n\t\t});\r\n\t\tcloseImagePreview();\r\n\t}\r\n};\r\n\r\n/**\r\n * 将文件流转换为临时文件\r\n */\r\nconst convertStreamToTempFile = async (fileData) => {\r\n\ttry {\r\n\t\tconsole.log('开始转换文件流，数据类型:', typeof fileData);\r\n\r\n\t\t// 情况1：base64格式的文件流\r\n\t\tif (typeof fileData === 'string' && fileData.startsWith('data:')) {\r\n\t\t\tconsole.log('检测到base64文件流数据');\r\n\r\n\t\t\tconst base64Data = fileData.split(',')[1];\r\n\t\t\tconst tempFileName = `temp_pdf_${Date.now()}.pdf`;\r\n\t\t\tconst tempFilePath = `${uni.env.USER_DATA_PATH}/${tempFileName}`;\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tuni.getFileSystemManager().writeFile({\r\n\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\tdata: base64Data,\r\n\t\t\t\t\tencoding: 'base64',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('base64文件写入成功:', tempFilePath);\r\n\t\t\t\t\t\tresolve(tempFilePath);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.error('base64文件写入失败:', error);\r\n\t\t\t\t\t\treject(new Error(`文件写入失败: ${error.errMsg || error.message}`));\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// 情况2：纯base64字符串（无data:前缀）\r\n\t\tif (typeof fileData === 'string' && /^[A-Za-z0-9+/]+=*$/.test(fileData)) {\r\n\t\t\tconsole.log('检测到纯base64字符串数据');\r\n\r\n\t\t\tconst tempFileName = `temp_pdf_${Date.now()}.pdf`;\r\n\t\t\tconst tempFilePath = `${uni.env.USER_DATA_PATH}/${tempFileName}`;\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tuni.getFileSystemManager().writeFile({\r\n\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\tdata: fileData,\r\n\t\t\t\t\tencoding: 'base64',\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('纯base64文件写入成功:', tempFilePath);\r\n\t\t\t\t\t\tresolve(tempFilePath);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.error('纯base64文件写入失败:', error);\r\n\t\t\t\t\t\treject(new Error(`文件写入失败: ${error.errMsg || error.message}`));\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// 情况3：ArrayBuffer格式\r\n\t\tif (fileData instanceof ArrayBuffer) {\r\n\t\t\tconsole.log('检测到ArrayBuffer文件流数据');\r\n\r\n\t\t\tconst tempFileName = `temp_pdf_${Date.now()}.pdf`;\r\n\t\t\tconst tempFilePath = `${uni.env.USER_DATA_PATH}/${tempFileName}`;\r\n\r\n\t\t\treturn new Promise((resolve, reject) => {\r\n\t\t\t\tuni.getFileSystemManager().writeFile({\r\n\t\t\t\t\tfilePath: tempFilePath,\r\n\t\t\t\t\tdata: fileData,\r\n\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\tconsole.log('ArrayBuffer文件写入成功:', tempFilePath);\r\n\t\t\t\t\t\tresolve(tempFilePath);\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\tconsole.error('ArrayBuffer文件写入失败:', error);\r\n\t\t\t\t\t\treject(new Error(`文件写入失败: ${error.errMsg || error.message}`));\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t// 情况4：如果是字符串但不是base64，可能是文件URL\r\n\t\tif (typeof fileData === 'string' && (fileData.startsWith('http') || fileData.startsWith('/'))) {\r\n\t\t\tconsole.log('检测到文件URL，直接返回:', fileData);\r\n\t\t\treturn fileData;\r\n\t\t}\r\n\r\n\t\t// 情况5：如果是对象，可能包含文件URL或其他信息\r\n\t\tif (typeof fileData === 'object' && fileData !== null) {\r\n\t\t\tconsole.log('检测到对象类型的文件数据:', fileData);\r\n\r\n\t\t\t// 尝试从对象中提取文件URL\r\n\t\t\tconst possibleUrl = fileData.url || fileData.fileUrl || fileData.downloadUrl || fileData.path;\r\n\t\t\tif (possibleUrl && typeof possibleUrl === 'string') {\r\n\t\t\t\tconsole.log('从对象中提取到文件URL:', possibleUrl);\r\n\t\t\t\treturn possibleUrl;\r\n\t\t\t}\r\n\r\n\t\t\t// 尝试从对象中提取base64数据\r\n\t\t\tconst possibleBase64 = fileData.base64 || fileData.data || fileData.content;\r\n\t\t\tif (possibleBase64 && typeof possibleBase64 === 'string') {\r\n\t\t\t\tconsole.log('从对象中提取到base64数据');\r\n\t\t\t\treturn await convertStreamToTempFile(possibleBase64);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t// 不支持的格式\r\n\t\tconsole.error('不支持的文件流格式:', typeof fileData, fileData);\r\n\t\tthrow new Error(`不支持的文件流格式: ${typeof fileData}`);\r\n\r\n\t} catch (error) {\r\n\t\tconsole.error('文件流转换失败:', error);\r\n\t\tthrow error;\r\n\t}\r\n};\r\n\r\n/**\r\n * 图片预览模式\r\n */\r\nconst viewAsImages = async () => {\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\tshowImagePreview.value = true;\r\n\t\t\r\n\t\t// 获取PDF转换的图片列表\r\n\t\tconst images = await getPdfImages();\r\n\t\tif (images.length > 0) {\r\n\t\t\tpdfImages.value = images;\r\n\t\t} else {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '暂无可预览内容',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\tcloseImagePreview();\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('加载图片预览失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '加载失败，请稍后重试',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t\tcloseImagePreview();\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 调用API获取方案详情数据\r\n */\r\nconst fetchSolutionDetails = async () => {\r\n\ttry {\r\n\t\tconsole.log('开始调用 api.solution.getDetails() 接口...');\r\n\r\n\t\t// 调用API获取数据\r\n\t\tconst result = await api.solution.getDetails();\r\n\r\n\t\tif (result.state === \"success\" && result.data) {\r\n\t\t\tsolutionData.value = result.data;\r\n\t\t\tconsole.log('方案详情数据获取成功:', result.data);\r\n\r\n\t\t\t// 详细日志：分析API返回的数据结构\r\n\t\t\tconsole.log('=== API数据结构分析 ===');\r\n\t\t\tconsole.log('数据类型:', typeof result.data);\r\n\t\t\tconsole.log('数据键值:', Object.keys(result.data));\r\n\r\n\t\t\t// 检查可能的文件相关字段\r\n\t\t\tconst fileFields = ['fileStream', 'fileData', 'data', 'file', 'pdfUrl', 'url', 'downloadUrl', 'pdfInfo', 'pdfImages'];\r\n\t\t\tfileFields.forEach(field => {\r\n\t\t\t\tif (result.data[field] !== undefined) {\r\n\t\t\t\t\tconsole.log(`发现字段 ${field}:`, typeof result.data[field], result.data[field]);\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tconsole.log('=== 数据结构分析结束 ===');\r\n\r\n\t\t\t// 如果API返回了PDF相关信息，更新文件信息\r\n\t\t\tif (result.data.pdfInfo) {\r\n\t\t\t\tconst pdfInfo = result.data.pdfInfo;\r\n\t\t\t\tif (pdfInfo.fileUrl) {\r\n\t\t\t\t\tfileUrl.value = pdfInfo.fileUrl;\r\n\t\t\t\t}\r\n\t\t\t\tif (pdfInfo.fileName) {\r\n\t\t\t\t\tfileName.value = pdfInfo.fileName;\r\n\t\t\t\t}\r\n\t\t\t\tif (pdfInfo.baseUrl) {\r\n\t\t\t\t\tpdfImagesBaseUrl.value = pdfInfo.baseUrl;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\treturn result.data;\r\n\t\t} else {\r\n\t\t\tconst errorMsg = result.msg || '获取方案详情失败';\r\n\t\t\tconsole.error('API调用失败:', errorMsg);\r\n\t\t\tapiError.value = errorMsg;\r\n\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: errorMsg,\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 2000\r\n\t\t\t});\r\n\r\n\t\t\tthrow new Error(errorMsg);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('调用 api.solution.getDetails() 失败:', error);\r\n\t\tapiError.value = error.message || '网络请求失败';\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '获取数据失败，请检查网络连接',\r\n\t\t\ticon: 'none',\r\n\t\t\tduration: 2000\r\n\t\t});\r\n\r\n\t\tthrow error;\r\n\t}\r\n};\r\n\r\n/**\r\n * 获取PDF转换的图片\r\n * 注意：此函数不再调用API，假设API数据已经通过其他方式获取\r\n */\r\nconst getPdfImages = async () => {\r\n\ttry {\r\n\t\t// 如果API返回了图片列表，使用API数据\r\n\t\tif (solutionData.value && solutionData.value.pdfImages) {\r\n\t\t\tconsole.log('使用API返回的PDF图片列表');\r\n\t\t\treturn solutionData.value.pdfImages;\r\n\t\t}\r\n\r\n\t\t// 否则使用默认的图片列表生成逻辑\r\n\t\tconsole.log('使用默认PDF图片列表生成逻辑');\r\n\r\n\t\t// 模拟加载延时\r\n\t\tawait new Promise(resolve => setTimeout(resolve, 1000));\r\n\r\n\t\t// 生成默认图片列表（基于案件号或默认值）\r\n\t\tconst pageCount = solutionData.value?.pageCount || 5;\r\n\t\tconst images = [];\r\n\r\n\t\tfor (let i = 1; i <= pageCount; i++) {\r\n\t\t\timages.push({\r\n\t\t\t\turl: `${pdfImagesBaseUrl.value}page_${i}.jpg`,\r\n\t\t\t\tpage: i\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\treturn images;\r\n\t} catch (error) {\r\n\t\tconsole.error('获取PDF图片失败:', error);\r\n\r\n\t\t// 如果出错，使用模拟数据\r\n\t\tconsole.log('使用模拟数据');\r\n\t\tawait new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n\t\treturn [\r\n\t\t\t{ url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },\r\n\t\t\t{ url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },\r\n\t\t\t{ url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },\r\n\t\t\t{ url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },\r\n\t\t\t{ url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }\r\n\t\t];\r\n\t}\r\n};\r\n\r\n/**\r\n * 滚动事件处理\r\n */\r\nconst onScroll = (e) => {\r\n\tconst { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;\r\n\t\r\n\t// 更新滚动位置\r\n\tscrollTop.value = currentScrollTop;\r\n\t\r\n\t// 计算阅读进度\r\n\tif (scrollHeight > clientHeight) {\r\n\t\tscrollProgress.value = (currentScrollTop / (scrollHeight - clientHeight)) * 100;\r\n\t}\r\n};\r\n\r\n/**\r\n * 图片加载成功\r\n */\r\nconst onImageLoad = (index) => {\r\n\tloadedImages.value.add(index);\r\n\tconsole.log(`图片 ${index + 1} 加载成功`);\r\n};\r\n\r\n/**\r\n * 图片加载失败\r\n */\r\nconst onImageError = (index) => {\r\n\tconsole.error(`图片 ${index + 1} 加载失败`);\r\n\tuni.showToast({\r\n\t\ttitle: `第${index + 1}页加载失败`,\r\n\t\ticon: 'none',\r\n\t\tduration: 2000\r\n\t});\r\n};\r\n\r\n/**\r\n * 关闭图片预览\r\n */\r\nconst closeImagePreview = () => {\r\n\tshowImagePreview.value = false;\r\n\tscrollTop.value = 0;\r\n\tscrollProgress.value = 0;\r\n\tloadedImages.value.clear();\r\n};\r\n\r\n/**\r\n * 显示API数据调试信息\r\n */\r\nconst showApiDataDebug = () => {\r\n\tconsole.log('=== 显示API调试信息 ===');\r\n\r\n\tlet debugInfo = '';\r\n\r\n\tif (solutionData.value) {\r\n\t\tdebugInfo = `API数据已获取\\n`;\r\n\t\tdebugInfo += `数据类型: ${typeof solutionData.value}\\n`;\r\n\t\tdebugInfo += `数据键值: ${Object.keys(solutionData.value).join(', ')}\\n\\n`;\r\n\r\n\t\t// 检查文件相关字段\r\n\t\tconst fileFields = ['fileStream', 'fileData', 'data', 'file', 'pdfUrl', 'url', 'downloadUrl'];\r\n\t\tfileFields.forEach(field => {\r\n\t\t\tif (solutionData.value[field] !== undefined) {\r\n\t\t\t\tconst value = solutionData.value[field];\r\n\t\t\t\tdebugInfo += `${field}: ${typeof value} (${value?.length || 'unknown'})\\n`;\r\n\t\t\t}\r\n\t\t});\r\n\t} else if (apiError.value) {\r\n\t\tdebugInfo = `API调用失败\\n错误信息: ${apiError.value}`;\r\n\t} else {\r\n\t\tdebugInfo = 'API数据尚未获取';\r\n\t}\r\n\r\n\tuni.showModal({\r\n\t\ttitle: 'API数据调试信息',\r\n\t\tcontent: debugInfo,\r\n\t\tshowCancel: true,\r\n\t\tcancelText: '关闭',\r\n\t\tconfirmText: '重新获取',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\tinitializeApiData();\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n\r\n\tconsole.log('=== API调试信息显示完成 ===');\r\n};\r\n\r\n/**\r\n * SVG文件预览\r\n */\r\nconst showSvgPreview = () => {\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\tshowImagePreview.value = true;\r\n\r\n\t\t// 对于SVG文件，直接显示\r\n\t\tpdfImages.value = [{\r\n\t\t\turl: fileUrl.value,\r\n\t\t\tpage: 1,\r\n\t\t\ttype: 'svg'\r\n\t\t}];\r\n\r\n\t\tconsole.log('SVG文件预览准备完成:', fileUrl.value);\r\n\t} catch (error) {\r\n\t\tconsole.error('SVG预览失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: 'SVG预览失败',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 检测文件类型\r\n */\r\nconst detectFileType = (url) => {\r\n\tif (!url) return 'unknown';\r\n\r\n\tconst extension = url.split('.').pop()?.toLowerCase();\r\n\tswitch (extension) {\r\n\t\tcase 'svg':\r\n\t\t\treturn 'svg';\r\n\t\tcase 'pdf':\r\n\t\t\treturn 'pdf';\r\n\t\tcase 'docx':\r\n\t\tcase 'doc':\r\n\t\t\treturn 'docx';\r\n\t\tcase 'jpg':\r\n\t\tcase 'jpeg':\r\n\t\tcase 'png':\r\n\t\tcase 'gif':\r\n\t\t\treturn 'image';\r\n\t\tdefault:\r\n\t\t\treturn 'unknown';\r\n\t}\r\n};\r\n\r\n/**\r\n * 下载文件\r\n */\r\nconst downloadFile = () => {\r\n\tuni.showLoading({ title: '正在下载...' });\r\n\r\n\tuni.downloadFile({\r\n\t\turl: fileUrl.value,\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.statusCode === 200) {\r\n\t\t\t\t// 对于SVG文件，可以直接保存到相册（如果是图片格式）\r\n\t\t\t\tif (fileType.value === 'svg') {\r\n\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\tfilePath: res.tempFilePath,\r\n\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: (error) => {\r\n\t\t\t\t\t\t\tconsole.error('保存SVG失败:', error);\r\n\t\t\t\t\t\t\t// 如果保存失败，尝试用文档方式打开\r\n\t\t\t\t\t\t\topenDocument(res.tempFilePath);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 其他文件类型用文档方式打开\r\n\t\t\t\t\topenDocument(res.tempFilePath);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tfail: (error) => {\r\n\t\t\tuni.hideLoading();\r\n\t\t\tconsole.error('下载失败:', error);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '下载失败，请检查网络连接',\r\n\t\t\t\ticon: 'error'\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n/**\r\n * 打开文档\r\n */\r\nconst openDocument = (filePath) => {\r\n\tuni.openDocument({\r\n\t\tfilePath: filePath,\r\n\t\tshowMenu: true,\r\n\t\tsuccess: () => {\r\n\t\t\tuni.hideLoading();\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '打开成功',\r\n\t\t\t\ticon: 'success'\r\n\t\t\t});\r\n\t\t},\r\n\t\tfail: (error) => {\r\n\t\t\tuni.hideLoading();\r\n\t\t\tconsole.error('打开文档失败:', error);\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '打开失败，请检查是否安装相应的阅读器',\r\n\t\t\t\ticon: 'none',\r\n\t\t\t\tduration: 3000\r\n\t\t\t});\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 页面加载时获取参数\r\nonLoad((options) => {\r\n\tconsole.log('预览页面参数:', options);\r\n\r\n\t// 获取文件URL参数\r\n\tif (options.fileUrl) {\r\n\t\ttry {\r\n\t\t\treceivedFileUrl.value = decodeURIComponent(options.fileUrl);\r\n\t\t\tfileUrl.value = receivedFileUrl.value;\r\n\t\t\tconsole.log('接收到文件URL:', fileUrl.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('fileUrl参数解码失败:', error);\r\n\t\t\treceivedFileUrl.value = options.fileUrl;\r\n\t\t\tfileUrl.value = options.fileUrl;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取文件类型参数\r\n\tif (options.fileType) {\r\n\t\ttry {\r\n\t\t\treceivedFileType.value = decodeURIComponent(options.fileType);\r\n\t\t\tfileType.value = receivedFileType.value;\r\n\t\t\tconsole.log('接收到文件类型:', fileType.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('fileType参数解码失败:', error);\r\n\t\t\treceivedFileType.value = options.fileType;\r\n\t\t\tfileType.value = options.fileType;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取文件名参数\r\n\tif (options.fileName) {\r\n\t\ttry {\r\n\t\t\treceivedFileName.value = decodeURIComponent(options.fileName);\r\n\t\t\tfileName.value = receivedFileName.value;\r\n\t\t\tconsole.log('接收到文件名:', fileName.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('fileName参数解码失败:', error);\r\n\t\t\treceivedFileName.value = options.fileName;\r\n\t\t\tfileName.value = options.fileName;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取案件号参数\r\n\tif (options.caseNumber) {\r\n\t\ttry {\r\n\t\t\treceivedCaseNumber.value = decodeURIComponent(options.caseNumber);\r\n\t\t\tconsole.log('接收到案件号:', receivedCaseNumber.value);\r\n\t\t\t// 根据案件号构建PDF图片基础URL\r\n\t\t\tpdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${receivedCaseNumber.value}/`;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('caseNumber参数解码失败:', error);\r\n\t\t\treceivedCaseNumber.value = options.caseNumber;\r\n\t\t\tpdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${options.caseNumber}/`;\r\n\t\t}\r\n\t}\r\n});\r\n\r\nonMounted(async () => {\r\n\t// 根据文件类型设置页面标题\r\n\tconst title = fileName.value ? `预览 - ${fileName.value}` : '文件预览';\r\n\tuni.setNavigationBarTitle({ title });\r\n\r\n\ttry {\r\n\t\t// 页面加载时先获取API数据（统一调用一次）\r\n\t\tconsole.log('页面加载完成，开始获取方案详情数据...');\r\n\t\tawait fetchSolutionDetails();\r\n\r\n\t\t// 根据文件类型选择预览方式\r\n\t\tif (fileType.value === 'svg') {\r\n\t\t\t// SVG文件直接显示\r\n\t\t\tshowSvgPreview();\r\n\t\t} else if (fileType.value === 'pdf' || fileType.value === 'docx') {\r\n\t\t\t// PDF文件使用增强的预览处理（此时API数据已获取）\r\n\t\t\thandlePdfPreview();\r\n\t\t} else {\r\n\t\t\t// 其他文件类型尝试图片预览\r\n\t\t\tviewAsImages();\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('页面初始化过程中发生错误:', error);\r\n\t\t// 即使API调用失败，也继续显示预览功能\r\n\t\tconsole.log('API调用失败，使用默认预览方式');\r\n\r\n\t\tif (fileType.value === 'svg') {\r\n\t\t\tshowSvgPreview();\r\n\t\t} else {\r\n\t\t\t// 对于PDF，直接使用图片预览模式\r\n\t\t\tviewAsImages();\r\n\t\t}\r\n\t}\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.protocol-preview-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.preview-options {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.option-btn {\r\n\tflex: 1;\r\n\tbackground: linear-gradient(135deg, #fff 0%, #f8fafc 100%);\r\n\tborder: 2rpx solid #e2e8f0;\r\n\tborder-radius: 16rpx;\r\n\tpadding: 40rpx 20rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tbox-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n\ttransition: all 0.3s ease;\r\n\t\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n\t\r\n\t.fas {\r\n\t\tfont-size: 48rpx;\r\n\t\tcolor: #3b7eeb;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\t\r\n\ttext {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\t\r\n\t.option-desc {\r\n\t\tfont-size: 24rpx !important;\r\n\t\tcolor: #666 !important;\r\n\t\tfont-weight: normal !important;\r\n\t\tmargin-bottom: 0 !important;\r\n\t}\r\n}\r\n\r\n/* 长图预览样式 */\r\n.long-image-preview {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100vw;\r\n\theight: 100vh;\r\n\tbackground-color: #fff;\r\n\tz-index: 9999;\r\n}\r\n\r\n.preview-header {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 88rpx;\r\n\tbackground: rgba(255, 255, 255, 0.95);\r\n\tbackdrop-filter: blur(10rpx);\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\tpadding: 0 30rpx;\r\n\tz-index: 10000;\r\n}\r\n\r\n.header-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.api-status {\r\n\tpadding: 8rpx 16rpx;\r\n\tborder-radius: 12rpx;\r\n\tbackground-color: #e8f5e8;\r\n\r\n\t&.error {\r\n\t\tbackground-color: #ffeaea;\r\n\t}\r\n\r\n\t.status-text {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #4caf50;\r\n\r\n\t\t.error & {\r\n\t\t\tcolor: #f44336;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.header-right {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.refresh-btn {\r\n\tbackground: transparent;\r\n\tborder: 2rpx solid #e0e0e0;\r\n\tborder-radius: 50%;\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\r\n\t&:disabled {\r\n\t\topacity: 0.6;\r\n\t}\r\n\r\n\t&:active:not(:disabled) {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n}\r\n\r\n.refresh-icon {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\ttransition: transform 0.3s ease;\r\n\r\n\t&.spinning {\r\n\t\tanimation: spin 1s linear infinite;\r\n\t}\r\n}\r\n\r\n@keyframes spin {\r\n\tfrom { transform: rotate(0deg); }\r\n\tto { transform: rotate(360deg); }\r\n}\r\n\r\n.debug-btn {\r\n\tbackground-color: #ff9800;\r\n\tborder: none;\r\n\tborder-radius: 8rpx;\r\n\tpadding: 8rpx 16rpx;\r\n\tmargin-left: 10rpx;\r\n\r\n\t.debug-text {\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #fff;\r\n\t}\r\n\r\n\t&:active {\r\n\t\ttransform: scale(0.95);\r\n\t}\r\n}\r\n\r\n/* .header-left {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 20rpx;\r\n}\r\n\r\n.back-btn {\r\n\tbackground: transparent;\r\n\tborder: none;\r\n\tcolor: #333;\r\n\tfont-size: 32rpx;\r\n\tpadding: 8rpx;\r\n}\r\n\r\n.doc-title {\r\n\tfont-size: 30rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n} */\r\n\r\n.progress-text {\r\n\tfont-size: 26rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n.long-scroll-container {\r\n\twidth: 100%;\r\n\theight: 100vh;\r\n\tpadding-top: 88rpx;\r\n}\r\n\r\n.content-container {\r\n\tmin-height: calc(100vh - 88rpx);\r\n}\r\n\r\n.loading-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\theight: 60vh;\r\n\t\r\n\t.loading-icon {\r\n\t\tmargin-bottom: 30rpx;\r\n\t\t\r\n\t\t.fas {\r\n\t\t\tfont-size: 48rpx;\r\n\t\t\tcolor: #3b7eeb;\r\n\t\t}\r\n\t}\r\n\t\r\n\t.loading-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.api-loading-tip {\r\n\t\tpadding: 20rpx;\r\n\t\tbackground-color: #f0f8ff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tborder: 2rpx solid #e3f2fd;\r\n\r\n\t\t.tip-text {\r\n\t\t\tfont-size: 24rpx;\r\n\t\t\tcolor: #1976d2;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.error-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\theight: 60vh;\r\n\tpadding: 40rpx;\r\n\r\n\t.error-icon {\r\n\t\tfont-size: 80rpx;\r\n\t\tcolor: #f44336;\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.error-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.error-message {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #666;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 40rpx;\r\n\t\tline-height: 1.5;\r\n\t}\r\n\r\n\t.retry-btn {\r\n\t\tbackground-color: #3b7eeb;\r\n\t\tcolor: #fff;\r\n\t\tborder: none;\r\n\t\tborder-radius: 12rpx;\r\n\t\tpadding: 24rpx 48rpx;\r\n\t\tfont-size: 28rpx;\r\n\r\n\t\t&:active {\r\n\t\t\ttransform: scale(0.98);\r\n\t\t}\r\n\r\n\t\t.retry-text {\r\n\t\t\tcolor: #fff;\r\n\t\t}\r\n\t}\r\n}\r\n\r\n.images-container {\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.protocol-page {\r\n\twidth: 100%;\r\n\tdisplay: block;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n\r\n\t&.first-page {\r\n\t\tborder-top: none;\r\n\t}\r\n}\r\n\r\n/* SVG容器样式 */\r\n.svg-container {\r\n\twidth: 100%;\r\n\tbackground-color: #fff;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.svg-image {\r\n\twidth: 100%;\r\n\theight: auto;\r\n\tdisplay: block;\r\n}\r\n\r\n.bottom-tip {\r\n\tpadding: 60rpx 40rpx;\r\n\ttext-align: center;\r\n\tbackground-color: #f9f9f9;\r\n\t\r\n\t.tip-line {\r\n\t\twidth: 100rpx;\r\n\t\theight: 4rpx;\r\n\t\tbackground-color: #e0e0e0;\r\n\t\tmargin: 0 auto 30rpx;\r\n\t\tborder-radius: 2rpx;\r\n\t}\r\n\t\r\n\t.tip-text {\r\n\t\tdisplay: block;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #666;\r\n\t\tmargin-bottom: 40rpx;\r\n\t}\r\n}\r\n\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tjustify-content: center;\r\n}\r\n\r\n.download-btn-small {\r\n\tbackground-color: #3b7eeb;\r\n\tcolor: #fff;\r\n\tborder: none;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 20rpx 40rpx;\r\n\tfont-size: 28rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tgap: 12rpx;\r\n\t\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n}\r\n\r\n/* 进度指示器 */\r\n.progress-indicator {\r\n\tposition: fixed;\r\n\tbottom: 0;\r\n\tleft: 0;\r\n\twidth: 100%;\r\n\theight: 6rpx;\r\n\tbackground-color: rgba(0, 0, 0, 0.1);\r\n\tz-index: 10000;\r\n}\r\n\r\n.progress-bar {\r\n\twidth: 100%;\r\n\theight: 100%;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-fill {\r\n\theight: 100%;\r\n\tbackground: linear-gradient(90deg, #3b7eeb 0%, #2c62c9 100%);\r\n\ttransition: width 0.3s ease;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/protocol_preview/protocol_preview.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "api", "onLoad", "onMounted"], "mappings": ";;;;;;AAkIA,UAAM,kBAAkBA,cAAAA,IAAI,EAAE;AAC9B,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,mBAAmBA,cAAAA,IAAI,EAAE;AAC/B,UAAM,qBAAqBA,cAAAA,IAAI,EAAE;AAGjC,UAAM,UAAUA,cAAAA,IAAI,wGAAwG;AAC5H,UAAM,mBAAmBA,cAAAA,IAAI,mDAAmD;AAChF,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAC1B,UAAM,WAAWA,cAAAA,IAAI,UAAU;AAG/B,UAAM,eAAeA,cAAAA,IAAI,IAAI;AAC7B,UAAM,WAAWA,cAAAA,IAAI,IAAI;AAGzB,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAClC,UAAM,YAAYA,cAAAA,IAAI,CAAA,CAAE;AACxB,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAC3B,UAAM,eAAeA,cAAG,IAAC,oBAAI,IAAG,CAAE;AAGlC,UAAM,YAAYA,cAAAA,IAAI,CAAC;AACvB,UAAM,iBAAiBA,cAAAA,IAAI,CAAC;AACJA,kBAAG,IAAC,CAAC;AACPA,kBAAG,IAAC,CAAC;AAG3B,UAAM,YAAYC,cAAQ,SAAC,MAAM;AAChC,aAAO,UAAU,MACf,IAAI,CAAC,OAAO,WAAW,EAAE,GAAG,OAAO,eAAe,MAAK,EAAG,EAC1D,OAAO,WAAS,MAAM,SAAS,KAAK;AAAA,IACvC,CAAC;AAED,UAAM,eAAeA,cAAQ,SAAC,MAAM;AACnC,aAAO,UAAU,MACf,IAAI,CAAC,OAAO,WAAW,EAAE,GAAG,OAAO,eAAe,MAAK,EAAG,EAC1D,OAAO,WAAS,CAAC,MAAM,QAAQ,MAAM,SAAS,KAAK;AAAA,IACtD,CAAC;AAMD,UAAM,oBAAoB,YAAY;AACrC,UAAI;AACHC,sBAAAA,MAAY,MAAA,OAAA,sDAAA,eAAe;AAG3B,iBAAS,QAAQ;AAGjBA,sBAAAA,MAAI,YAAY,EAAE,OAAO,WAAY,CAAA;AAErC,cAAM,qBAAoB;AAE1BA,sBAAG,MAAC,YAAW;AACfA,sBAAAA,MAAY,MAAA,OAAA,sDAAA,YAAY;AAExBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAED,eAAO;AAAA,MACP,SAAQ,OAAO;AACfA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,sDAAA,eAAe,KAAK;AAElCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAED,eAAO;AAAA,MACP;AAAA,IACF;AAMA,UAAM,mBAAmB,YAAY;AACpC,UAAI;AACH,kBAAU,QAAQ;AAClB,yBAAiB,QAAQ;AAGzB,YAAI,CAAC,aAAa,OAAO;AACxBA,wBAAAA,MAAA,MAAA,OAAA,sDAAY,cAAc;AAC1B,gBAAM,qBAAoB;AAAA,QAC7B,OAAS;AACNA,wBAAAA,MAAA,MAAA,OAAA,sDAAY,aAAa;AAAA,QACzB;AAGD,YAAI,aAAa,OAAO;AAEvB,cAAI,aAAa,MAAM,QAAQ;AAC9BA,0BAAAA,MAAY,MAAA,OAAA,sDAAA,qBAAqB;AACjC,oBAAQ,QAAQ,aAAa,MAAM;AACnC,kBAAM,uBAAsB;AAC5B;AAAA,UACA;AAGD,cAAI,aAAa,MAAM,cAAc,aAAa,MAAM,UAAU;AACjEA,0BAAAA,MAAA,MAAA,OAAA,sDAAY,iBAAiB;AAC7B,kBAAM,wBAAuB;AAC7B;AAAA,UACA;AAGD,cAAI,aAAa,MAAM,aAAa,aAAa,MAAM,UAAU,SAAS,GAAG;AAC5EA,0BAAAA,MAAY,MAAA,OAAA,sDAAA,kBAAkB;AAC9B,sBAAU,QAAQ,aAAa,MAAM;AACrC;AAAA,UACA;AAAA,QACD;AAGDA,sBAAAA,MAAY,MAAA,OAAA,sDAAA,YAAY;AACxB,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,SAAS,GAAG;AACtB,oBAAU,QAAQ;AAAA,QACrB,OAAS;AACN,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC1B;AAAA,MAED,SAAQ,OAAO;AACfA,iGAAc,cAAc,KAAK;AACjCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AACD;MACF,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,yBAAyB,YAAY;AAC1C,UAAI;AACHA,sBAAY,MAAA,MAAA,OAAA,sDAAA,oBAAoB,QAAQ,KAAK;AAG7CA,sBAAAA,MAAI,aAAa;AAAA,UAChB,KAAK,QAAQ;AAAA,UACb,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,eAAe,KAAK;AAC3BA,4BAAAA,MAAI,aAAa;AAAA,gBAChB,UAAU,IAAI;AAAA,gBACd,UAAU;AAAA,gBACV,SAAS,MAAM;AACdA,gCAAAA,MAAA,MAAA,OAAA,sDAAY,aAAa;AAEzB;gBACA;AAAA,gBACD,MAAM,CAAC,UAAU;AAChBA,gCAAY,MAAA,MAAA,OAAA,sDAAA,uBAAuB,KAAK;AAExC;gBACA;AAAA,cACP,CAAM;AAAA,YACN,OAAW;AACNA,4BAAAA,MAAA,MAAA,OAAA,sDAAY,kBAAkB;AAC9B;YACA;AAAA,UACD;AAAA,UACD,MAAM,CAAC,UAAU;AAChBA,0BAAA,MAAA,MAAA,OAAA,sDAAY,qBAAqB,KAAK;AACtC;UACA;AAAA,QACJ,CAAG;AAAA,MACD,SAAQ,OAAO;AACfA,iGAAc,cAAc,KAAK;AACjC;MACA;AAAA,IACF;AAKA,UAAM,0BAA0B,YAAY;AAC3C,UAAI;AACHA,sBAAAA,MAAA,MAAA,OAAA,sDAAY,WAAW;AACvBA,sBAAY,MAAA,MAAA,OAAA,sDAAA,eAAe,aAAa,KAAK;AAG7C,cAAM,WAAW,aAAa,MAAM,cACnB,aAAa,MAAM,YACnB,aAAa,MAAM,QACnB,aAAa,MAAM,QACnB,aAAa;AAE9B,YAAI,CAAC,UAAU;AACdA,wBAAAA,MAAA,MAAA,OAAA,sDAAY,mBAAmB;AAC/B;AACA;AAAA,QACA;AAEDA,sBAAA,MAAA,MAAA,OAAA,sDAAY,YAAY,OAAO,QAAQ;AACvCA,+FAAY,YAAY,SAAS,UAAU,SAAS;AAGpD,cAAM,WAAW,MAAM,wBAAwB,QAAQ;AAGvD,YAAI,OAAO,aAAa,aAAa,SAAS,WAAW,MAAM,KAAK,SAAS,WAAW,GAAG,IAAI;AAC9FA,wBAAY,MAAA,MAAA,OAAA,sDAAA,kBAAkB,QAAQ;AAEtCA,wBAAAA,MAAI,aAAa;AAAA,YAChB,KAAK;AAAA,YACL,SAAS,CAAC,QAAQ;AACjB,kBAAI,IAAI,eAAe,KAAK;AAC3BA,8BAAAA,MAAI,aAAa;AAAA,kBAChB,UAAU,IAAI;AAAA,kBACd,UAAU;AAAA,kBACV,SAAS,MAAM;AACdA,kCAAAA,MAAY,MAAA,OAAA,sDAAA,YAAY;AACxB;kBACA;AAAA,kBACD,MAAM,CAAC,UAAU;AAChBA,kCAAA,MAAA,MAAA,OAAA,sDAAY,wBAAwB,KAAK;AACzC;kBACA;AAAA,gBACR,CAAO;AAAA,cACP,OAAY;AACNA,8BAAAA,yEAAY,iBAAiB;AAC7B;cACA;AAAA,YACD;AAAA,YACD,MAAM,CAAC,UAAU;AAChBA,qGAAY,oBAAoB,KAAK;AACrC;YACA;AAAA,UACL,CAAI;AAAA,QACJ,OAAS;AAENA,wBAAY,MAAA,MAAA,OAAA,sDAAA,mBAAmB,QAAQ;AAEvCA,wBAAAA,MAAI,aAAa;AAAA,YAChB;AAAA,YACA,UAAU;AAAA,YACV,SAAS,MAAM;AACdA,4BAAAA,MAAA,MAAA,OAAA,sDAAY,SAAS;AACrB;YACA;AAAA,YACD,MAAM,CAAC,UAAU;AAChBA,qGAAY,qBAAqB,KAAK;AACtC;YACA;AAAA,UACL,CAAI;AAAA,QACD;AAAA,MAED,SAAQ,OAAO;AACfA,iGAAc,YAAY,KAAK;AAC/B;MACA;AAAA,IACF;AAKA,UAAM,yBAAyB,YAAY;AAC1C,UAAI;AACHA,sBAAAA,MAAA,MAAA,OAAA,sDAAY,WAAW;AACvB,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,SAAS,GAAG;AACtB,oBAAU,QAAQ;AAAA,QACrB,OAAS;AACN,gBAAM,IAAI,MAAM,UAAU;AAAA,QAC1B;AAAA,MACD,SAAQ,OAAO;AACfA,iGAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;MACA;AAAA,IACF;AAKA,UAAM,0BAA0B,OAAO,aAAa;AACnD,UAAI;AACHA,sBAAY,MAAA,MAAA,OAAA,sDAAA,iBAAiB,OAAO,QAAQ;AAG5C,YAAI,OAAO,aAAa,YAAY,SAAS,WAAW,OAAO,GAAG;AACjEA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,gBAAgB;AAE5B,gBAAM,aAAa,SAAS,MAAM,GAAG,EAAE,CAAC;AACxC,gBAAM,eAAe,YAAY,KAAK,IAAG,CAAE;AAC3C,gBAAM,eAAe,GAAGA,oBAAI,IAAI,cAAc,IAAI,YAAY;AAE9D,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,gCAAI,qBAAsB,EAAC,UAAU;AAAA,cACpC,UAAU;AAAA,cACV,MAAM;AAAA,cACN,UAAU;AAAA,cACV,SAAS,MAAM;AACdA,uGAAY,iBAAiB,YAAY;AACzC,wBAAQ,YAAY;AAAA,cACpB;AAAA,cACD,MAAM,CAAC,UAAU;AAChBA,8BAAA,MAAA,MAAA,SAAA,sDAAc,iBAAiB,KAAK;AACpC,uBAAO,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,cAC5D;AAAA,YACN,CAAK;AAAA,UACL,CAAI;AAAA,QACD;AAGD,YAAI,OAAO,aAAa,YAAY,qBAAqB,KAAK,QAAQ,GAAG;AACxEA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,iBAAiB;AAE7B,gBAAM,eAAe,YAAY,KAAK,IAAG,CAAE;AAC3C,gBAAM,eAAe,GAAGA,oBAAI,IAAI,cAAc,IAAI,YAAY;AAE9D,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,gCAAI,qBAAsB,EAAC,UAAU;AAAA,cACpC,UAAU;AAAA,cACV,MAAM;AAAA,cACN,UAAU;AAAA,cACV,SAAS,MAAM;AACdA,uGAAY,kBAAkB,YAAY;AAC1C,wBAAQ,YAAY;AAAA,cACpB;AAAA,cACD,MAAM,CAAC,UAAU;AAChBA,8BAAA,MAAA,MAAA,SAAA,sDAAc,kBAAkB,KAAK;AACrC,uBAAO,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,cAC5D;AAAA,YACN,CAAK;AAAA,UACL,CAAI;AAAA,QACD;AAGD,YAAI,oBAAoB,aAAa;AACpCA,wBAAAA,MAAA,MAAA,OAAA,sDAAY,qBAAqB;AAEjC,gBAAM,eAAe,YAAY,KAAK,IAAG,CAAE;AAC3C,gBAAM,eAAe,GAAGA,oBAAI,IAAI,cAAc,IAAI,YAAY;AAE9D,iBAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,gCAAI,qBAAsB,EAAC,UAAU;AAAA,cACpC,UAAU;AAAA,cACV,MAAM;AAAA,cACN,SAAS,MAAM;AACdA,8BAAA,MAAA,MAAA,OAAA,sDAAY,sBAAsB,YAAY;AAC9C,wBAAQ,YAAY;AAAA,cACpB;AAAA,cACD,MAAM,CAAC,UAAU;AAChBA,8BAAc,MAAA,MAAA,SAAA,sDAAA,sBAAsB,KAAK;AACzC,uBAAO,IAAI,MAAM,WAAW,MAAM,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,cAC5D;AAAA,YACN,CAAK;AAAA,UACL,CAAI;AAAA,QACD;AAGD,YAAI,OAAO,aAAa,aAAa,SAAS,WAAW,MAAM,KAAK,SAAS,WAAW,GAAG,IAAI;AAC9FA,wBAAY,MAAA,MAAA,OAAA,sDAAA,kBAAkB,QAAQ;AACtC,iBAAO;AAAA,QACP;AAGD,YAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACtDA,wBAAY,MAAA,MAAA,OAAA,sDAAA,iBAAiB,QAAQ;AAGrC,gBAAM,cAAc,SAAS,OAAO,SAAS,WAAW,SAAS,eAAe,SAAS;AACzF,cAAI,eAAe,OAAO,gBAAgB,UAAU;AACnDA,0BAAA,MAAA,MAAA,OAAA,sDAAY,iBAAiB,WAAW;AACxC,mBAAO;AAAA,UACP;AAGD,gBAAM,iBAAiB,SAAS,UAAU,SAAS,QAAQ,SAAS;AACpE,cAAI,kBAAkB,OAAO,mBAAmB,UAAU;AACzDA,0BAAAA,MAAA,MAAA,OAAA,sDAAY,iBAAiB;AAC7B,mBAAO,MAAM,wBAAwB,cAAc;AAAA,UACnD;AAAA,QACD;AAGDA,sBAAc,MAAA,MAAA,SAAA,sDAAA,cAAc,OAAO,UAAU,QAAQ;AACrD,cAAM,IAAI,MAAM,cAAc,OAAO,QAAQ,EAAE;AAAA,MAE/C,SAAQ,OAAO;AACfA,iGAAc,YAAY,KAAK;AAC/B,cAAM;AAAA,MACN;AAAA,IACF;AAKA,UAAM,eAAe,YAAY;AAChC,UAAI;AACH,kBAAU,QAAQ;AAClB,yBAAiB,QAAQ;AAGzB,cAAM,SAAS,MAAM;AACrB,YAAI,OAAO,SAAS,GAAG;AACtB,oBAAU,QAAQ;AAAA,QACrB,OAAS;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;QACA;AAAA,MACD,SAAQ,OAAO;AACfA,iGAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AACD;MACF,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAKA,UAAM,uBAAuB,YAAY;AACxC,UAAI;AACHA,sBAAAA,MAAY,MAAA,OAAA,sDAAA,sCAAsC;AAGlD,cAAM,SAAS,MAAMC,UAAAA,IAAI,SAAS,WAAU;AAE5C,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC9C,uBAAa,QAAQ,OAAO;AAC5BD,wBAAY,MAAA,MAAA,OAAA,sDAAA,eAAe,OAAO,IAAI;AAGtCA,wBAAAA,MAAA,MAAA,OAAA,sDAAY,mBAAmB;AAC/BA,wBAAY,MAAA,MAAA,OAAA,sDAAA,SAAS,OAAO,OAAO,IAAI;AACvCA,8BAAA,MAAA,OAAA,sDAAY,SAAS,OAAO,KAAK,OAAO,IAAI,CAAC;AAG7C,gBAAM,aAAa,CAAC,cAAc,YAAY,QAAQ,QAAQ,UAAU,OAAO,eAAe,WAAW,WAAW;AACpH,qBAAW,QAAQ,WAAS;AAC3B,gBAAI,OAAO,KAAK,KAAK,MAAM,QAAW;AACrCA,qGAAY,QAAQ,KAAK,KAAK,OAAO,OAAO,KAAK,KAAK,GAAG,OAAO,KAAK,KAAK,CAAC;AAAA,YAC3E;AAAA,UACL,CAAI;AACDA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,kBAAkB;AAG9B,cAAI,OAAO,KAAK,SAAS;AACxB,kBAAM,UAAU,OAAO,KAAK;AAC5B,gBAAI,QAAQ,SAAS;AACpB,sBAAQ,QAAQ,QAAQ;AAAA,YACxB;AACD,gBAAI,QAAQ,UAAU;AACrB,uBAAS,QAAQ,QAAQ;AAAA,YACzB;AACD,gBAAI,QAAQ,SAAS;AACpB,+BAAiB,QAAQ,QAAQ;AAAA,YACjC;AAAA,UACD;AAED,iBAAO,OAAO;AAAA,QACjB,OAAS;AACN,gBAAM,WAAW,OAAO,OAAO;AAC/BA,wBAAA,MAAA,MAAA,SAAA,sDAAc,YAAY,QAAQ;AAClC,mBAAS,QAAQ;AAEjBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UACd,CAAI;AAED,gBAAM,IAAI,MAAM,QAAQ;AAAA,QACxB;AAAA,MACD,SAAQ,OAAO;AACfA,iGAAc,oCAAoC,KAAK;AACvD,iBAAS,QAAQ,MAAM,WAAW;AAElCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QACb,CAAG;AAED,cAAM;AAAA,MACN;AAAA,IACF;AAMA,UAAM,eAAe,YAAY;;AAChC,UAAI;AAEH,YAAI,aAAa,SAAS,aAAa,MAAM,WAAW;AACvDA,wBAAAA,MAAY,MAAA,OAAA,sDAAA,iBAAiB;AAC7B,iBAAO,aAAa,MAAM;AAAA,QAC1B;AAGDA,sBAAAA,yEAAY,iBAAiB;AAG7B,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAGtD,cAAM,cAAY,kBAAa,UAAb,mBAAoB,cAAa;AACnD,cAAM,SAAS,CAAA;AAEf,iBAAS,IAAI,GAAG,KAAK,WAAW,KAAK;AACpC,iBAAO,KAAK;AAAA,YACX,KAAK,GAAG,iBAAiB,KAAK,QAAQ,CAAC;AAAA,YACvC,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAED,eAAO;AAAA,MACP,SAAQ,OAAO;AACfA,iGAAc,cAAc,KAAK;AAGjCA,sBAAAA,MAAA,MAAA,OAAA,sDAAY,QAAQ;AACpB,cAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAG,CAAC;AAErD,eAAO;AAAA,UACN,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,UACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,UACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,UACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,UACvD,EAAE,KAAK,GAAG,iBAAiB,KAAK,cAAc,MAAM,EAAG;AAAA,QAC1D;AAAA,MACE;AAAA,IACF;AAKA,UAAM,WAAW,CAAC,MAAM;AACvB,YAAM,EAAE,WAAW,kBAAkB,cAAc,aAAc,IAAG,EAAE;AAGtE,gBAAU,QAAQ;AAGlB,UAAI,eAAe,cAAc;AAChC,uBAAe,QAAS,oBAAoB,eAAe,gBAAiB;AAAA,MAC5E;AAAA,IACF;AAKA,UAAM,cAAc,CAAC,UAAU;AAC9B,mBAAa,MAAM,IAAI,KAAK;AAC5BA,0BAAA,MAAA,OAAA,sDAAY,MAAM,QAAQ,CAAC,OAAO;AAAA,IACnC;AAKA,UAAM,eAAe,CAAC,UAAU;AAC/BA,0BAAA,MAAA,SAAA,sDAAc,MAAM,QAAQ,CAAC,OAAO;AACpCA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO,IAAI,QAAQ,CAAC;AAAA,QACpB,MAAM;AAAA,QACN,UAAU;AAAA,MACZ,CAAE;AAAA,IACF;AAKA,UAAM,oBAAoB,MAAM;AAC/B,uBAAiB,QAAQ;AACzB,gBAAU,QAAQ;AAClB,qBAAe,QAAQ;AACvB,mBAAa,MAAM;IACpB;AAKA,UAAM,mBAAmB,MAAM;AAC9BA,oBAAAA,MAAA,MAAA,OAAA,sDAAY,mBAAmB;AAE/B,UAAI,YAAY;AAEhB,UAAI,aAAa,OAAO;AACvB,oBAAY;AAAA;AACZ,qBAAa,SAAS,OAAO,aAAa,KAAK;AAAA;AAC/C,qBAAa,SAAS,OAAO,KAAK,aAAa,KAAK,EAAE,KAAK,IAAI,CAAC;AAAA;AAAA;AAGhE,cAAM,aAAa,CAAC,cAAc,YAAY,QAAQ,QAAQ,UAAU,OAAO,aAAa;AAC5F,mBAAW,QAAQ,WAAS;AAC3B,cAAI,aAAa,MAAM,KAAK,MAAM,QAAW;AAC5C,kBAAM,QAAQ,aAAa,MAAM,KAAK;AACtC,yBAAa,GAAG,KAAK,KAAK,OAAO,KAAK,MAAK,+BAAO,WAAU,SAAS;AAAA;AAAA,UACrE;AAAA,QACJ,CAAG;AAAA,MACH,WAAY,SAAS,OAAO;AAC1B,oBAAY;AAAA,QAAkB,SAAS,KAAK;AAAA,MAC9C,OAAQ;AACN,oBAAY;AAAA,MACZ;AAEDA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAChB;UACA;AAAA,QACD;AAAA,MACH,CAAE;AAEDA,oBAAAA,MAAA,MAAA,OAAA,sDAAY,qBAAqB;AAAA,IAClC;AAKA,UAAM,iBAAiB,MAAM;AAC5B,UAAI;AACH,kBAAU,QAAQ;AAClB,yBAAiB,QAAQ;AAGzB,kBAAU,QAAQ,CAAC;AAAA,UAClB,KAAK,QAAQ;AAAA,UACb,MAAM;AAAA,UACN,MAAM;AAAA,QACT,CAAG;AAEDA,sBAAA,MAAA,MAAA,OAAA,sDAAY,gBAAgB,QAAQ,KAAK;AAAA,MACzC,SAAQ,OAAO;AACfA,iGAAc,YAAY,KAAK;AAC/BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAkGAE,kBAAM,OAAC,CAAC,YAAY;AACnBF,oBAAA,MAAA,MAAA,OAAA,sDAAY,WAAW,OAAO;AAG9B,UAAI,QAAQ,SAAS;AACpB,YAAI;AACH,0BAAgB,QAAQ,mBAAmB,QAAQ,OAAO;AAC1D,kBAAQ,QAAQ,gBAAgB;AAChCA,wBAAY,MAAA,MAAA,OAAA,sDAAA,aAAa,QAAQ,KAAK;AAAA,QACtC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,kBAAkB,KAAK;AACrC,0BAAgB,QAAQ,QAAQ;AAChC,kBAAQ,QAAQ,QAAQ;AAAA,QACxB;AAAA,MACD;AAGD,UAAI,QAAQ,UAAU;AACrB,YAAI;AACH,2BAAiB,QAAQ,mBAAmB,QAAQ,QAAQ;AAC5D,mBAAS,QAAQ,iBAAiB;AAClCA,wBAAY,MAAA,MAAA,OAAA,sDAAA,YAAY,SAAS,KAAK;AAAA,QACtC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,mBAAmB,KAAK;AACtC,2BAAiB,QAAQ,QAAQ;AACjC,mBAAS,QAAQ,QAAQ;AAAA,QACzB;AAAA,MACD;AAGD,UAAI,QAAQ,UAAU;AACrB,YAAI;AACH,2BAAiB,QAAQ,mBAAmB,QAAQ,QAAQ;AAC5D,mBAAS,QAAQ,iBAAiB;AAClCA,wBAAY,MAAA,MAAA,OAAA,sDAAA,WAAW,SAAS,KAAK;AAAA,QACrC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,mBAAmB,KAAK;AACtC,2BAAiB,QAAQ,QAAQ;AACjC,mBAAS,QAAQ,QAAQ;AAAA,QACzB;AAAA,MACD;AAGD,UAAI,QAAQ,YAAY;AACvB,YAAI;AACH,6BAAmB,QAAQ,mBAAmB,QAAQ,UAAU;AAChEA,wBAAA,MAAA,MAAA,OAAA,sDAAY,WAAW,mBAAmB,KAAK;AAE/C,2BAAiB,QAAQ,gDAAgD,mBAAmB,KAAK;AAAA,QACjG,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,sDAAA,qBAAqB,KAAK;AACxC,6BAAmB,QAAQ,QAAQ;AACnC,2BAAiB,QAAQ,gDAAgD,QAAQ,UAAU;AAAA,QAC3F;AAAA,MACD;AAAA,IACF,CAAC;AAEDG,kBAAAA,UAAU,YAAY;AAErB,YAAM,QAAQ,SAAS,QAAQ,QAAQ,SAAS,KAAK,KAAK;AAC1DH,oBAAAA,MAAI,sBAAsB,EAAE,MAAK,CAAE;AAEnC,UAAI;AAEHA,sBAAAA,MAAY,MAAA,OAAA,sDAAA,sBAAsB;AAClC,cAAM,qBAAoB;AAG1B,YAAI,SAAS,UAAU,OAAO;AAE7B;QACH,WAAa,SAAS,UAAU,SAAS,SAAS,UAAU,QAAQ;AAEjE;QACH,OAAS;AAEN;QACA;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,sDAAA,iBAAiB,KAAK;AAEpCA,sBAAAA,yEAAY,kBAAkB;AAE9B,YAAI,SAAS,UAAU,OAAO;AAC7B;QACH,OAAS;AAEN;QACA;AAAA,MACD;AAAA,IACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACr9BD,GAAG,WAAW,eAAe;"}