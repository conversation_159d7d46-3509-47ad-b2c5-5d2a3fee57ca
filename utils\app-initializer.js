// 应用初始化工具
import { isDebug } from '@/config/env.js';
import { 
  initPerformanceOptimization, 
  destroyPerformanceOptimization,
  performanceMonitor,
  memoryOptimizer 
} from '@/utils/performance-optimizer.js';
import wechatAuth from '@/utils/wechat-auth.js';

/**
 * 应用初始化器
 */
class AppInitializer {
  constructor() {
    this.initialized = false;
    this.initPromise = null;
  }

  /**
   * 初始化应用
   */
  async init() {
    if (this.initialized) {
      return this.initPromise;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._performInit();
    return this.initPromise;
  }

  /**
   * 执行初始化
   */
  async _performInit() {
    const monitor = performanceMonitor;
    monitor.start('app-init');

    try {
      if (isDebug()) {
        console.log('开始应用初始化...');
      }

      // 1. 初始化性能优化
      await this._initPerformanceOptimization();

      // 2. 初始化微信认证
      await this._initWechatAuth();

      // 3. 初始化全局错误处理
      await this._initGlobalErrorHandler();

      // 4. 初始化页面生命周期监听
      await this._initPageLifecycle();

      this.initialized = true;

      if (isDebug()) {
        console.log('应用初始化完成');
      }

      monitor.end('app-init');
      return { success: true };
    } catch (error) {
      monitor.end('app-init');
      console.error('应用初始化失败:', error);
      return { success: false, error };
    }
  }

  /**
   * 初始化性能优化
   */
  async _initPerformanceOptimization() {
    try {
      initPerformanceOptimization();
      
      // 添加微信认证相关的清理任务
      memoryOptimizer.addCleanupTask(() => {
        // 清理过期的登录信息
        try {
          const tokenExpireTime = uni.getStorageSync('token_expire_time');
          if (tokenExpireTime && Date.now() > tokenExpireTime) {
            wechatAuth.clearLoginInfo();
          }
        } catch (error) {
          console.error('清理过期登录信息失败:', error);
        }
      });

      if (isDebug()) {
        console.log('性能优化初始化完成');
      }
    } catch (error) {
      console.error('性能优化初始化失败:', error);
    }
  }

  /**
   * 初始化微信认证
   */
  async _initWechatAuth() {
    try {
      // 检查登录状态但不自动登录
      await wechatAuth.checkLoginStatus();
      
      if (isDebug()) {
        console.log('微信认证初始化完成');
      }
    } catch (error) {
      console.error('微信认证初始化失败:', error);
    }
  }

  /**
   * 初始化全局错误处理
   */
  async _initGlobalErrorHandler() {
    try {
      // 监听未捕获的Promise错误
      if (typeof uni !== 'undefined' && uni.onUnhandledRejection) {
        uni.onUnhandledRejection((event) => {
          console.error('未处理的Promise错误:', event.reason);
          
          // 在调试模式下显示错误
          if (isDebug()) {
            uni.showToast({
              title: '发生未处理错误',
              icon: 'none',
              duration: 2000
            });
          }
        });
      }

      // 监听JS错误
      /* if (typeof uni !== 'undefined' && uni.onError) {
        uni.onError((error) => {
          console.error('JS错误:', error);
          
          // 在调试模式下显示错误
          if (isDebug()) {
            uni.showToast({
              title: 'JS执行错误',
              icon: 'none',
              duration: 2000
            });
          }
        });
      } */

      if (isDebug()) {
        console.log('全局错误处理初始化完成');
      }
    } catch (error) {
      console.error('全局错误处理初始化失败:', error);
    }
  }

  /**
   * 初始化页面生命周期监听
   */
  async _initPageLifecycle() {
    try {
      // 监听页面显示
      if (typeof uni !== 'undefined' && uni.onAppShow) {
        uni.onAppShow(() => {
          if (isDebug()) {
            console.log('应用显示');
          }
          
          // 检查登录状态
          wechatAuth.checkLoginStatus().catch(error => {
            console.error('检查登录状态失败:', error);
          });
        });
      }

      // 监听页面隐藏
      if (typeof uni !== 'undefined' && uni.onAppHide) {
        uni.onAppHide(() => {
          if (isDebug()) {
            console.log('应用隐藏');
          }
          
          // 执行内存清理
          memoryOptimizer.performCleanup();
        });
      }

      if (isDebug()) {
        console.log('页面生命周期监听初始化完成');
      }
    } catch (error) {
      console.error('页面生命周期监听初始化失败:', error);
    }
  }

  /**
   * 销毁应用
   */
  destroy() {
    try {
      // 销毁性能优化
      destroyPerformanceOptimization();

      // 销毁微信认证
      if (wechatAuth && typeof wechatAuth.destroy === 'function') {
        wechatAuth.destroy();
      }

      this.initialized = false;
      this.initPromise = null;

      if (isDebug()) {
        console.log('应用已销毁');
      }
    } catch (error) {
      console.error('应用销毁失败:', error);
    }
  }

  /**
   * 获取初始化状态
   */
  isInitialized() {
    return this.initialized;
  }
}

// 创建全局实例
export const appInitializer = new AppInitializer();

/**
 * 快捷初始化函数
 */
export async function initApp() {
  return await appInitializer.init();
}

/**
 * 快捷销毁函数
 */
export function destroyApp() {
  appInitializer.destroy();
}

/**
 * 检查应用是否已初始化
 */
export function isAppInitialized() {
  return appInitializer.isInitialized();
}
