@echo off
chcp 65001 >nul
echo 🧹 开始清理uni-popup相关的编译缓存...
echo.

cd /d "%~dp0.."

echo 📁 清理开发环境编译目录...
if exist "unpackage\dist\dev\mp-weixin\node-modules\@dcloudio" (
    rmdir /s /q "unpackage\dist\dev\mp-weixin\node-modules\@dcloudio"
    echo ✅ 已删除: unpackage\dist\dev\mp-weixin\node-modules\@dcloudio
) else (
    echo ℹ️ 目录不存在: unpackage\dist\dev\mp-weixin\node-modules\@dcloudio
)

echo 📁 清理生产环境编译目录...
if exist "unpackage\dist\build\mp-weixin\node-modules\@dcloudio" (
    rmdir /s /q "unpackage\dist\build\mp-weixin\node-modules\@dcloudio"
    echo ✅ 已删除: unpackage\dist\build\mp-weixin\node-modules\@dcloudio
) else (
    echo ℹ️ 目录不存在: unpackage\dist\build\mp-weixin\node-modules\@dcloudio
)

echo 📄 清理应用配置文件...
if exist "unpackage\dist\dev\mp-weixin\app.json" (
    del /q "unpackage\dist\dev\mp-weixin\app.json"
    echo ✅ 已删除: unpackage\dist\dev\mp-weixin\app.json
) else (
    echo ℹ️ 文件不存在: unpackage\dist\dev\mp-weixin\app.json
)

if exist "unpackage\dist\build\mp-weixin\app.json" (
    del /q "unpackage\dist\build\mp-weixin\app.json"
    echo ✅ 已删除: unpackage\dist\build\mp-weixin\app.json
) else (
    echo ℹ️ 文件不存在: unpackage\dist\build\mp-weixin\app.json
)

echo 📦 清理node_modules缓存...
if exist "node_modules\@dcloudio\uni-ui" (
    rmdir /s /q "node_modules\@dcloudio\uni-ui"
    echo ✅ 已删除: node_modules\@dcloudio\uni-ui
) else (
    echo ℹ️ 目录不存在: node_modules\@dcloudio\uni-ui
)

echo 🗂️ 清理HBuilderX缓存...
if exist ".hbuilderx" (
    rmdir /s /q ".hbuilderx"
    echo ✅ 已删除: .hbuilderx
) else (
    echo ℹ️ 目录不存在: .hbuilderx
)

echo.
echo ✨ 清理完成！
echo.
echo 📋 接下来请按顺序执行以下步骤：
echo 1. 关闭HBuilderX（如果正在运行）
echo 2. 重新打开HBuilderX
echo 3. 在HBuilderX中点击"运行" -^> "清理项目"
echo 4. 重新编译项目到微信小程序
echo 5. 如果仍有错误，请检查控制台输出的具体错误信息
echo.
echo ⚠️ 注意：如果问题仍然存在，可能需要重新安装依赖
echo    可以删除整个 node_modules 目录后重新运行项目
echo.
pause
