// 性能优化工具
import { isDebug } from '@/config/env.js';
import { assetPreloader } from '@/config/assets.js';

/**
 * 性能监控器
 */
class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.observers = [];
    this.isEnabled = isDebug();
  }

  /**
   * 开始性能测量
   * @param {string} name 测量名称
   */
  start(name) {
    if (!this.isEnabled) return;
    
    this.metrics.set(name, {
      startTime: Date.now(),
      startMemory: this.getMemoryUsage()
    });
  }

  /**
   * 结束性能测量
   * @param {string} name 测量名称
   */
  end(name) {
    if (!this.isEnabled) return;
    
    const metric = this.metrics.get(name);
    if (!metric) return;

    const endTime = Date.now();
    const endMemory = this.getMemoryUsage();
    
    const result = {
      name,
      duration: endTime - metric.startTime,
      memoryDelta: endMemory - metric.startMemory,
      timestamp: endTime
    };

    console.log(`[性能] ${name}: ${result.duration}ms, 内存变化: ${result.memoryDelta}KB`);
    
    // 通知观察者
    this.observers.forEach(observer => observer(result));
    
    this.metrics.delete(name);
    return result;
  }

  /**
   * 获取内存使用情况（简化版本）
   */
  getMemoryUsage() {
    // 微信小程序中无法直接获取内存使用情况
    // 这里返回一个模拟值
    return 0;
  }

  /**
   * 添加性能观察者
   * @param {Function} observer 观察者函数
   */
  addObserver(observer) {
    this.observers.push(observer);
  }

  /**
   * 移除性能观察者
   * @param {Function} observer 观察者函数
   */
  removeObserver(observer) {
    const index = this.observers.indexOf(observer);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }
}

/**
 * 资源加载优化器
 */
class ResourceOptimizer {
  constructor() {
    this.loadQueue = [];
    this.loadingResources = new Set();
    this.loadedResources = new Set();
    this.maxConcurrent = 3; // 最大并发加载数
  }

  /**
   * 预加载关键资源
   */
  async preloadCriticalResources() {
    const monitor = performanceMonitor;
    monitor.start('preload-critical');

    try {
      // 预加载关键头像资源
      const result = await assetPreloader.preloadCriticalAssets();
      
      if (isDebug()) {
        console.log('关键资源预加载完成:', result);
      }

      monitor.end('preload-critical');
      return result;
    } catch (error) {
      monitor.end('preload-critical');
      console.error('关键资源预加载失败:', error);
      return { success: [], failed: [], total: 0, successRate: 0 };
    }
  }

  /**
   * 延迟加载非关键资源
   * @param {Array} resources 资源列表
   */
  async lazyLoadResources(resources) {
    if (!Array.isArray(resources)) return;

    const monitor = performanceMonitor;
    monitor.start('lazy-load');

    try {
      // 分批加载资源
      const batches = this.createBatches(resources, this.maxConcurrent);
      const results = [];

      for (const batch of batches) {
        const batchResults = await Promise.allSettled(
          batch.map(resource => this.loadResource(resource))
        );
        results.push(...batchResults);
      }

      monitor.end('lazy-load');
      return results;
    } catch (error) {
      monitor.end('lazy-load');
      console.error('延迟加载失败:', error);
      return [];
    }
  }

  /**
   * 创建资源加载批次
   * @param {Array} resources 资源列表
   * @param {number} batchSize 批次大小
   */
  createBatches(resources, batchSize) {
    const batches = [];
    for (let i = 0; i < resources.length; i += batchSize) {
      batches.push(resources.slice(i, i + batchSize));
    }
    return batches;
  }

  /**
   * 加载单个资源
   * @param {string} resource 资源URL
   */
  loadResource(resource) {
    return new Promise((resolve, reject) => {
      if (this.loadedResources.has(resource)) {
        resolve(resource);
        return;
      }

      if (this.loadingResources.has(resource)) {
        // 如果正在加载，等待加载完成
        const checkLoaded = () => {
          if (this.loadedResources.has(resource)) {
            resolve(resource);
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
        return;
      }

      this.loadingResources.add(resource);

      uni.getImageInfo({
        src: resource,
        success: () => {
          this.loadingResources.delete(resource);
          this.loadedResources.add(resource);
          resolve(resource);
        },
        fail: (error) => {
          this.loadingResources.delete(resource);
          reject(error);
        }
      });
    });
  }
}

/**
 * 内存优化器
 */
class MemoryOptimizer {
  constructor() {
    this.cleanupTasks = [];
    this.cleanupInterval = null;
  }

  /**
   * 启动内存清理
   */
  startCleanup() {
    if (this.cleanupInterval) return;

    // 每5分钟执行一次清理
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1000);
  }

  /**
   * 停止内存清理
   */
  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 执行内存清理
   */
  performCleanup() {
    const monitor = performanceMonitor;
    monitor.start('memory-cleanup');

    try {
      // 执行所有清理任务
      this.cleanupTasks.forEach(task => {
        try {
          task();
        } catch (error) {
          console.error('清理任务执行失败:', error);
        }
      });

      // 清理过期的缓存
      this.clearExpiredCache();

      if (isDebug()) {
        console.log('内存清理完成');
      }

      monitor.end('memory-cleanup');
    } catch (error) {
      monitor.end('memory-cleanup');
      console.error('内存清理失败:', error);
    }
  }

  /**
   * 添加清理任务
   * @param {Function} task 清理任务函数
   */
  addCleanupTask(task) {
    if (typeof task === 'function') {
      this.cleanupTasks.push(task);
    }
  }

  /**
   * 移除清理任务
   * @param {Function} task 清理任务函数
   */
  removeCleanupTask(task) {
    const index = this.cleanupTasks.indexOf(task);
    if (index > -1) {
      this.cleanupTasks.splice(index, 1);
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpiredCache() {
    try {
      const now = Date.now();
      const keys = ['token_expire_time', 'login_time'];
      
      keys.forEach(key => {
        try {
          const expireTime = uni.getStorageSync(key);
          if (expireTime && now > expireTime) {
            uni.removeStorageSync(key);
          }
        } catch (error) {
          // 忽略单个key的清理错误
        }
      });
    } catch (error) {
      console.error('清理过期缓存失败:', error);
    }
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor();
export const resourceOptimizer = new ResourceOptimizer();
export const memoryOptimizer = new MemoryOptimizer();

/**
 * 初始化性能优化
 */
export function initPerformanceOptimization() {
  if (isDebug()) {
    console.log('初始化性能优化...');
  }

  // 启动内存清理
  memoryOptimizer.startCleanup();

  // 预加载关键资源
  resourceOptimizer.preloadCriticalResources().catch(error => {
    console.error('关键资源预加载失败:', error);
  });

  // 添加页面卸载时的清理任务
  memoryOptimizer.addCleanupTask(() => {
    // 清理临时文件
    try {
      // 这里可以添加具体的清理逻辑
    } catch (error) {
      console.error('页面卸载清理失败:', error);
    }
  });
}

/**
 * 销毁性能优化
 */
export function destroyPerformanceOptimization() {
  memoryOptimizer.stopCleanup();
  
  if (isDebug()) {
    console.log('性能优化已销毁');
  }
}
