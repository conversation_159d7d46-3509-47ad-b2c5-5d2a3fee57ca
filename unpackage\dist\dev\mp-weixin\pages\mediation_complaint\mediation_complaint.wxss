/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-0f5f266a:root {
  --primary-color: #3b7eeb;
  --primary-light: #e6f0ff;
  --text-primary: #333;
  --text-secondary: #666;
  --text-placeholder: #999;
  --border-color: #e0e0e0;
  --background-color: #f5f5f5;
  --card-background: #ffffff;
  --shadow-light: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  --shadow-card: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  --border-radius: 16rpx;
  --primary-dark: #2c62c9;
  --text-light:#999;
  --transition-normal:all 0.3s ease;
}
.mediation-complaint-container.data-v-0f5f266a {
  min-height: 100vh;
  background-color: var(--background-color);
  padding: 30rpx 20rpx;
}
.gradient-bg.data-v-0f5f266a {
  background: linear-gradient(135deg, #e6f0ff 0%, #e6f0ff 100%);
  border: none;
  margin-bottom: 40rpx;
  box-shadow: rgba(0, 0, 0, 0.04) 0 4rpx 16rpx;
  margin-bottom: 30rpx;
  border-radius: 24rpx;
  padding: 36rpx;
  transition: var(--transition-normal);
}
.gradient-bg .handle-box.data-v-0f5f266a {
  text-align: center;
  padding: 20rpx 10rpx 40rpx;
}
.gradient-bg .handle-icon.data-v-0f5f266a {
  width: 100rpx;
  height: 100rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30rpx;
}
.gradient-bg .handle-icon .fas.data-v-0f5f266a {
  font-size: 44rpx;
  color: white;
}
.gradient-bg .handle-title.data-v-0f5f266a {
  margin: 0 0 16rpx 0;
  color: var(--primary-color);
  font-size: 36rpx;
  font-weight: 600;
}
.gradient-bg .handle-text.data-v-0f5f266a {
  margin: 0;
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}
.feedback-types.data-v-0f5f266a {
  padding: 0 0 30rpx;
  display: flex;
  gap: 20rpx;
}
.feedback-types .form-card.data-v-0f5f266a {
  flex: 1;
}
.feedback-types .type-card-content.data-v-0f5f266a {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 28rpx;
  padding: 36rpx;
}
.feedback-types .type-card.data-v-0f5f266a {
  flex: 1;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  border: 2rpx solid var(--border-color);
  box-shadow: var(--shadow-light);
  transition: var(--transition-normal);
}
.feedback-types .type-card.active.data-v-0f5f266a {
  border-color: var(--primary-color);
  background-color: var(--primary-light);
  border-top: 8rpx solid var(--primary-color);
}
.feedback-types .type-card.active .feedback-type-icon.data-v-0f5f266a {
  background-color: var(--primary-color);
}
.feedback-types .type-card.active .feedback-type-icon .fas.data-v-0f5f266a {
  transform: scale(1.1);
  color: white;
}
.feedback-types .type-card.active .type-text.data-v-0f5f266a {
  color: var(--primary-color);
  font-weight: 600;
}
.feedback-types .type-card .feedback-type-icon.data-v-0f5f266a {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-light);
  border-radius: 50%;
  margin: 0px auto 20rpx;
  transition: 0.3s;
}
.feedback-types .type-card .feedback-type-icon .fas.data-v-0f5f266a {
  font-size: 36rpx;
  color: var(--primary-color);
  transition: 0.3s;
}
.feedback-types .type-card .type-text.data-v-0f5f266a {
  font-size: 28rpx;
  color: var(--text-primary);
  font-weight: 500;
  transition: var(--transition-normal);
}
.form-container.data-v-0f5f266a {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.form-card.data-v-0f5f266a {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  overflow: hidden;
}
.form-card .card-header.data-v-0f5f266a {
  padding: 30rpx 30rpx 20rpx 30rpx;
  display: flex;
  align-items: center;
}
.form-card .card-header .fas.data-v-0f5f266a {
  margin-right: 8px;
  color: var(--primary-color);
}
.form-card .card-header .card-title.data-v-0f5f266a {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 800;
}
.form-card .card-header .optional-tag.data-v-0f5f266a {
  font-size: 24rpx;
  color: var(--text-placeholder);
  padding: 4rpx 12rpx;
}
.form-card .card-content.data-v-0f5f266a {
  padding: 30rpx;
}
.form-card .card-content .help-text.data-v-0f5f266a {
  font-size: 24rpx;
  color: var(--text-light);
  margin-top: 15rpx;
  margin-bottom: 0;
  display: flex;
}
.form-card .card-content .help-text .fas.data-v-0f5f266a {
  margin-right: 10rpx;
  padding-top: 4rpx;
}
.input-field.data-v-0f5f266a {
  border-radius: 12rpx;
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
}
.category-grid.data-v-0f5f266a {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}
.category-card.data-v-0f5f266a {
  display: flex;
  align-items: center;
  padding: 32rpx;
  background-color: white;
  border-radius: 24rpx;
  border-width: 1px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.06);
  transition: 0.3s;
  position: relative;
}
.category-card .category-info.data-v-0f5f266a {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 20rpx;
}
.category-card .category-info .category-title.data-v-0f5f266a {
  font-size: 30rpx;
  color: var(--text-primary);
  margin-bottom: 6rpx;
  font-weight: 500;
  line-height: 1.2;
}
.category-card .category-info .category-desc.data-v-0f5f266a {
  font-size: 24rpx;
  color: var(--text-secondary);
  line-height: 1.3;
}
.category-card.active.data-v-0f5f266a {
  background-color: var(--primary-light);
  border-left: 10rpx solid var(--primary-color);
  box-shadow: rgba(59, 126, 235, 0.15) 0px 4px 12px;
  border-color: var(--primary-color);
  padding-left: 21rpx;
}
.category-card.active .category-title.data-v-0f5f266a {
  color: var(--primary-color);
  font-weight: bold;
}
.category-card.active .category-desc.data-v-0f5f266a {
  color: var(--primary-color);
  opacity: 0.8;
}
.category-card.active .radio-inner.data-v-0f5f266a {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}
.category-card.active .radio-inner .radio-dot.data-v-0f5f266a {
  color: white;
}
.category-card .category-icon.data-v-0f5f266a {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  flex-shrink: 0;
  background: #f8f9fa;
  border-radius: 10rpx;
  transition: 0.3s;
}
.category-card .category-icon .fas.data-v-0f5f266a {
  font-size: 36rpx;
  transition: 0.3s;
}
.category-card .category-radio.data-v-0f5f266a {
  flex-shrink: 0;
}
.category-card .category-radio .radio-inner.data-v-0f5f266a {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.category-card .category-radio .radio-inner.checked.data-v-0f5f266a {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
}
.category-card .category-radio .radio-inner .radio-dot.data-v-0f5f266a {
  font-size: 32rpx;
  color: white;
  transition: 0.3s;
}
.textarea-container.data-v-0f5f266a {
  position: relative;
}
.textarea-container .textarea-field.data-v-0f5f266a {
  min-height: 200rpx;
  border-radius: 12rpx;
  width: 100%;
}
.textarea-container .char-count.data-v-0f5f266a {
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;
  font-size: 24rpx;
  color: var(--text-placeholder);
  background-color: rgba(255, 255, 255, 0.8);
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}
.submit-btn.data-v-0f5f266a {
  margin-top: 60rpx;
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, #2979ff 100%);
  color: white;
  border-radius: 16rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  font-size: 32rpx;
  font-weight: 500;
  box-shadow: 0 8rpx 20rpx rgba(59, 126, 235, 0.3);
  transition: all 0.3s ease;
}
.submit-btn.data-v-0f5f266a:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(59, 126, 235, 0.3);
}
.submit-btn .submit-icon.data-v-0f5f266a {
  font-size: 28rpx;
}
.submit-btn .submit-text.data-v-0f5f266a {
  font-size: 32rpx;
}
.data-v-0f5f266a .uni-easyinput__content {
  border-color: var(--border-color) !important;
  border-radius: 12rpx !important;
  background-color: #fafafa !important;
}
.data-v-0f5f266a .uni-easyinput__content-input {
  font-size: 28rpx !important;
  color: var(--text-primary) !important;
}
.data-v-0f5f266a .uni-easyinput__placeholder-class {
  color: var(--text-placeholder) !important;
}
.data-v-0f5f266a .uni-easyinput__content-textarea {
  font-size: 28rpx !important;
  color: var(--text-primary) !important;
}