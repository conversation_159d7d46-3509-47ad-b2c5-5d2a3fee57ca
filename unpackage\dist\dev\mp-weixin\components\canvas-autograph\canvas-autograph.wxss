/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.canvas-autograph.data-v-a41641d7 {
  position: fixed;
  z-index: 998;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
}
.canvas-autograph .scroll-view.data-v-a41641d7 {
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
}
.canvas-autograph .action-buttons.data-v-a41641d7 {
  position: absolute;
  bottom: 40rpx;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 0 30rpx;
}
.canvas-autograph .action-buttons .btn.data-v-a41641d7 {
  min-width: 160rpx;
  height: 80rpx;
  margin: 0 20rpx;
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #FFFFFF;
  font-size: 28rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.canvas-autograph .action-buttons .btn-clear.data-v-a41641d7 {
  background-color: #F4F4F5;
  color: #909399;
}
.canvas-autograph .action-buttons .btn-confirm.data-v-a41641d7 {
  background-color: #409EFF;
}
.canvas-autograph .action-buttons .btn-cancel.data-v-a41641d7 {
  background-color: #F67D7D;
}