/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-info-form.data-v-5416efb5 {
  padding: 40rpx;
  background-color: #ffffff;
}
.avatar-section.data-v-5416efb5 {
  text-align: center;
  margin-bottom: 60rpx;
}
.avatar-label.data-v-5416efb5 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 30rpx;
}
.avatar-button.data-v-5416efb5 {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  overflow: hidden;
  border: none;
  background: none;
  padding: 0;
  margin: 0 auto;
}
.avatar-image.data-v-5416efb5 {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid #e5e5e5;
}
.avatar-overlay.data-v-5416efb5 {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.avatar-button:active .avatar-overlay.data-v-5416efb5 {
  opacity: 1;
}
.avatar-icon.data-v-5416efb5 {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}
.avatar-text.data-v-5416efb5 {
  font-size: 24rpx;
  color: #ffffff;
}
.nickname-section.data-v-5416efb5 {
  margin-bottom: 60rpx;
}
.nickname-label.data-v-5416efb5 {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}
.nickname-input.data-v-5416efb5 {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  background-color: #ffffff;
}
.nickname-input.data-v-5416efb5:focus {
  border-color: #007aff;
}
.nickname-tip.data-v-5416efb5 {
  font-size: 24rpx;
  color: #999999;
  margin-top: 16rpx;
}
.submit-section.data-v-5416efb5 {
  margin-bottom: 40rpx;
}
.submit-button.data-v-5416efb5 {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}
.submit-button--disabled.data-v-5416efb5 {
  background-color: #cccccc;
  color: #999999;
}
.skip-section.data-v-5416efb5 {
  text-align: center;
}
.skip-button.data-v-5416efb5 {
  background: none;
  border: none;
  color: #999999;
  font-size: 28rpx;
  padding: 20rpx;
}