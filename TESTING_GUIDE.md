# 电子签名功能测试指南

## 测试环境准备

### 1. 确保必要参数
- 确保页面接收到正确的 `case_number` 参数
- 确保用户已登录并有有效的token

### 2. 服务器端准备
- 确保服务器支持PUT方法
- 确保接口 `/mediation_management/mediation_case/wechat/{case_number}/update_electronic_signature/` 可用
- 确保服务器支持form-data格式的文件上传

## 功能测试步骤

### 测试1: 电子签名保存功能

1. **进入协议签署页面**
   ```
   /pages/agreement_signing/agreement_signing?case_number=TEST001&initiate_date=2023-11-01&case_status_cn=进行中
   ```

2. **勾选协议条款**
   - 点击复选框"我已阅读并同意《调解协议》的全部条款"

3. **进行电子签名**
   - 点击电子签名区域
   - 在签名界面进行签名
   - 点击确认完成签名

4. **验证结果**
   - 检查是否显示"签名保存成功"提示
   - 检查页面状态是否更新为已签名
   - 检查按钮文本是否变为"协议签署完成"

### 测试2: 页面跳转功能

1. **完成电子签名** (按照测试1的步骤)

2. **点击确认按钮**
   - 点击底部的"协议签署完成"按钮

3. **验证跳转**
   - 检查是否显示"协议确认完成"提示
   - 检查是否自动跳转到联系方式确认页面
   - 验证跳转的URL是否为 `/pages/contact_information/contact_information`

## 错误场景测试

### 测试3: 网络错误处理

1. **模拟网络断开**
   - 断开网络连接
   - 尝试进行电子签名

2. **验证错误处理**
   - 检查是否显示"签名保存失败，请重试"提示
   - 检查签名状态是否正确回滚

### 测试4: 参数缺失处理

1. **不传递case_number参数**
   ```
   /pages/agreement_signing/agreement_signing
   ```

2. **尝试签名**
   - 进行电子签名操作

3. **验证错误处理**
   - 检查是否显示相应的错误提示

### 测试5: 重复签名处理

1. **完成一次签名** (按照测试1的步骤)

2. **尝试再次签名**
   - 点击电子签名区域

3. **验证防重复逻辑**
   - 检查是否显示"协议已签署，无需重复操作"提示

## 调试信息

### 控制台日志
在测试过程中，可以在浏览器控制台查看以下日志：

```javascript
// 签名数据接收
console.log('签名数据:', signatureBase64);

// 临时文件创建
console.log('临时文件创建成功:', filePath);

// API调用结果
console.log('电子签名保存成功:', response);

// 错误信息
console.error('处理签名失败:', error);
```

### 网络请求监控
可以在开发者工具的Network面板中监控：

1. **PUT请求**
   - URL: `/mediation_management/mediation_case/wechat/{case_number}/update_electronic_signature/`
   - Method: PUT
   - Content-Type: multipart/form-data

2. **请求参数**
   - case_number: 案件号
   - electronic_signature: 签名图片文件

3. **请求头**
   - Authorization: Bearer {token}

## 常见问题排查

### 问题1: 签名保存失败
**可能原因:**
- 网络连接问题
- 服务器接口不可用
- 认证token无效
- 案件号参数错误

**排查步骤:**
1. 检查网络连接
2. 验证服务器接口状态
3. 检查token是否有效
4. 确认case_number参数正确

### 问题2: 页面跳转失败
**可能原因:**
- 目标页面不存在
- 路由配置错误
- 页面权限问题

**排查步骤:**
1. 检查目标页面路径是否正确
2. 验证pages.json中的路由配置
3. 检查页面访问权限

### 问题3: 文件转换失败
**可能原因:**
- base64数据格式错误
- 文件系统权限问题
- 存储空间不足

**排查步骤:**
1. 检查base64数据格式
2. 验证文件系统权限
3. 检查设备存储空间

## 性能测试

### 测试指标
- 签名保存响应时间
- 文件上传速度
- 页面跳转延迟

### 测试方法
1. 使用Performance API测量关键操作耗时
2. 监控内存使用情况
3. 测试不同网络条件下的表现

## 兼容性测试

### 测试平台
- 微信小程序
- 不同版本的微信客户端
- 不同操作系统(iOS/Android)

### 测试要点
- 文件系统API兼容性
- 网络请求兼容性
- UI组件显示效果
