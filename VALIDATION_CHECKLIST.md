# 电子签名功能修复验证清单

## ✅ 已完成的修复

### 1. 请求参数修复
- ✅ 确保 `name: 'electronic_signature'` 参数正确设置
- ✅ 使用 `filePath` 传递实际的二进制文件
- ✅ 保持 `method: 'PUT'` 和 form-data 格式

### 2. API调用优化
- ✅ 移除 `utils/api.js` 中复杂的文件上传逻辑
- ✅ 简化为只提供 `getUpdateSignatureUrl()` 方法
- ✅ 在页面组件中直接使用 `uni.uploadFile`

### 3. 文件处理增强
- ✅ 优化 `base64ToTempFile()` 函数
- ✅ 生成唯一文件名包含案件号和时间戳
- ✅ 添加详细的调试日志

## 🔍 验证要点

### 网络请求验证
在开发者工具中检查以下内容：

```
Request URL: /mediation_management/mediation_case/wechat/{case_number}/update_electronic_signature/
Request Method: PUT
Content-Type: multipart/form-data; boundary=----formdata-uni-app-xxx

Form Data:
- case_number: {实际案件号}
- electronic_signature: {二进制PNG文件}
```

### 关键代码验证

#### 1. 文件上传配置
```javascript
uni.uploadFile({
  url: apiUrl,
  filePath: filePath,                    // ✅ 实际文件路径
  name: 'electronic_signature',          // ✅ 正确的参数名
  method: 'PUT',                         // ✅ PUT方法
  formData: {
    case_number: caseNumber.value        // ✅ 案件号参数
  },
  header: {
    'Authorization': `${tokenType} ${token}` // ✅ 认证头
  }
})
```

#### 2. 文件转换验证
```javascript
// ✅ 生成的文件名格式
const fileName = `electronic_signature_${caseNumber.value}_${Date.now()}.png`;

// ✅ 文件路径
const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

// ✅ base64写入文件
fs.writeFile({
  filePath: filePath,
  data: base64,
  encoding: 'base64'
});
```

## 🧪 测试步骤

### 步骤1: 基础功能测试
1. 进入协议签署页面，确保有 `case_number` 参数
2. 勾选协议条款复选框
3. 点击电子签名区域，进入签名界面
4. 完成签名并确认

**预期结果:**
- 显示"签名保存成功"提示
- 页面状态更新为已签名
- 按钮文本变为"协议签署完成"

### 步骤2: 网络请求验证
1. 打开开发者工具 Network 面板
2. 执行步骤1的签名操作
3. 查看网络请求详情

**预期结果:**
- 看到PUT请求到正确的API地址
- 请求包含 `electronic_signature` 文件参数
- 请求包含 `case_number` 表单数据
- 响应状态码为200或201

### 步骤3: 错误处理测试
1. 断开网络连接
2. 尝试进行电子签名
3. 观察错误处理

**预期结果:**
- 显示"签名保存失败，请重试"提示
- 签名状态正确回滚
- 不会出现未处理的异常

### 步骤4: 文件系统验证
1. 在签名过程中查看控制台日志
2. 确认临时文件创建成功

**预期日志:**
```
开始创建临时文件: {
  fileName: "electronic_signature_TEST001_1699123456789.png",
  filePath: "/usr/xxx/electronic_signature_TEST001_1699123456789.png",
  base64Length: 12345
}

临时文件创建成功: {
  filePath: "/usr/xxx/electronic_signature_TEST001_1699123456789.png",
  fileName: "electronic_signature_TEST001_1699123456789.png"
}

开始上传电子签名: {
  url: "https://api.example.com/mediation_management/mediation_case/wechat/TEST001/update_electronic_signature/",
  filePath: "/usr/xxx/electronic_signature_TEST001_1699123456789.png",
  caseNumber: "TEST001"
}
```

## 🚨 常见问题排查

### 问题1: 服务器收不到文件
**检查项:**
- [ ] `name: 'electronic_signature'` 参数名是否正确
- [ ] 文件路径是否有效
- [ ] 服务器是否支持PUT方法和multipart/form-data

### 问题2: 文件转换失败
**检查项:**
- [ ] base64数据是否完整
- [ ] 文件系统权限是否正常
- [ ] 存储空间是否充足

### 问题3: 认证失败
**检查项:**
- [ ] token是否有效
- [ ] Authorization头格式是否正确
- [ ] 用户是否有上传权限

## 📋 修复前后对比

### 修复前的问题
```javascript
// ❌ 问题：复杂的封装，难以控制
const response = await api.electronicSignature.updateSignature(caseNumber.value, filePath);

// ❌ 问题：可能缺少关键参数
// ❌ 问题：错误处理不够详细
```

### 修复后的改进
```javascript
// ✅ 改进：直接控制上传过程
uni.uploadFile({
  url: apiUrl,
  filePath: filePath,
  name: 'electronic_signature',  // ✅ 明确指定参数名
  method: 'PUT',                 // ✅ 明确指定方法
  formData: {
    case_number: caseNumber.value // ✅ 明确传递案件号
  },
  // ✅ 详细的成功/失败处理
});
```

## ✅ 验证完成标准

当以下所有项目都通过时，修复验证完成：

- [ ] 电子签名文件能够成功上传到服务器
- [ ] 服务器能够接收到 `electronic_signature` 二进制文件参数
- [ ] 网络请求使用正确的PUT方法和form-data格式
- [ ] 错误处理机制工作正常
- [ ] 用户体验良好（提示信息清晰）
- [ ] 页面跳转逻辑正常工作
- [ ] 没有JavaScript错误或异常

## 📝 测试报告模板

```
测试时间: ___________
测试环境: ___________
案件号: _____________

✅ 基础功能测试: 通过/失败
✅ 网络请求验证: 通过/失败  
✅ 错误处理测试: 通过/失败
✅ 文件系统验证: 通过/失败

问题记录:
1. ________________
2. ________________

总体评估: 通过/需要进一步修复
```
