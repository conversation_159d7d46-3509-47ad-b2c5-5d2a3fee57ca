{"version": 3, "file": "user-info-form.js", "sources": ["components/user-info-form/user-info-form.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovd29yay_kuI3oia_otYTkuqfns7vnu58vbm9uLXBlcmZvcm1pbmctYXNzZXRzL2NvbXBvbmVudHMvdXNlci1pbmZvLWZvcm0vdXNlci1pbmZvLWZvcm0udnVl"], "sourcesContent": ["<template>\n  <view class=\"user-info-form\">\n    <!-- 头像选择区域 -->\n    <view class=\"avatar-section\">\n      <view class=\"avatar-label\">设置头像</view>\n      <button \n        class=\"avatar-button\"\n        open-type=\"chooseAvatar\"\n        @chooseavatar=\"onChooseAvatar\"\n        :disabled=\"isSubmitting\"\n      >\n        <image\n          class=\"avatar-image\"\n          :src=\"avatarPreviewUrl\"\n          mode=\"aspectFill\"\n          @error=\"onAvatarLoadError\"\n        />\n        <view class=\"avatar-overlay\">\n          <text class=\"avatar-icon\">📷</text>\n          <text class=\"avatar-text\">点击选择头像</text>\n        </view>\n      </button>\n    </view>\n\n    <!-- 昵称输入区域 -->\n    <view class=\"nickname-section\">\n      <view class=\"nickname-label\">设置昵称</view>\n      <input\n        class=\"nickname-input\"\n        type=\"nickname\"\n        placeholder=\"请输入您的昵称\"\n        v-model=\"userInfo.nickName\"\n        @blur=\"onNicknameBlur\"\n        @input=\"onNicknameInput\"\n        :disabled=\"isSubmitting\"\n        maxlength=\"20\"\n      />\n      <view class=\"nickname-tip\">昵称长度不超过20个字符</view>\n    </view>\n\n    <!-- 提交按钮 -->\n    <view class=\"submit-section\">\n      <button \n        class=\"submit-button\"\n        :class=\"{ 'submit-button--disabled': !canSubmit || isSubmitting }\"\n        :disabled=\"!canSubmit || isSubmitting\"\n        @click=\"onSubmit\"\n      >\n        <text v-if=\"isSubmitting\">保存中...</text>\n        <text v-else>{{ submitButtonText }}</text>\n      </button>\n    </view>\n\n    <!-- 跳过按钮（可选） -->\n    <view class=\"skip-section\" v-if=\"allowSkip\">\n      <button \n        class=\"skip-button\"\n        @click=\"onSkip\"\n        :disabled=\"isSubmitting\"\n      >\n        暂时跳过\n      </button>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, computed, watch, onMounted, onUnmounted } from 'vue';\nimport wechatAuth from '@/utils/wechat-auth.js';\nimport { isDebug } from '@/config/env.js';\nimport {\n  getDefaultAvatar,\n  handleAvatarError,\n  createAvatarPreviewUrl,\n  cleanupTempAvatars\n} from '@/utils/avatar-helper.js';\nimport { showError, withErrorBoundary } from '@/utils/error-handler.js';\nimport { showWechatError } from '@/utils/wechat-error-handler.js';\n\n// Props\nconst props = defineProps({\n  // 是否允许跳过\n  allowSkip: {\n    type: Boolean,\n    default: false\n  },\n  // 提交按钮文案\n  submitButtonText: {\n    type: String,\n    default: '完成设置'\n  },\n  // 初始用户信息\n  initialUserInfo: {\n    type: Object,\n    default: () => ({})\n  }\n});\n\n// Emits\nconst emit = defineEmits(['submit', 'skip', 'avatar-change', 'nickname-change']);\n\n// 响应式数据\nconst isSubmitting = ref(false);\nconst tempAvatarPaths = ref([]); // 临时头像文件路径，用于清理\nconst userInfo = ref({\n  avatarUrl: '',\n  nickName: '',\n  ...props.initialUserInfo\n});\n\n// 默认头像 - 使用头像管理工具\nconst defaultAvatarUrl = computed(() => {\n  return getDefaultAvatar('default', userInfo.value.gender);\n});\n\n// 头像预览URL\nconst avatarPreviewUrl = computed(() => {\n  return createAvatarPreviewUrl(userInfo.value.avatarUrl) || defaultAvatarUrl.value;\n});\n\n// 计算属性\nconst canSubmit = computed(() => {\n  return userInfo.value.nickName && \n         userInfo.value.nickName.trim().length > 0 && \n         userInfo.value.avatarUrl &&\n         !isSubmitting.value;\n});\n\n// 页面加载时获取已保存的用户信息\nonMounted(() => {\n  const storedInfo = wechatAuth.getStoredUserInfo();\n  if (storedInfo.wechatUserInfo) {\n    userInfo.value = {\n      ...userInfo.value,\n      ...storedInfo.wechatUserInfo\n    };\n  }\n});\n\n// 监听用户信息变化\nwatch(() => userInfo.value, (newVal) => {\n  emit('avatar-change', newVal.avatarUrl);\n  emit('nickname-change', newVal.nickName);\n}, { deep: true });\n\n// 处理头像加载错误\nfunction onAvatarLoadError() {\n  const fallbackUrl = handleAvatarError(userInfo.value.avatarUrl, {\n    gender: userInfo.value.gender,\n    useGenderDefault: true\n  });\n  userInfo.value.avatarUrl = fallbackUrl;\n}\n\n// 处理头像选择 - 增强版本\nasync function onChooseAvatar(event) {\n  const result = await withErrorBoundary(async () => {\n    const chooseResult = await wechatAuth.handleChooseAvatar(event);\n    if (chooseResult.success) {\n      // 记录临时文件路径用于清理\n      if (chooseResult.avatarUrl && chooseResult.avatarUrl.includes('tmp')) {\n        tempAvatarPaths.value.push(chooseResult.avatarUrl);\n      }\n\n      userInfo.value.avatarUrl = chooseResult.avatarUrl;\n\n      uni.showToast({\n        title: '头像设置成功',\n        icon: 'success',\n        duration: 1500\n      });\n\n      return chooseResult;\n    } else {\n      throw new Error('头像选择失败');\n    }\n  }, {\n    showError: false, // 使用微信专用错误处理\n    onError: (errorAnalysis) => {\n      console.error('头像选择失败:', errorAnalysis);\n      // 使用微信专用错误提示\n      showWechatError(errorAnalysis.originalMessage || '头像选择失败');\n    }\n  });\n\n  if (!result.success) {\n    // 错误已在 withErrorBoundary 中处理\n    console.warn('头像选择操作失败');\n  }\n}\n\n// 处理昵称输入\nfunction onNicknameInput(event) {\n  const value = event.detail.value;\n  userInfo.value.nickName = value;\n}\n\n// 处理昵称失焦 - 增强版本\nasync function onNicknameBlur() {\n  const nickname = userInfo.value.nickName;\n  if (!nickname || nickname.trim().length === 0) {\n    return;\n  }\n\n  await withErrorBoundary(async () => {\n    await wechatAuth.handleNicknameInput(nickname);\n  }, {\n    showError: false, // 使用微信专用错误处理\n    onError: (errorAnalysis) => {\n      console.error('昵称处理失败:', errorAnalysis);\n      // 使用微信专用错误提示\n      showWechatError(errorAnalysis.originalMessage || '昵称设置失败');\n    }\n  });\n}\n\n// 提交用户信息\nasync function onSubmit() {\n  if (!canSubmit.value || isSubmitting.value) {\n    return;\n  }\n\n  // 验证昵称\n  if (!userInfo.value.nickName || userInfo.value.nickName.trim().length === 0) {\n    uni.showToast({\n      title: '请输入昵称',\n      icon: 'none'\n    });\n    return;\n  }\n\n  // 验证头像\n  if (!userInfo.value.avatarUrl) {\n    uni.showToast({\n      title: '请选择头像',\n      icon: 'none'\n    });\n    return;\n  }\n\n  try {\n    isSubmitting.value = true;\n\n    // 1. 保存昵称到微信认证工具（本地存储）\n    await wechatAuth.handleNicknameInput(userInfo.value.nickName);\n\n    // 2. 提交用户信息到后端服务器\n    try {\n      const { api } = require('@/utils/api.js');\n      await api.user.updateUserInfo({\n        wechat_nickname: userInfo.value.nickName,\n        wechat_avatar_url: userInfo.value.avatarUrl\n      });\n\n      if (isDebug()) {\n        console.log('用户信息已成功提交到后端');\n      }\n    } catch (apiError) {\n      console.warn('提交用户信息到后端失败，但本地保存成功:', apiError);\n      // 不阻断流程，因为本地已保存成功\n    }\n\n    // 3. 触发提交事件\n    emit('submit', {\n      success: true,\n      userInfo: userInfo.value\n    });\n\n    uni.showToast({\n      title: '设置完成',\n      icon: 'success'\n    });\n  } catch (error) {\n    console.error('提交用户信息失败:', error);\n    uni.showToast({\n      title: error.message || '设置失败',\n      icon: 'none',\n      duration: 2000\n    });\n\n    emit('submit', {\n      success: false,\n      error: error.message\n    });\n  } finally {\n    isSubmitting.value = false;\n  }\n}\n\n// 跳过设置\nfunction onSkip() {\n  if (isSubmitting.value) {\n    return;\n  }\n\n  emit('skip', {\n    userInfo: userInfo.value\n  });\n}\n\n// 组件卸载时清理临时文件\nonUnmounted(() => {\n  if (tempAvatarPaths.value.length > 0) {\n    cleanupTempAvatars(tempAvatarPaths.value);\n  }\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.user-info-form {\n  padding: 40rpx;\n  background-color: #ffffff;\n}\n\n.avatar-section {\n  text-align: center;\n  margin-bottom: 60rpx;\n}\n\n.avatar-label {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 30rpx;\n}\n\n.avatar-button {\n  position: relative;\n  width: 200rpx;\n  height: 200rpx;\n  border-radius: 50%;\n  overflow: hidden;\n  border: none;\n  background: none;\n  padding: 0;\n  margin: 0 auto;\n}\n\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  border: 4rpx solid #e5e5e5;\n}\n\n.avatar-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  border-radius: 50%;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.avatar-button:active .avatar-overlay {\n  opacity: 1;\n}\n\n.avatar-icon {\n  font-size: 48rpx;\n  margin-bottom: 8rpx;\n}\n\n.avatar-text {\n  font-size: 24rpx;\n  color: #ffffff;\n}\n\n.nickname-section {\n  margin-bottom: 60rpx;\n}\n\n.nickname-label {\n  font-size: 32rpx;\n  font-weight: 500;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.nickname-input {\n  width: 100%;\n  height: 88rpx;\n  border: 2rpx solid #e5e5e5;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  font-size: 30rpx;\n  background-color: #ffffff;\n}\n\n.nickname-input:focus {\n  border-color: #007aff;\n}\n\n.nickname-tip {\n  font-size: 24rpx;\n  color: #999999;\n  margin-top: 16rpx;\n}\n\n.submit-section {\n  margin-bottom: 40rpx;\n}\n\n.submit-button {\n  width: 100%;\n  height: 88rpx;\n  background-color: #007aff;\n  color: #ffffff;\n  border-radius: 8rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  border: none;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.submit-button--disabled {\n  background-color: #cccccc;\n  color: #999999;\n}\n\n.skip-section {\n  text-align: center;\n}\n\n.skip-button {\n  background: none;\n  border: none;\n  color: #999999;\n  font-size: 28rpx;\n  padding: 20rpx;\n}\n</style>\n", "import Component from 'D:/work/不良资产系统/non-performing-assets/components/user-info-form/user-info-form.vue'\nwx.createComponent(Component)"], "names": ["ref", "computed", "getDefaultAvatar", "createAvatarPreviewUrl", "onMounted", "wechatAuth", "watch", "handleAvatarError", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "uni", "showWechatError", "isDebug", "onUnmounted", "cleanupTempAvatars"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgFA,UAAM,QAAQ;AAmBd,UAAM,OAAO;AAGb,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAC9B,UAAM,WAAWA,cAAAA,IAAI;AAAA,MACnB,WAAW;AAAA,MACX,UAAU;AAAA,MACV,GAAG,MAAM;AAAA,IACX,CAAC;AAGD,UAAM,mBAAmBC,cAAQ,SAAC,MAAM;AACtC,aAAOC,mBAAgB,iBAAC,WAAW,SAAS,MAAM,MAAM;AAAA,IAC1D,CAAC;AAGD,UAAM,mBAAmBD,cAAQ,SAAC,MAAM;AACtC,aAAOE,mBAAAA,uBAAuB,SAAS,MAAM,SAAS,KAAK,iBAAiB;AAAA,IAC9E,CAAC;AAGD,UAAM,YAAYF,cAAQ,SAAC,MAAM;AAC/B,aAAO,SAAS,MAAM,YACf,SAAS,MAAM,SAAS,KAAM,EAAC,SAAS,KACxC,SAAS,MAAM,aACf,CAAC,aAAa;AAAA,IACvB,CAAC;AAGDG,kBAAAA,UAAU,MAAM;AACd,YAAM,aAAaC,4BAAW;AAC9B,UAAI,WAAW,gBAAgB;AAC7B,iBAAS,QAAQ;AAAA,UACf,GAAG,SAAS;AAAA,UACZ,GAAG,WAAW;AAAA,QACpB;AAAA,MACG;AAAA,IACH,CAAC;AAGDC,kBAAK,MAAC,MAAM,SAAS,OAAO,CAAC,WAAW;AACtC,WAAK,iBAAiB,OAAO,SAAS;AACtC,WAAK,mBAAmB,OAAO,QAAQ;AAAA,IACzC,GAAG,EAAE,MAAM,KAAI,CAAE;AAGjB,aAAS,oBAAoB;AAC3B,YAAM,cAAcC,mBAAiB,kBAAC,SAAS,MAAM,WAAW;AAAA,QAC9D,QAAQ,SAAS,MAAM;AAAA,QACvB,kBAAkB;AAAA,MACtB,CAAG;AACD,eAAS,MAAM,YAAY;AAAA,IAC7B;AAGA,mBAAe,eAAe,OAAO;AACnC,YAAM,SAAS,MAAMC,mBAAAA,kBAAkB,YAAY;AACjD,cAAM,eAAe,MAAMH,iBAAAA,WAAW,mBAAmB,KAAK;AAC9D,YAAI,aAAa,SAAS;AAExB,cAAI,aAAa,aAAa,aAAa,UAAU,SAAS,KAAK,GAAG;AACpE,4BAAgB,MAAM,KAAK,aAAa,SAAS;AAAA,UAClD;AAED,mBAAS,MAAM,YAAY,aAAa;AAExCI,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAED,iBAAO;AAAA,QACb,OAAW;AACL,gBAAM,IAAI,MAAM,QAAQ;AAAA,QACzB;AAAA,MACL,GAAK;AAAA,QACD,WAAW;AAAA;AAAA,QACX,SAAS,CAAC,kBAAkB;AAC1BA,wBAAA,MAAA,MAAA,SAAA,uDAAc,WAAW,aAAa;AAEtCC,mCAAAA,gBAAgB,cAAc,mBAAmB,QAAQ;AAAA,QAC1D;AAAA,MACL,CAAG;AAED,UAAI,CAAC,OAAO,SAAS;AAEnBD,sBAAAA,MAAA,MAAA,QAAA,uDAAa,UAAU;AAAA,MACxB;AAAA,IACH;AAGA,aAAS,gBAAgB,OAAO;AAC9B,YAAM,QAAQ,MAAM,OAAO;AAC3B,eAAS,MAAM,WAAW;AAAA,IAC5B;AAGA,mBAAe,iBAAiB;AAC9B,YAAM,WAAW,SAAS,MAAM;AAChC,UAAI,CAAC,YAAY,SAAS,KAAI,EAAG,WAAW,GAAG;AAC7C;AAAA,MACD;AAED,YAAMD,mBAAiB,kBAAC,YAAY;AAClC,cAAMH,iBAAU,WAAC,oBAAoB,QAAQ;AAAA,MACjD,GAAK;AAAA,QACD,WAAW;AAAA;AAAA,QACX,SAAS,CAAC,kBAAkB;AAC1BI,wBAAA,MAAA,MAAA,SAAA,uDAAc,WAAW,aAAa;AAEtCC,mCAAAA,gBAAgB,cAAc,mBAAmB,QAAQ;AAAA,QAC1D;AAAA,MACL,CAAG;AAAA,IACH;AAGA,mBAAe,WAAW;AACxB,UAAI,CAAC,UAAU,SAAS,aAAa,OAAO;AAC1C;AAAA,MACD;AAGD,UAAI,CAAC,SAAS,MAAM,YAAY,SAAS,MAAM,SAAS,KAAI,EAAG,WAAW,GAAG;AAC3ED,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAGD,UAAI,CAAC,SAAS,MAAM,WAAW;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,qBAAa,QAAQ;AAGrB,cAAMJ,iBAAU,WAAC,oBAAoB,SAAS,MAAM,QAAQ;AAG5D,YAAI;AACF,gBAAM,EAAE,IAAG,IAAK,QAAQ,gBAAgB;AACxC,gBAAM,IAAI,KAAK,eAAe;AAAA,YAC5B,iBAAiB,SAAS,MAAM;AAAA,YAChC,mBAAmB,SAAS,MAAM;AAAA,UAC1C,CAAO;AAED,cAAIM,WAAO,QAAA,GAAI;AACbF,0BAAAA,MAAY,MAAA,OAAA,uDAAA,cAAc;AAAA,UAC3B;AAAA,QACF,SAAQ,UAAU;AACjBA,mGAAa,wBAAwB,QAAQ;AAAA,QAE9C;AAGD,aAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,UAAU,SAAS;AAAA,QACzB,CAAK;AAEDA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,QACZ,CAAK;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,uDAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,MAAM,WAAW;AAAA,UACxB,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAED,aAAK,UAAU;AAAA,UACb,SAAS;AAAA,UACT,OAAO,MAAM;AAAA,QACnB,CAAK;AAAA,MACL,UAAY;AACR,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAGA,aAAS,SAAS;AAChB,UAAI,aAAa,OAAO;AACtB;AAAA,MACD;AAED,WAAK,QAAQ;AAAA,QACX,UAAU,SAAS;AAAA,MACvB,CAAG;AAAA,IACH;AAGAG,kBAAAA,YAAY,MAAM;AAChB,UAAI,gBAAgB,MAAM,SAAS,GAAG;AACpCC,8CAAmB,gBAAgB,KAAK;AAAA,MACzC;AAAA,IACH,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;AChTD,GAAG,gBAAgB,SAAS;"}