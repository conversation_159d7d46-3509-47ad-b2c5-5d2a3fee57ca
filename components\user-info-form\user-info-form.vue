<template>
  <view class="user-info-form">
    <!-- 头像选择区域 -->
    <view class="avatar-section">
      <view class="avatar-label">设置头像</view>
      <button 
        class="avatar-button"
        open-type="chooseAvatar"
        @chooseavatar="onChooseAvatar"
        :disabled="isSubmitting"
      >
        <image
          class="avatar-image"
          :src="avatarPreviewUrl"
          mode="aspectFill"
          @error="onAvatarLoadError"
        />
        <view class="avatar-overlay">
          <text class="avatar-icon">📷</text>
          <text class="avatar-text">点击选择头像</text>
        </view>
      </button>
    </view>

    <!-- 昵称输入区域 -->
    <view class="nickname-section">
      <view class="nickname-label">设置昵称</view>
      <input
        class="nickname-input"
        type="nickname"
        placeholder="请输入您的昵称"
        v-model="userInfo.nickName"
        @blur="onNicknameBlur"
        @input="onNicknameInput"
        :disabled="isSubmitting"
        maxlength="20"
      />
      <view class="nickname-tip">昵称长度不超过20个字符</view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-button"
        :class="{ 'submit-button--disabled': !canSubmit || isSubmitting }"
        :disabled="!canSubmit || isSubmitting"
        @click="onSubmit"
      >
        <text v-if="isSubmitting">保存中...</text>
        <text v-else>{{ submitButtonText }}</text>
      </button>
    </view>

    <!-- 跳过按钮（可选） -->
    <view class="skip-section" v-if="allowSkip">
      <button 
        class="skip-button"
        @click="onSkip"
        :disabled="isSubmitting"
      >
        暂时跳过
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import wechatAuth from '@/utils/wechat-auth.js';
import { isDebug } from '@/config/env.js';
import {
  getDefaultAvatar,
  handleAvatarError,
  createAvatarPreviewUrl,
  cleanupTempAvatars
} from '@/utils/avatar-helper.js';
import { showError, withErrorBoundary } from '@/utils/error-handler.js';
import { showWechatError } from '@/utils/wechat-error-handler.js';

// Props
const props = defineProps({
  // 是否允许跳过
  allowSkip: {
    type: Boolean,
    default: false
  },
  // 提交按钮文案
  submitButtonText: {
    type: String,
    default: '完成设置'
  },
  // 初始用户信息
  initialUserInfo: {
    type: Object,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['submit', 'skip', 'avatar-change', 'nickname-change']);

// 响应式数据
const isSubmitting = ref(false);
const tempAvatarPaths = ref([]); // 临时头像文件路径，用于清理
const userInfo = ref({
  avatarUrl: '',
  nickName: '',
  ...props.initialUserInfo
});

// 默认头像 - 使用头像管理工具
const defaultAvatarUrl = computed(() => {
  return getDefaultAvatar('default', userInfo.value.gender);
});

// 头像预览URL
const avatarPreviewUrl = computed(() => {
  return createAvatarPreviewUrl(userInfo.value.avatarUrl) || defaultAvatarUrl.value;
});

// 计算属性
const canSubmit = computed(() => {
  return userInfo.value.nickName && 
         userInfo.value.nickName.trim().length > 0 && 
         userInfo.value.avatarUrl &&
         !isSubmitting.value;
});

// 页面加载时获取已保存的用户信息
onMounted(() => {
  const storedInfo = wechatAuth.getStoredUserInfo();
  if (storedInfo.wechatUserInfo) {
    userInfo.value = {
      ...userInfo.value,
      ...storedInfo.wechatUserInfo
    };
  }
});

// 监听用户信息变化
watch(() => userInfo.value, (newVal) => {
  emit('avatar-change', newVal.avatarUrl);
  emit('nickname-change', newVal.nickName);
}, { deep: true });

// 处理头像加载错误
function onAvatarLoadError() {
  const fallbackUrl = handleAvatarError(userInfo.value.avatarUrl, {
    gender: userInfo.value.gender,
    useGenderDefault: true
  });
  userInfo.value.avatarUrl = fallbackUrl;
}

// 处理头像选择 - 增强版本
async function onChooseAvatar(event) {
  const result = await withErrorBoundary(async () => {
    const chooseResult = await wechatAuth.handleChooseAvatar(event);
    if (chooseResult.success) {
      // 记录临时文件路径用于清理
      if (chooseResult.avatarUrl && chooseResult.avatarUrl.includes('tmp')) {
        tempAvatarPaths.value.push(chooseResult.avatarUrl);
      }

      userInfo.value.avatarUrl = chooseResult.avatarUrl;

      uni.showToast({
        title: '头像设置成功',
        icon: 'success',
        duration: 1500
      });

      return chooseResult;
    } else {
      throw new Error('头像选择失败');
    }
  }, {
    showError: false, // 使用微信专用错误处理
    onError: (errorAnalysis) => {
      console.error('头像选择失败:', errorAnalysis);
      // 使用微信专用错误提示
      showWechatError(errorAnalysis.originalMessage || '头像选择失败');
    }
  });

  if (!result.success) {
    // 错误已在 withErrorBoundary 中处理
    console.warn('头像选择操作失败');
  }
}

// 处理昵称输入
function onNicknameInput(event) {
  const value = event.detail.value;
  userInfo.value.nickName = value;
}

// 处理昵称失焦 - 增强版本
async function onNicknameBlur() {
  const nickname = userInfo.value.nickName;
  if (!nickname || nickname.trim().length === 0) {
    return;
  }

  await withErrorBoundary(async () => {
    await wechatAuth.handleNicknameInput(nickname);
  }, {
    showError: false, // 使用微信专用错误处理
    onError: (errorAnalysis) => {
      console.error('昵称处理失败:', errorAnalysis);
      // 使用微信专用错误提示
      showWechatError(errorAnalysis.originalMessage || '昵称设置失败');
    }
  });
}

// 提交用户信息
async function onSubmit() {
  if (!canSubmit.value || isSubmitting.value) {
    return;
  }

  // 验证昵称
  if (!userInfo.value.nickName || userInfo.value.nickName.trim().length === 0) {
    uni.showToast({
      title: '请输入昵称',
      icon: 'none'
    });
    return;
  }

  // 验证头像
  if (!userInfo.value.avatarUrl) {
    uni.showToast({
      title: '请选择头像',
      icon: 'none'
    });
    return;
  }

  try {
    isSubmitting.value = true;

    // 1. 保存昵称到微信认证工具（本地存储）
    await wechatAuth.handleNicknameInput(userInfo.value.nickName);

    // 2. 提交用户信息到后端服务器
    try {
      const { api } = require('@/utils/api.js');
      await api.user.updateUserInfo({
        wechat_nickname: userInfo.value.nickName,
        wechat_avatar_url: userInfo.value.avatarUrl
      });

      if (isDebug()) {
        console.log('用户信息已成功提交到后端');
      }
    } catch (apiError) {
      console.warn('提交用户信息到后端失败，但本地保存成功:', apiError);
      // 不阻断流程，因为本地已保存成功
    }

    // 3. 触发提交事件
    emit('submit', {
      success: true,
      userInfo: userInfo.value
    });

    uni.showToast({
      title: '设置完成',
      icon: 'success'
    });
  } catch (error) {
    console.error('提交用户信息失败:', error);
    uni.showToast({
      title: error.message || '设置失败',
      icon: 'none',
      duration: 2000
    });

    emit('submit', {
      success: false,
      error: error.message
    });
  } finally {
    isSubmitting.value = false;
  }
}

// 跳过设置
function onSkip() {
  if (isSubmitting.value) {
    return;
  }

  emit('skip', {
    userInfo: userInfo.value
  });
}

// 组件卸载时清理临时文件
onUnmounted(() => {
  if (tempAvatarPaths.value.length > 0) {
    cleanupTempAvatars(tempAvatarPaths.value);
  }
});
</script>

<style lang="scss" scoped>
.user-info-form {
  padding: 40rpx;
  background-color: #ffffff;
}

.avatar-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.avatar-label {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 30rpx;
}

.avatar-button {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  overflow: hidden;
  border: none;
  background: none;
  padding: 0;
  margin: 0 auto;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4rpx solid #e5e5e5;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.avatar-button:active .avatar-overlay {
  opacity: 1;
}

.avatar-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.avatar-text {
  font-size: 24rpx;
  color: #ffffff;
}

.nickname-section {
  margin-bottom: 60rpx;
}

.nickname-label {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 20rpx;
}

.nickname-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
  background-color: #ffffff;
}

.nickname-input:focus {
  border-color: #007aff;
}

.nickname-tip {
  font-size: 24rpx;
  color: #999999;
  margin-top: 16rpx;
}

.submit-section {
  margin-bottom: 40rpx;
}

.submit-button {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button--disabled {
  background-color: #cccccc;
  color: #999999;
}

.skip-section {
  text-align: center;
}

.skip-button {
  background: none;
  border: none;
  color: #999999;
  font-size: 28rpx;
  padding: 20rpx;
}
</style>
