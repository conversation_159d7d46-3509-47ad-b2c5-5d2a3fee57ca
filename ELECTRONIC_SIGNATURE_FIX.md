# 电子签名功能修复说明

## 修复的问题

### 1. ✅ 请求参数缺失问题
**问题**: 之前缺少必需的 `electronic_signature` 二进制图片参数
**修复**: 
- 确保 `name: 'electronic_signature'` 参数正确设置
- 使用 `filePath` 传递实际的文件路径
- 文件作为二进制数据正确上传

### 2. ✅ API调用位置优化
**问题**: 电子签名请求通过 `api.js` 封装方法调用，不够灵活
**修复**:
- 直接在 `agreement_signing.vue` 页面中使用 `uni.uploadFile`
- 更好地控制文件上传流程和错误处理
- 保持代码的可维护性

### 3. ✅ api.js文件优化
**问题**: `utils/api.js` 中包含复杂的文件上传逻辑
**修复**:
- 简化 `electronicSignature` 部分，只保留API路径定义
- 移除复杂的文件上传逻辑
- 提供 `getUpdateSignatureUrl()` 方法获取API地址

### 4. ✅ 技术要求实现
- ✅ `electronic_signature` 参数作为二进制文件正确传递
- ✅ 使用 form-data 格式上传
- ✅ 保持PUT方法调用
- ✅ 维持现有的错误处理机制

## 核心修改

### 1. 简化的API配置 (`utils/api.js`)

```javascript
// 电子签名相关 - 简化版本，只提供API路径
electronicSignature: {
  // 获取更新电子签名的API路径
  getUpdateSignatureUrl: (caseNumber) => {
    return `${getBaseURL()}${getApiPath.updateElectronicSignature(caseNumber)}`;
  }
}
```

### 2. 优化的文件上传逻辑 (`agreement_signing.vue`)

```javascript
const saveElectronicSignature = async (signatureBase64) => {
  // 1. 参数验证
  if (!caseNumber.value) {
    throw new Error('案件号不能为空');
  }

  // 2. 转换base64为临时文件
  const filePath = await base64ToTempFile(signatureBase64);
  
  // 3. 获取认证信息和API地址
  const token = uni.getStorageSync('access_token') || uni.getStorageSync('token');
  const tokenType = uni.getStorageSync('token_type') || 'Bearer';
  const apiUrl = api.electronicSignature.getUpdateSignatureUrl(caseNumber.value);

  // 4. 直接使用uni.uploadFile上传
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: apiUrl,
      filePath: filePath,
      name: 'electronic_signature', // 关键：确保参数名正确
      method: 'PUT', // 使用PUT方法
      formData: {
        case_number: caseNumber.value
      },
      header: {
        'Authorization': token ? `${tokenType} ${token}` : ''
      },
      success: (res) => {
        // 处理成功响应
      },
      fail: (error) => {
        // 处理失败情况
      }
    });
  });
};
```

### 3. 增强的文件转换函数

```javascript
const base64ToTempFile = (base64Data) => {
  return new Promise((resolve, reject) => {
    // 1. 移除base64前缀
    const base64 = base64Data.replace(/^data:image\/\w+;base64,/, '');
    
    // 2. 生成唯一文件名
    const fileName = `electronic_signature_${caseNumber.value}_${Date.now()}.png`;
    const filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;

    // 3. 写入文件系统
    const fs = wx.getFileSystemManager();
    fs.writeFile({
      filePath: filePath,
      data: base64,
      encoding: 'base64',
      success: () => resolve(filePath),
      fail: (error) => reject(new Error(`创建临时文件失败: ${error.errMsg}`))
    });
  });
};
```

## 关键改进点

### 1. 参数传递优化
- **之前**: 可能缺少 `electronic_signature` 文件参数
- **现在**: 明确设置 `name: 'electronic_signature'`，确保服务器能接收到文件

### 2. 错误处理增强
- 添加详细的日志输出
- 区分不同类型的错误（网络、服务器、文件系统）
- 提供更具体的错误信息

### 3. 文件处理改进
- 生成唯一的文件名，避免冲突
- 包含案件号和时间戳，便于调试
- 使用PNG格式确保兼容性

### 4. 调试信息完善
```javascript
console.log('开始上传电子签名:', {
  url: apiUrl,
  filePath: filePath,
  caseNumber: caseNumber.value
});
```

## 测试验证要点

### 1. 网络请求验证
在开发者工具Network面板中检查：
- **URL**: `/mediation_management/mediation_case/wechat/{case_number}/update_electronic_signature/`
- **Method**: PUT
- **Content-Type**: multipart/form-data
- **Body**: 包含 `electronic_signature` 文件和 `case_number` 参数

### 2. 文件上传验证
- 检查临时文件是否正确创建
- 验证文件大小是否合理
- 确认文件格式为PNG

### 3. 响应处理验证
- 检查服务器响应状态码
- 验证响应数据解析
- 确认错误处理逻辑

## 预期效果

修复后的功能应该能够：

1. ✅ 正确将签名的base64数据转换为PNG文件
2. ✅ 使用PUT方法和form-data格式上传文件
3. ✅ 服务器能够接收到 `electronic_signature` 二进制文件参数
4. ✅ 提供详细的错误信息和调试日志
5. ✅ 保持良好的用户体验（加载提示、成功/失败反馈）

## 故障排查

如果仍然存在问题，请检查：

1. **服务器端**: 确认接口支持PUT方法和multipart/form-data
2. **参数名**: 确认服务器期望的参数名是 `electronic_signature`
3. **文件格式**: 确认服务器支持PNG格式
4. **认证**: 确认token有效且有上传权限
5. **网络**: 检查网络连接和超时设置
