{"version": 3, "file": "work_order_detail.js", "sources": ["pages/work_order_detail/work_order_detail.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd29ya19vcmRlcl9kZXRhaWwvd29ya19vcmRlcl9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"work-order-detail-container\">\r\n\t\t<!-- 案件基本信息 -->\r\n\t\t<view class=\"work-order-card\">\r\n\t\t\t<view class=\"work-order-header\">\r\n\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t<text>调解案件号: </text>\r\n\t\t\t\t\t<text class=\"work-order-id\">{{caseNumber}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"work-order-status\">\r\n\t\t\t\t\t<text class=\"status-label\" :class=\"{'status-pending': caseStatus === '待确认'}\">{{caseStatus}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"work-order-date\">发起日期: {{initiateDate}}</view>\r\n\t\t\t<view class=\"work-order-date\" v-if=\"caseStatus === '已关闭'\">关闭日期: {{closeDate}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 进度条 -->\r\n\t\t<view class=\"progress-bar\">\r\n\t\t\t<view class=\"progress-steps\">\r\n\t\t\t\t<view class=\"progress-step active\">\r\n\t\t\t\t\t<view class=\"step-circle\">1</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">调解确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">2</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">方案确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">3</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">协议签署</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">4</view>\r\n\t\t\t\t\t<view class=\"step-label\">完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 调解信息 -->\r\n\t\t<view class=\"info-section\">\r\n\t\t\t<view class=\"section-title\">调解信息</view>\r\n\t\t\t<view class=\"info-item\" v-for=\"item in workOrderData.mediation_config\" :key=\"item.id\">\r\n\t\t\t\t<text class=\"info-label\">{{item.title}}</text>\r\n\t\t\t\t<text class=\"info-value\">{{item.value}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 文件列表 -->\r\n\t\t<view class=\"info-section\">\r\n\t\t\t<view class=\"section-title\">相关文件</view>\r\n\t\t\t<view class=\"file-item\" v-for=\"file in workOrderData.attachments\" :key=\"file.id\">\r\n\t\t\t\t<!-- 根据file.name的扩展名,判断是pdf还是图片 -->\r\n\t\t\t\t<i class=\"fas \" :class=\"file.name.split('.').pop() === 'pdf' ? 'fa-file-pdf' : 'fa-file-image'\" :style=\"{color: file.name.split('.').pop() === 'pdf' ? '#ff4d4f' : '#52c41a'}\"></i>\r\n\t\t\t\t<view class=\"file-content\"><text class=\"file-name\">{{file.name}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"info-section work-closing\" v-if=\"workOrderData.case_status_cn === '已关闭'\">\r\n\t\t\t<view class=\"section-title\">\r\n\t\t\t\t<i class=\"fas fa-info-circle\"></i>关闭原因\r\n\t\t\t</view>\r\n\t\t\t<view class=\"section-tip\">\r\n\t\t\t\t<text>{{workOrderData.closingReason || '调解案件已超过规定期限。'}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"action-buttons\" v-else>\r\n\t\t\t<button class=\"accept-button\" @click=\"handleAccept\"><i class=\"fas fa-check-circle\"></i>接受调解</button>\r\n\t\t\t<button class=\"reject-button\" @click=\"handleReject\"><i class=\"fas fa-clock\"></i>考虑一下</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { api } from '@/utils/api.js';\r\n\r\n// 接收参数\r\nconst caseNumber = ref('');\r\nconst initiateDate = ref('');\r\nconst closeDate = ref('');\r\nconst caseStatus = ref('');\r\nconst mediationProgress = ref('');\r\n\r\n// 调解数据\r\nconst workOrderData = ref([]);\r\n\r\n// 页面加载时获取参数 - uni-app标准方式\r\n// onLoad是uni-app专门用于接收页面参数的生命周期钩子\r\nonLoad((options) => {\r\n\tconsole.log('页面参数:', options);\r\n\t// 处理URL参数\r\n\tif (options && options.case_number) {\r\n\t\thandleUrlParams(options);\r\n\t}\r\n});\r\n\r\n// 组件挂载后的初始化逻辑\r\nonMounted(() => {\r\n\tconsole.log('工单详情页面组件已挂载');\r\n\t// 注意：参数获取逻辑已移至onLoad中，这里只保留组件挂载后的其他初始化操作\r\n});\r\n\r\n\r\n// 处理URL参数\r\nconst handleUrlParams = async (options) => {\r\n\r\n\tconst { case_number, initiate_date, close_date, case_status_cn, mediation_progress } = options;\r\n\r\n\t// 检查是否有完整参数\r\n\tconst hasCompleteParams = case_number && (initiate_date || close_date) && case_status_cn || mediation_progress;\r\n\r\n\tif (hasCompleteParams) {\r\n\t\t// 有完整参数，直接显示基本信息\r\n\t\tconsole.log(\"检测到完整参数，直接显示基本信息\");\r\n\t\tcaseNumber.value = decodeURIComponent(case_number);\r\n\t\tinitiateDate.value = decodeURIComponent(initiate_date);\r\n\t\tcloseDate.value = decodeURIComponent(close_date);\r\n\t\tcaseStatus.value = decodeURIComponent(case_status_cn);\r\n\t\tmediationProgress.value = decodeURIComponent(mediation_progress);\r\n\r\n\t\t// 获取详细的调解信息数据\r\n\t\tif (caseNumber.value) {\r\n\t\t\tfetchWorkOrderDetail(caseNumber.value);\r\n\t\t}\r\n\t} else if (case_number) {\r\n\t\t// 仅有案件编号，调用接口获取详细信息\r\n\t\tconsole.log(\"仅有案件编号，调用接口获取详细信息\");\r\n\t\tconst decodedCaseNumber = decodeURIComponent(case_number);\r\n\t\tcaseNumber.value = decodedCaseNumber;\r\n\r\n\t\tawait fetchMediationSingleDetail(decodedCaseNumber);\r\n\t\tawait fetchWorkOrderDetail(decodedCaseNumber);\r\n\t}\r\n}\r\n// 获取单条调解数据基本信息\r\nconst fetchMediationSingleDetail = async (caseNumber) => {\r\n  try {\r\n    uni.showLoading({ title: \"加载中...\" });\r\n\r\n    const result = await api.mediationQuery.getSingleDetail(caseNumber);\r\n\r\n    uni.hideLoading();\r\n\r\n    if (result.state === \"success\" && result.data) {\r\n      const data = result.data;\r\n      basicInfo.value.case_number = data.case_number || caseNumber;\r\n      basicInfo.value.initiate_date = data.initiate_date || '';\r\n      basicInfo.value.case_status_cn = data.case_status_cn || '';\r\n      basicInfo.value.mediation_progress = data.mediation_progress || '';\r\n    } else {\r\n      uni.showToast({\r\n        title: result.msg || \"获取案件信息失败\",\r\n        icon: \"none\",\r\n        duration: 2000,\r\n      });\r\n    }\r\n  } catch (error) {\r\n    uni.hideLoading();\r\n    console.error(\"获取案件详情失败:\", error);\r\n    uni.showToast({\r\n      title: \"获取案件信息失败\",\r\n      icon: \"none\",\r\n      duration: 2000,\r\n    });\r\n  }\r\n};\r\n// 获取调解确认数据\r\nconst fetchWorkOrderDetail = async (id) => {\r\n\tif (id) {\r\n\t\t// 使用API获取数据\r\n\t\tuni.showLoading({\r\n\t\t\ttitle: '加载中...'\r\n\t\t});\r\n\t\t// mediation_case_id (整数, 必需): 调解案件ID，通过URL路径参数传递\r\n\t\tconst result = await api.workOrder.getDetail(id)\r\n\t\t\t\r\n\t\tuni.hideLoading();\r\n\t\tif (result.state === \"success\" && result.data) {\r\n\t\t\tworkOrderData.value = result.data;\r\n\t\t} else {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: res.msg || '获取调解确认失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t} \r\n\t}\r\n};\r\n\r\n// 处理接受调解\r\nconst handleAccept = () => {\r\n\t// 显示确认对话框\r\n\tuni.showModal({\r\n\t\ttitle: '确认接受',\r\n\t\tcontent: '您确定要接受此调解吗？',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 用户点击确定\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 调用API接受调解\r\n\t\t\t\tif (caseNumber.value) {\r\n\t\t\t\t\tapi.workOrder.acceptWorkOrder(caseNumber.value)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tif (res.state === \"success\") {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 成功后跳转到调解方案确认页面\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\t\turl: `/pages/solution_confirm/solution_confirm?case_number=${res.data.case_number}`,\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到调解方案确认页面');\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.msg || '操作失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tconsole.error('接受调解失败', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '接受调解失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}else{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '缺少调解案件编号',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t});\r\n};\r\n\r\n// 考虑一下\r\nconst handleReject = () => {\r\n\tuni.redirectTo({\r\n\t\turl: `/pages/mediation_query/mediation_query`,\r\n\t});\r\n\t// 显示确认对话框\r\n\t/* uni.showModal({\r\n\t\ttitle: '确认拒绝',\r\n\t\tcontent: '您确定要拒绝此调解吗？',\r\n\t\tsuccess: (res) => {\r\n\t\t\tif (res.confirm) {\r\n\t\t\t\t// 用户点击确定\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '处理中...'\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\t// 调用API拒绝调解\r\n\t\t\t\tif (caseNumber.value) {\r\n\t\t\t\t\tapi.workOrder.rejectWorkOrder(caseNumber.value)\r\n\t\t\t\t\t\t.then(res => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tif (res.code === 0) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '调解已拒绝',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t// 成功后跳转到首页\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\t\turl: '/pages/index/index',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.log('跳转到首页');\r\n\t\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t\t\t\tconsole.error('跳转失败', err);\r\n\t\t\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle: '跳转失败',\r\n\t\t\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}, 1500);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: res.msg || '操作失败',\r\n\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\t.catch(err => {\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t\tconsole.error('拒绝调解失败', err);\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '拒绝调解失败',\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}); */\r\n};\r\n\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.work-order-detail-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.work-order-card {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.work-order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n\tfont-size: 29rpx;\r\n\tcolor: #333;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n\tpadding: 6rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n\tfont-size: 26rpx;\r\n\tpadding: 8rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: #999;\r\n\tcolor: #fff;\r\n}\r\n\r\n.status-pending {\r\n\tbackground-color: #faad14;\r\n}\r\n\r\n.work-order-date {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n}\r\n\r\n/* .progress-bar {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n} */\r\n\r\n.progress-steps {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-step {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tposition: relative;\r\n}\r\n\r\n.step-circle {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #e0e0e0;\r\n\tcolor: #fff;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n}\r\n\r\n.step-line {\r\n\tposition: absolute;\r\n\ttop: 30rpx;\r\n\tleft: 50%;\r\n\tright: -50%;\r\n\theight: 4rpx;\r\n\tbackground-color: #e0e0e0;\r\n\tz-index: 1;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n\tdisplay: none;\r\n}\r\n\r\n.step-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: #999;\r\n\ttext-align: center;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n\tbackground-color: #2979ff;\r\n}\r\n\r\n.progress-step.active .step-label {\r\n\tcolor: #2979ff;\r\n\tfont-weight: bold;\r\n}\r\n\r\n.info-section {\r\n\tbackground-color: #fff;\r\n\tborder-radius: 12rpx;\r\n\tpadding: 30rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: bold;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n}\r\n\r\n/* .section-title::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\twidth: 8rpx;\r\n\theight: 32rpx;\r\n\tbackground-color: #2979ff;\r\n\tborder-radius: 4rpx;\r\n} */\r\n\r\n.info-item {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n    align-items: flex-start;\r\n    gap: 16rpx;\r\n\tpadding: 20rpx 0;\r\n\tcolor: #333;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.info-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n.info-label {\r\n\tfont-weight: 500;\r\n}\r\n\r\n.file-item {\r\n    display: flex;\r\n    align-items: center;\r\n    cursor: pointer;\r\n    padding: 28rpx 0;\r\n    border-bottom: 2rpx solid rgba(0, 0, 0, 0.06);\r\n    transition: 0.3s;\r\n\t.fas{\r\n\t\tmargin-right: 24rpx;\r\n\t}\r\n}\r\n.file-item:last-child {\r\n    border-bottom: none;\r\n}\r\n.file-content {\r\n    min-width: 0;\r\n    display: flex;\r\n    align-items: center;\r\n    flex: 1 1 0%;\r\n}\r\n.file-name {\r\n    font-size: 30rpx;\r\n    font-weight: 500;\r\n    color: #333;\r\n    line-height: 1.3;\r\n    word-break: break-all;\r\n    transition: color 0.3s;\r\n}\r\n\r\n.action-buttons {\r\n\tdisplay: flex;\r\n\tgap: 30rpx;\r\n}\r\n\r\n.reject-button,\r\n.accept-button {\r\n\tflex: 1;\r\n\tdisplay: flex;\r\n\tflex-direction: row;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tgap: 16rpx;\r\n\tborder-radius: 16rpx;\r\n    box-shadow: rgba(0, 0, 0, 0.1) 0px 2px 4px;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.reject-button {\r\n\tbackground-color: #fff;\r\n\tcolor: #3b7eeb;\r\n\tborder: 2rpx solid #3b7eeb;\r\n}\r\n\r\n.accept-button {\r\n\tbackground-color: #3b7eeb;\r\n\tcolor: #fff;\r\n}\r\n.work-closing{\r\n\tbackground-color: rgb(255, 248, 248);\r\n    border:2rpx solid rgb(255, 237, 237);\r\n\t.section-title{\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor:#f5222d;\r\n\t\tfont-weight: bold;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t.fas{\r\n\t\t\tcolor: #f5222d;\r\n\t\t\tfont-size: 40rpx;\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t}\r\n\t}\r\n\t.section-tip{\r\n\t\tfont-size: 32rpx;\r\n\t\tcolor:#666;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/work_order_detail/work_order_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "uni", "onMounted", "caseNumber", "api", "res", "MiniProgramPage"], "mappings": ";;;;;;AAkFA,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,oBAAoBA,cAAAA,IAAI,EAAE;AAGhC,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAI5BC,kBAAM,OAAC,CAAC,YAAY;AACnBC,8FAAY,SAAS,OAAO;AAE5B,UAAI,WAAW,QAAQ,aAAa;AACnC,wBAAgB,OAAO;AAAA,MACvB;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACfD,oBAAAA,2EAAY,aAAa;AAAA,IAE1B,CAAC;AAID,UAAM,kBAAkB,OAAO,YAAY;AAE1C,YAAM,EAAE,aAAa,eAAe,YAAY,gBAAgB,mBAAoB,IAAG;AAGvF,YAAM,oBAAoB,gBAAgB,iBAAiB,eAAe,kBAAkB;AAE5F,UAAI,mBAAmB;AAEtBA,sBAAAA,2EAAY,kBAAkB;AAC9B,mBAAW,QAAQ,mBAAmB,WAAW;AACjD,qBAAa,QAAQ,mBAAmB,aAAa;AACrD,kBAAU,QAAQ,mBAAmB,UAAU;AAC/C,mBAAW,QAAQ,mBAAmB,cAAc;AACpD,0BAAkB,QAAQ,mBAAmB,kBAAkB;AAG/D,YAAI,WAAW,OAAO;AACrB,+BAAqB,WAAW,KAAK;AAAA,QACrC;AAAA,MACD,WAAU,aAAa;AAEvBA,sBAAAA,2EAAY,mBAAmB;AAC/B,cAAM,oBAAoB,mBAAmB,WAAW;AACxD,mBAAW,QAAQ;AAEnB,cAAM,2BAA2B,iBAAiB;AAClD,cAAM,qBAAqB,iBAAiB;AAAA,MAC5C;AAAA,IACF;AAEA,UAAM,6BAA6B,OAAOE,gBAAe;AACvD,UAAI;AACFF,sBAAAA,MAAI,YAAY,EAAE,OAAO,SAAU,CAAA;AAEnC,cAAM,SAAS,MAAMG,UAAG,IAAC,eAAe,gBAAgBD,WAAU;AAElEF,sBAAG,MAAC,YAAW;AAEf,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC7C,gBAAM,OAAO,OAAO;AACpB,oBAAU,MAAM,cAAc,KAAK,eAAeE;AAClD,oBAAU,MAAM,gBAAgB,KAAK,iBAAiB;AACtD,oBAAU,MAAM,iBAAiB,KAAK,kBAAkB;AACxD,oBAAU,MAAM,qBAAqB,KAAK,sBAAsB;AAAA,QACtE,OAAW;AACLF,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO,OAAO;AAAA,YACrB,MAAM;AAAA,YACN,UAAU;AAAA,UAClB,CAAO;AAAA,QACF;AAAA,MACF,SAAQ,OAAO;AACdA,sBAAG,MAAC,YAAW;AACfA,sBAAc,MAAA,MAAA,SAAA,wDAAA,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACF;AAAA,IACH;AAEA,UAAM,uBAAuB,OAAO,OAAO;AAC1C,UAAI,IAAI;AAEPA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACV,CAAG;AAED,cAAM,SAAS,MAAMG,UAAAA,IAAI,UAAU,UAAU,EAAE;AAE/CH,sBAAG,MAAC,YAAW;AACf,YAAI,OAAO,UAAU,aAAa,OAAO,MAAM;AAC9C,wBAAc,QAAQ,OAAO;AAAA,QAChC,OAAS;AACNA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO,IAAI,OAAO;AAAA,YAClB,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACD;AAAA,IACF;AAGA,UAAM,eAAe,MAAM;AAE1BA,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAACI,SAAQ;AACjB,cAAIA,KAAI,SAAS;AAEhBJ,0BAAAA,MAAI,YAAY;AAAA,cACf,OAAO;AAAA,YACZ,CAAK;AAGD,gBAAI,WAAW,OAAO;AACrBG,wBAAAA,IAAI,UAAU,gBAAgB,WAAW,KAAK,EAC5C,KAAK,CAAAC,SAAO;AACZJ,8BAAG,MAAC,YAAW;AACf,oBAAII,KAAI,UAAU,WAAW;AAC5BJ,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAOI,KAAI;AAAA,oBACX,MAAM;AAAA,oBACN,UAAU;AAAA,kBACnB,CAAS;AAGD,6BAAW,MAAM;AAChBJ,kCAAAA,MAAI,WAAW;AAAA,sBACd,KAAK,wDAAwDI,KAAI,KAAK,WAAW;AAAA,sBACjF,SAAS,MAAM;AACdJ,sCAAAA,MAAA,MAAA,OAAA,wDAAY,aAAa;AAAA,sBACzB;AAAA,sBACD,MAAM,CAAC,QAAQ;AACdA,sCAAc,MAAA,MAAA,SAAA,wDAAA,QAAQ,GAAG;AACzBA,sCAAAA,MAAI,UAAU;AAAA,0BACb,OAAO;AAAA,0BACP,MAAM;AAAA,wBAClB,CAAY;AAAA,sBACD;AAAA,oBACX,CAAU;AAAA,kBACD,GAAE,IAAI;AAAA,gBACf,OAAc;AACNA,gCAAAA,MAAI,UAAU;AAAA,oBACb,OAAOI,KAAI,OAAO;AAAA,oBAClB,MAAM;AAAA,kBACf,CAAS;AAAA,gBACD;AAAA,cACR,CAAO,EACA,MAAM,SAAO;AACbJ,8BAAG,MAAC,YAAW;AACfA,8BAAA,MAAA,MAAA,SAAA,wDAAc,UAAU,GAAG;AAC3BA,8BAAAA,MAAI,UAAU;AAAA,kBACb,OAAO;AAAA,kBACP,MAAM;AAAA,gBACd,CAAQ;AAAA,cACR,CAAO;AAAA,YACP,OAAS;AACJA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACZ,CAAM;AAAA,YACD;AAAA,UACD;AAAA,QACD;AAAA,MACH,CAAE;AAAA,IACF;AAGA,UAAM,eAAe,MAAM;AAC1BA,oBAAAA,MAAI,WAAW;AAAA,QACd,KAAK;AAAA,MACP,CAAE;AAAA,IA2DF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClUA,GAAG,WAAWK,SAAe;"}