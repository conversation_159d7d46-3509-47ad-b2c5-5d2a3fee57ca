/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-ad806119:root {
  --primary-color: #3b7eeb;
  --primary-light: #e6f0ff;
  --transition-normal: all 0.3s ease;
  --text-secondary: #666;
  --text-color: #333;
  --text-light: #999;
}
.content.data-v-ad806119 {
  height: calc(100% - 188rpx);
  overflow-y: auto;
  padding: 30rpx;
  padding-bottom: 140rpx;
  background-color: #f8fafc;
}
.gradient-bg.data-v-ad806119 {
  background: linear-gradient(135deg, var(--primary-light) 0%, #e6f0ff 100%);
  border: none;
  text-align: center;
  padding: 60rpx 40rpx;
}
.icon-box.data-v-ad806119 {
  width: 120rpx;
  height: 120rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx;
}
.icon-box .fas.data-v-ad806119 {
  color: white;
  font-size: 48rpx;
}
.text.data-v-ad806119 {
  margin: 0 0 20rpx 0;
  color: var(--primary-color);
  font-size: 36rpx;
  font-weight: bold;
}
.text-gray.data-v-ad806119 {
  color: var(--text-secondary);
  font-size: 28rpx;
  line-height: 1.6;
  margin: 0;
}
.card.data-v-ad806119 {
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 36rpx;
  margin-bottom: 30rpx;
  transition: var(--transition-normal);
  border: 1px solid rgba(0, 0, 0, 0.03);
}
.form-container.data-v-ad806119 {
  display: flex;
  flex-direction: column;
}
.form-container .form-card.data-v-ad806119 {
  overflow: hidden;
  margin-bottom: 20rpx;
}
.form-container .form-card .card-header.data-v-ad806119 {
  padding: 30rpx 30rpx 20rpx 10rpx;
  display: flex;
  align-items: center;
}
.form-container .form-card .card-header .fas.data-v-ad806119 {
  margin-right: 8px;
  color: var(--primary-color);
}
.form-container .form-card .card-header .card-title.data-v-ad806119 {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: 800;
}
.text-lg.data-v-ad806119 {
  text-align: center;
  color: var(--text-color);
  font-size: 36rpx;
  font-weight: bold;
}
.action-buttons.data-v-ad806119 {
  padding: 20rpx 0;
}
.confirm-btn.data-v-ad806119 {
  background-color: #3b7eeb;
  color: #fff;
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
}
.confirm-btn .fas.data-v-ad806119 {
  margin-right: 20rpx;
  font-size: 36rpx;
  color: var(--bg-primary);
}
.confirm-btn.data-v-ad806119:hover {
  background-color: #2a6ed8;
}
.text-xs.data-v-ad806119 {
  display: flex;
  align-items: center;
  margin-top: 25rpx;
  font-size: 24rpx;
  color: var(--text-light);
}
.text-xs .fas.data-v-ad806119 {
  color: var(--text-light);
  font-size: 24rpx;
  margin-right: 10rpx;
}
.code-input.data-v-ad806119 {
  display: flex;
  gap: 24rpx;
}
.code-input .input-field.data-v-ad806119 {
  position: relative;
  flex: 1;
}
.code-input .code-btn.data-v-ad806119 {
  white-space: nowrap;
  min-width: 100px;
  background-color: var(--primary-color);
  color: white;
}
.data-v-ad806119 .uni-easyinput__content-input {
  height: 80rpx;
  font-size: 28rpx;
}