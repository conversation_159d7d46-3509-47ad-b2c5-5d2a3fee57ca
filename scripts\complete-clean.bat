@echo off
chcp 65001 >nul
echo ===============================================
echo 🧹 完全清理uni-popup编译错误解决方案
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 当前项目目录: %CD%
echo.

echo ⚠️ 警告：此操作将完全清理所有编译缓存和uni-ui依赖
echo 请确保已保存所有重要工作！
echo.
set /p confirm=是否继续？(Y/N): 
if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 🚀 开始完全清理...
echo.

echo 📁 第一步：删除所有编译输出...
if exist "unpackage" (
    echo 正在删除 unpackage 目录...
    rmdir /s /q "unpackage" 2>nul
    timeout /t 1 >nul
    if exist "unpackage" (
        echo ⚠️ 强制删除 unpackage...
        for /d %%i in ("unpackage\*") do rmdir /s /q "%%i" 2>nul
        for %%i in ("unpackage\*") do del /q "%%i" 2>nul
        rmdir /q "unpackage" 2>nul
    )
    if not exist "unpackage" (
        echo ✅ unpackage 目录已删除
    ) else (
        echo ❌ unpackage 目录删除失败，请手动删除后重新运行
        pause
        exit /b
    )
) else (
    echo ℹ️ unpackage 目录不存在
)

echo.
echo 📦 第二步：完全删除node_modules中的uni-ui...
if exist "node_modules\@dcloudio" (
    echo 正在删除 node_modules\@dcloudio...
    rmdir /s /q "node_modules\@dcloudio" 2>nul
    echo ✅ @dcloudio 依赖已删除
) else (
    echo ℹ️ node_modules\@dcloudio 不存在
)

echo.
echo 🗂️ 第三步：清理所有缓存目录...
for %%d in (".hbuilderx" ".hbuilder" ".vscode" ".idea" "dist" "build") do (
    if exist "%%d" (
        rmdir /s /q "%%d" 2>nul
        echo ✅ 已删除 %%d
    )
)

echo.
echo 📄 第四步：清理临时文件...
for %%f in ("*.tmp" "*.temp" "*.log" ".DS_Store" "Thumbs.db") do (
    if exist "%%f" (
        del /q "%%f" 2>nul
        echo ✅ 已删除 %%f
    )
)

echo.
echo 🔧 第五步：验证关键配置文件...
echo 检查 package.json...
findstr /c:"@dcloudio/uni-ui" package.json >nul 2>&1
if %errorlevel%==0 (
    echo ❌ package.json 中仍有 uni-ui 依赖引用
    echo 请手动编辑 package.json 删除 "@dcloudio/uni-ui" 依赖
) else (
    echo ✅ package.json 中已无 uni-ui 依赖
)

echo 检查 pages.json...
findstr /c:"uni-ui" pages.json >nul 2>&1
if %errorlevel%==0 (
    echo ❌ pages.json 中仍有 uni-ui 配置
    echo 请手动编辑 pages.json 删除 uni-ui 相关配置
) else (
    echo ✅ pages.json 中已无 uni-ui 配置
)

echo.
echo 🔍 第六步：检查代码中的uni-popup引用...
findstr /s /i "uni-popup" pages\*.vue >nul 2>&1
if %errorlevel%==0 (
    echo ❌ 发现代码中仍有 uni-popup 引用
    echo 请检查以下文件：
    findstr /s /i /n "uni-popup" pages\*.vue
) else (
    echo ✅ 代码中已无 uni-popup 引用
)

echo.
echo ===============================================
echo ✨ 完全清理完成！
echo ===============================================
echo.
echo 📋 接下来请严格按照以下步骤操作：
echo.
echo 1️⃣ 完全关闭 HBuilderX
echo.
echo 2️⃣ 等待 10 秒钟
echo.
echo 3️⃣ 重新打开 HBuilderX
echo.
echo 4️⃣ 打开项目后，在 HBuilderX 中执行：
echo    菜单 "运行" → "清理项目"
echo.
echo 5️⃣ 清理完成后，重新编译：
echo    "运行" → "运行到小程序模拟器" → "微信开发者工具"
echo.
echo 6️⃣ 如果编译成功：
echo    ✅ 问题已解决！
echo.
echo 7️⃣ 如果仍有错误：
echo    - 检查控制台的具体错误信息
echo    - 确认是否有其他文件引用了uni-ui组件
echo    - 可能需要重新创建项目
echo.
echo ⚠️ 重要提示：
echo   清理后首次编译可能较慢，这是正常现象
echo   请耐心等待编译完成
echo.
echo ===============================================
echo 按任意键退出...
pause >nul
