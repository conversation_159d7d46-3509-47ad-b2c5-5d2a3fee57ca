<view class="solution-confirm-container data-v-dd2a7f81"><view class="work-order-card data-v-dd2a7f81"><view class="work-order-header data-v-dd2a7f81"><view class="work-order-title data-v-dd2a7f81"><text class="data-v-dd2a7f81">工单号: </text><text class="work-order-id data-v-dd2a7f81">{{a}}</text></view><view class="work-order-status data-v-dd2a7f81"><text class="{{['status-label', 'data-v-dd2a7f81', c && 'status-processing']}}">{{b}}</text></view></view><view class="work-order-date data-v-dd2a7f81">发起日期: {{d}}</view></view><view class="progress-bar data-v-dd2a7f81"><view class="progress-steps data-v-dd2a7f81"><view class="progress-step completed data-v-dd2a7f81"><view class="step-circle data-v-dd2a7f81">1</view><view class="step-line completed data-v-dd2a7f81"></view><view class="step-label data-v-dd2a7f81">调解确认</view></view><view class="progress-step completed data-v-dd2a7f81"><view class="step-circle data-v-dd2a7f81">2</view><view class="step-line completed data-v-dd2a7f81"></view><view class="step-label data-v-dd2a7f81">方案确认</view></view><view class="progress-step active data-v-dd2a7f81"><view class="step-circle data-v-dd2a7f81">3</view><view class="step-line data-v-dd2a7f81"></view><view class="step-label data-v-dd2a7f81">协议签署</view></view><view class="progress-step data-v-dd2a7f81"><view class="step-circle data-v-dd2a7f81">4</view><view class="step-label data-v-dd2a7f81">完成</view></view></view></view><view class="solutions-container data-v-dd2a7f81"><view class="solution-card data-v-dd2a7f81"><view class="solution-bg data-v-dd2a7f81"></view><view class="agreement-document data-v-dd2a7f81"><view class="agreement-document-icon data-v-dd2a7f81"><view class="fas fa-file-pdf data-v-dd2a7f81"></view></view><view class="agreement-document-title data-v-dd2a7f81">《调解协议》</view><text class="agreement-document-tip data-v-dd2a7f81">请点击下方按钮查看完整PDF协议文件</text><button class="agreement-document-button data-v-dd2a7f81" bindtap="{{h}}"><view wx:if="{{e}}" class="fas fa-spinner fa-spin data-v-dd2a7f81"></view><view wx:else class="{{['data-v-dd2a7f81', f]}}"></view> {{g}}</button><view class="solution-border data-v-dd2a7f81"></view><view class="confirm-signing data-v-dd2a7f81"><view class="sign data-v-dd2a7f81"><view class="fas fa-pen-nib data-v-dd2a7f81"></view><view class="sign-title data-v-dd2a7f81">确认签署</view><text class="sign-tip data-v-dd2a7f81">{{i}}</text></view><view class="{{['agreement-check', 'data-v-dd2a7f81', n && 'agreement-check-signed']}}" bindtap="{{o}}"><view class="agreement-content data-v-dd2a7f81"><uni-data-checkbox wx:if="{{k}}" class="data-v-dd2a7f81" u-i="dd2a7f81-0" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"/><text class="{{['electronic-signature', 'data-v-dd2a7f81', m && 'signature-completed']}}">{{l}}</text></view></view></view></view></view></view><view class="action-buttons data-v-dd2a7f81"><button class="{{['confirm-button', 'data-v-dd2a7f81', s]}}" bindtap="{{t}}" disabled="{{v}}"><view wx:if="{{p}}" class="fas fa-spinner fa-spin data-v-dd2a7f81"></view><view wx:else class="{{['data-v-dd2a7f81', q]}}"></view> {{r}}</button></view><canvas-autograph wx:if="{{y}}" class="data-v-dd2a7f81" bindcomplete="{{w}}" u-i="dd2a7f81-1" bind:__l="__l" bindupdateModelValue="{{x}}" u-p="{{y}}"></canvas-autograph></view>