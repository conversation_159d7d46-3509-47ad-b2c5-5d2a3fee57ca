<template>
	<view class="real-case-container">
		<!-- 搜索和筛选区域 -->
		<view class="search-filter-section">
			<view class="search-box">
				<uni-easyinput
					v-model="searchKeyword"
					placeholder="搜索案例关键词"
					suffixIcon="search"
					:clearable="true"
					@confirm="handleSearch"
					@iconClick="handleSearch"
				></uni-easyinput>
			</view>
			
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{ active: activeFilter === item.value }"
					v-for="item in filterOptions" 
					:key="item.value"
					@click="handleFilterChange(item.value)"
				>
					{{ item.label }}
				</view>
			</view>
		</view>

		<!-- 案例列表 -->
		<view class="case-list">
			<view 
				class="case-card" 
				v-for="caseItem in filteredCases" 
				:key="caseItem.id"
				@click="navigateToDetail(caseItem.id)"
			>
				<!-- 案例头部 -->
				<view class="case-header">
					<view class="case-title">{{ caseItem.title }}</view>
					<view class="case-date">{{ caseItem.date }}</view>
				</view>

				<!-- 案例标签 -->
				<view class="case-tags">
					<text class="case-tag type-tag">{{ caseItem.type }}</text>
					<text class="case-tag status-tag" :class="getStatusClass(caseItem.status)">
						{{ caseItem.status }}
					</text>
				</view>

				<!-- 金额信息 -->
				<view class="amount-info">
					<view class="amount-row">
						<text class="amount-label">债务金额：</text>
						<text class="debt-amount">¥{{ formatAmount(caseItem.debtAmount) }}</text>
					</view>
					<view class="amount-row">
						<text class="amount-label">调解结果：</text>
						<text class="resolved-amount">¥{{ formatAmount(caseItem.resolvedAmount) }}</text>
					</view>
					<view class="reduction-info">
						<text class="reduction-rate">{{ caseItem.reductionRate }}</text>
					</view>
				</view>

				<!-- 案例简介 -->
				<view class="case-summary">
					<text class="summary-text">{{ caseItem.summary }}</text>
				</view>

				<!-- 查看详情按钮 -->
				<view class="detail-button">
					<text class="button-text">查看详情</text>
					<text class="arrow-icon">›</text>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredCases.length === 0 && !loading">
			<text class="empty-text">暂无相关案例</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';

// 响应式数据
const searchKeyword = ref('');
const activeFilter = ref('all');
const loading = ref(false);
const caseList = ref([]);

// 筛选选项
const filterOptions = [
	{ label: '全部', value: 'all' },
	{ label: '信用卡', value: 'credit_card' },
	{ label: '车贷', value: 'car_loan' },
	{ label: '房贷', value: 'mortgage' },
	{ label: '其他', value: 'other' }
];

// 生命周期钩子
onMounted(() => {
	console.log('案例展示页面已加载');
	fetchCaseList();
});

// 获取案例列表
const fetchCaseList = async () => {
	loading.value = true;
	
	// 模拟API延迟
	setTimeout(() => {
		caseList.value = [
			{
				id: '1',
				title: '信用卡欠款减免案例',
				type: '信用卡欠款',
				debtAmount: 80000,
				resolvedAmount: 28000,
				reductionRate: '减免65%',
				date: '2023-10-15',
				status: '调解成功',
				summary: '张先生因疫情影响，无法按时偿还卡款，通过调解平台申请调解，最终与银行达成和解',
				category: 'credit_card'
			},
			{
				id: '2',
				title: '车贷逾期调解案例',
				type: '车贷逾期',
				debtAmount: 120000,
				resolvedAmount: 120000,
				reductionRate: '减免全部罚息',
				date: '2023-09-28',
				status: '延期还款',
				summary: '李女士因生意周转困难，车贷逾期3个月，通过调解平台寻求帮助',
				category: 'car_loan'
			},
			{
				id: '3',
				title: '房贷协商案例',
				type: '房贷协商',
				debtAmount: 850000,
				resolvedAmount: 680000,
				reductionRate: '减免20%',
				date: '2023-08-12',
				status: '调解成功',
				summary: '王先生失业后无法正常还贷，通过协商重新制定还款计划',
				category: 'mortgage'
			},
			{
				id: '4',
				title: '小额贷款纠纷案例',
				type: '小额贷款',
				debtAmount: 35000,
				resolvedAmount: 25000,
				reductionRate: '减免28.5%',
				date: '2023-11-03',
				status: '调解成功',
				summary: '陈女士因投资失败导致无法还款，最终达成分期还款协议',
				category: 'other'
			},
			{
				id: '5',
				title: '信用卡分期调解案例',
				type: '信用卡分期',
				debtAmount: 45000,
				resolvedAmount: 36000,
				reductionRate: '减免20%',
				date: '2023-09-15',
				status: '调解成功',
				summary: '刘先生因家庭变故影响收入，通过调解获得更合理的还款方案',
				category: 'credit_card'
			}
		];
		loading.value = false;
	}, 800);
};

// 过滤后的案例列表
const filteredCases = computed(() => {
	let filtered = caseList.value;
	
	// 按类型筛选
	if (activeFilter.value !== 'all') {
		filtered = filtered.filter(item => item.category === activeFilter.value);
	}
	
	// 按搜索关键词筛选
	if (searchKeyword.value.trim()) {
		const keyword = searchKeyword.value.trim().toLowerCase();
		filtered = filtered.filter(item => 
			item.title.toLowerCase().includes(keyword) ||
			item.type.toLowerCase().includes(keyword) ||
			item.summary.toLowerCase().includes(keyword)
		);
	}
	
	return filtered;
});

// 处理搜索
const handleSearch = () => {
	// 触发计算属性重新计算
	console.log('搜索关键词：', searchKeyword.value);
};

// 处理筛选变化
const handleFilterChange = (filterValue) => {
	activeFilter.value = filterValue;
};

// 跳转到详情页面
const navigateToDetail = (caseId) => {
	uni.navigateTo({
		url: `/pages/case_detail/case_detail?id=${caseId}`
	});
};

// 格式化金额显示
const formatAmount = (amount) => {
	if (!amount) return '0.00';
	return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });
};

// 获取状态样式类
const getStatusClass = (status) => {
	switch (status) {
		case '调解成功':
			return 'success';
		case '延期还款':
			return 'warning';
		case '调解中':
			return 'processing';
		default:
			return 'default';
	}
};
</script>

<style lang="scss" scoped>
.real-case-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding-bottom: 30rpx;
}

.search-filter-section {
	background-color: #ffffff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-box {
	margin-bottom: 30rpx;
}

.filter-tabs {
	display: flex;
	gap: 20rpx;
	overflow-x: auto;
}

.filter-tab {
	padding: 15rpx 30rpx;
	background-color: #f8f8f8;
	color: #666;
	border-radius: 30rpx;
	font-size: 26rpx;
	white-space: nowrap;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background-color: #2979ff;
	color: #ffffff;
}

.case-list {
	padding: 0 20rpx;
}

.case-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: relative;
}

.case-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.case-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	line-height: 1.4;
}

.case-date {
	font-size: 24rpx;
	color: #999;
	margin-left: 20rpx;
}

.case-tags {
	display: flex;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.case-tag {
	padding: 8rpx 16rpx;
	font-size: 22rpx;
	border-radius: 20rpx;
}

.type-tag {
	background-color: #e3f2fd;
	color: #1976d2;
}

.status-tag {
	&.success {
		background-color: #e8f5e8;
		color: #4caf50;
	}
	
	&.warning {
		background-color: #fff3e0;
		color: #ff9800;
	}
	
	&.processing {
		background-color: #f3e5f5;
		color: #9c27b0;
	}
	
	&.default {
		background-color: #f5f5f5;
		color: #666;
	}
}

.amount-info {
	margin-bottom: 25rpx;
}

.amount-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.amount-label {
	font-size: 26rpx;
	color: #666;
}

.debt-amount {
	font-size: 26rpx;
	color: #f44336;
	font-weight: 600;
}

.resolved-amount {
	font-size: 26rpx;
	color: #4caf50;
	font-weight: 600;
}

.reduction-info {
	text-align: right;
	margin-top: 5rpx;
}

.reduction-rate {
	font-size: 24rpx;
	color: #ff9800;
	font-weight: 500;
}

.case-summary {
	margin-bottom: 25rpx;
}

.summary-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.detail-button {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 15rpx 0;
	border-top: 2rpx solid #f0f0f0;
	margin-top: 20rpx;
}

.button-text {
	font-size: 28rpx;
	color: #2979ff;
	margin-right: 10rpx;
}

.arrow-icon {
	font-size: 32rpx;
	color: #2979ff;
	transform: rotate(90deg);
}

.empty-state, .loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.empty-text, .loading-text {
	font-size: 28rpx;
	color: #999;
}

/* uni-easyinput 组件样式覆盖 */
:deep(.uni-easyinput__content) {
	height: 80rpx;
	background-color: #f8f8f8;
	border: none;
	border-radius: 40rpx;
	padding: 0 30rpx;
}

:deep(.uni-easyinput__content-input) {
	font-size: 28rpx;
}
</style>