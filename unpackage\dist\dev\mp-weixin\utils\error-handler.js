"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const ERROR_TYPES = {
  NETWORK: "network",
  // 网络错误
  TIMEOUT: "timeout",
  // 超时错误
  AUTHORIZATION: "authorization",
  // 授权错误
  VALIDATION: "validation",
  // 验证错误
  SERVER: "server",
  // 服务器错误
  USER_CANCELLED: "user_cancelled",
  // 用户取消
  SYSTEM: "system",
  // 系统错误
  UNKNOWN: "unknown"
  // 未知错误
};
const ERROR_LEVELS = {
  INFO: "info",
  // 信息级别
  WARNING: "warning",
  // 警告级别
  ERROR: "error",
  // 错误级别
  CRITICAL: "critical"
  // 严重错误级别
};
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: {
    title: "网络连接失败",
    message: "请检查网络连接后重试",
    icon: "none"
  },
  [ERROR_TYPES.TIMEOUT]: {
    title: "请求超时",
    message: "网络响应较慢，请稍后重试",
    icon: "none"
  },
  [ERROR_TYPES.AUTHORIZATION]: {
    title: "授权失败",
    message: "请重新授权后继续使用",
    icon: "none"
  },
  [ERROR_TYPES.VALIDATION]: {
    title: "输入有误",
    message: "请检查输入信息是否正确",
    icon: "none"
  },
  [ERROR_TYPES.SERVER]: {
    title: "服务器错误",
    message: "服务器暂时无法响应，请稍后重试",
    icon: "none"
  },
  [ERROR_TYPES.USER_CANCELLED]: {
    title: "操作已取消",
    message: "您已取消当前操作",
    icon: "none"
  },
  [ERROR_TYPES.SYSTEM]: {
    title: "系统错误",
    message: "系统出现异常，请重启应用后重试",
    icon: "none"
  },
  [ERROR_TYPES.UNKNOWN]: {
    title: "未知错误",
    message: "发生了未知错误，请重试",
    icon: "none"
  }
};
function analyzeError(error) {
  let message = "";
  let stack = "";
  if (error instanceof Error) {
    message = error.message || "";
    stack = error.stack || "";
  } else if (typeof error === "string") {
    message = error;
  } else if (error && error.errMsg) {
    message = error.errMsg;
  } else {
    message = String(error || "");
  }
  const lowerMessage = message.toLowerCase();
  if (lowerMessage.includes("network") || lowerMessage.includes("网络") || lowerMessage.includes("connection") || lowerMessage.includes("连接") || lowerMessage.includes("request:fail")) {
    return {
      type: ERROR_TYPES.NETWORK,
      level: ERROR_LEVELS.WARNING,
      originalMessage: message,
      stack
    };
  }
  if (lowerMessage.includes("timeout") || lowerMessage.includes("超时") || lowerMessage.includes("time out")) {
    return {
      type: ERROR_TYPES.TIMEOUT,
      level: ERROR_LEVELS.WARNING,
      originalMessage: message,
      stack
    };
  }
  if (lowerMessage.includes("auth") && !lowerMessage.includes("fail") || lowerMessage.includes("授权") && lowerMessage.includes("失败") || lowerMessage.includes("permission denied") || lowerMessage.includes("权限不足") || lowerMessage.includes("token") && lowerMessage.includes("invalid")) {
    return {
      type: ERROR_TYPES.AUTHORIZATION,
      level: ERROR_LEVELS.ERROR,
      originalMessage: message,
      stack
    };
  }
  if (lowerMessage.includes("cancel") || lowerMessage.includes("取消") || lowerMessage.includes("abort") || lowerMessage.includes("中止")) {
    return {
      type: ERROR_TYPES.USER_CANCELLED,
      level: ERROR_LEVELS.INFO,
      originalMessage: message,
      stack
    };
  }
  if (lowerMessage.includes("valid") || lowerMessage.includes("验证") || lowerMessage.includes("format") || lowerMessage.includes("格式") || lowerMessage.includes("required") || lowerMessage.includes("必填")) {
    return {
      type: ERROR_TYPES.VALIDATION,
      level: ERROR_LEVELS.WARNING,
      originalMessage: message,
      stack
    };
  }
  if (lowerMessage.includes("server") || lowerMessage.includes("服务器") || lowerMessage.includes("500") || lowerMessage.includes("502") || lowerMessage.includes("503") || lowerMessage.includes("504")) {
    return {
      type: ERROR_TYPES.SERVER,
      level: ERROR_LEVELS.ERROR,
      originalMessage: message,
      stack
    };
  }
  if (lowerMessage.includes("system") || lowerMessage.includes("系统") || lowerMessage.includes("crash") || lowerMessage.includes("崩溃")) {
    return {
      type: ERROR_TYPES.SYSTEM,
      level: ERROR_LEVELS.CRITICAL,
      originalMessage: message,
      stack
    };
  }
  return {
    type: ERROR_TYPES.UNKNOWN,
    level: ERROR_LEVELS.ERROR,
    originalMessage: message,
    stack
  };
}
function getFriendlyErrorMessage(errorAnalysis, customMessages = {}) {
  const defaultMessage = ERROR_MESSAGES[errorAnalysis.type] || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];
  const customMessage = customMessages[errorAnalysis.type];
  return {
    ...defaultMessage,
    ...customMessage,
    originalMessage: errorAnalysis.originalMessage,
    type: errorAnalysis.type,
    level: errorAnalysis.level
  };
}
function showError(error, options = {}) {
  const defaultOptions = {
    duration: 2e3,
    showType: true,
    // 是否在调试模式下显示错误类型
    customMessages: {},
    ...options
  };
  const errorAnalysis = analyzeError(error);
  const friendlyMessage = getFriendlyErrorMessage(errorAnalysis, defaultOptions.customMessages);
  if (config_env.isDebug()) {
    common_vendor.index.__f__("error", "at utils/error-handler.js:238", "错误详情:", {
      type: errorAnalysis.type,
      level: errorAnalysis.level,
      original: errorAnalysis.originalMessage,
      friendly: friendlyMessage.message,
      stack: errorAnalysis.stack
    });
  }
  let displayMessage = friendlyMessage.message;
  if (config_env.isDebug() && defaultOptions.showType) {
    displayMessage = `[${errorAnalysis.type}] ${displayMessage}`;
  }
  if (errorAnalysis.level === ERROR_LEVELS.INFO && errorAnalysis.type === ERROR_TYPES.USER_CANCELLED) {
    return;
  }
  common_vendor.index.showToast({
    title: displayMessage,
    icon: friendlyMessage.icon || "none",
    duration: defaultOptions.duration,
    mask: false
  });
}
function withErrorBoundary(operation, options = {}) {
  const defaultOptions = {
    showError: true,
    fallbackValue: null,
    onError: null,
    ...options
  };
  return new Promise(async (resolve) => {
    try {
      const result = await operation();
      resolve({ success: true, data: result, error: null });
    } catch (error) {
      const errorAnalysis = analyzeError(error);
      if (config_env.isDebug()) {
        common_vendor.index.__f__("error", "at utils/error-handler.js:347", "错误边界捕获错误:", errorAnalysis);
      }
      if (defaultOptions.showError) {
        showError(error, options);
      }
      if (typeof defaultOptions.onError === "function") {
        defaultOptions.onError(errorAnalysis);
      }
      resolve({
        success: false,
        data: defaultOptions.fallbackValue,
        error: errorAnalysis
      });
    }
  });
}
exports.ERROR_TYPES = ERROR_TYPES;
exports.showError = showError;
exports.withErrorBoundary = withErrorBoundary;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/error-handler.js.map
