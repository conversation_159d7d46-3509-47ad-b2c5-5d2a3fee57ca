<template>
  <view class="user-info-page">
    <!-- 顶部标题区域 -->
    <view class="header-section">
      <view class="title">完善个人信息</view>
      <view class="subtitle">设置您的头像和昵称，让其他用户更好地认识您</view>
    </view>

    <!-- 用户信息表单 -->
    <view class="form-section">
      <user-info-form
        :allow-skip="true"
        submit-button-text="完成设置"
        :initial-user-info="initialUserInfo"
        @submit="handleSubmit"
        @skip="handleSkip"
        @avatar-change="handleAvatarChange"
        @nickname-change="handleNicknameChange"
      />
    </view>

    <!-- 说明文字 -->
    <view class="info-section">
      <view class="info-item">
        <text class="info-icon">🔒</text>
        <text class="info-text">您的个人信息将被安全保护</text>
      </view>
      <view class="info-item">
        <text class="info-icon">✨</text>
        <text class="info-text">完善信息后可享受更好的服务体验</text>
      </view>
    </view>

    <!-- 加载遮罩 -->
    <view class="loading-mask" v-if="isProcessing">
      <view class="loading-content">
        <uni-load-more status="loading" :content-text="loadingText"></uni-load-more>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import wechatAuth from '@/utils/wechat-auth.js';
import userStore from '@/utils/user-store.js';

// 响应式数据
const isProcessing = ref(false);
const initialUserInfo = ref({});

// 加载文案
const loadingText = ref({
  contentText: {
    contentdown: '正在保存...',
    contentrefresh: '正在保存...',
    contentnomore: '保存完成'
  }
});

// 页面加载
onMounted(() => {
  loadUserInfo();
});

// 加载用户信息
function loadUserInfo() {
  try {
    const storedInfo = wechatAuth.getStoredUserInfo();
    if (storedInfo.wechatUserInfo) {
      initialUserInfo.value = storedInfo.wechatUserInfo;
    }
  } catch (error) {
    console.error('加载用户信息失败:', error);
  }
}

// 处理头像变化
function handleAvatarChange(avatarUrl) {
  console.log('头像已更新:', avatarUrl);
}

// 处理昵称变化
function handleNicknameChange(nickname) {
  console.log('昵称已更新:', nickname);
}

// 处理提交
async function handleSubmit(result) {
  if (!result.success) {
    uni.showToast({
      title: result.error || '设置失败',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  try {
    isProcessing.value = true;

    // 更新用户状态管理
    const completeUserData = wechatAuth.getCompleteUserData();
    userStore.setWechatUserInfo(result.userInfo);

    // 显示成功提示
    uni.showToast({
      title: '设置完成',
      icon: 'success',
      duration: 1500
    });

    // 延迟跳转到首页
    setTimeout(() => {
      uni.switchTab({
        url: '/pages/index/index'
      });
    }, 1500);

  } catch (error) {
    console.error('保存用户信息失败:', error);
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none',
      duration: 2000
    });
  } finally {
    isProcessing.value = false;
  }
}

// 处理跳过
function handleSkip() {
  uni.showModal({
    title: '确认跳过',
    content: '跳过设置后，您可以稍后在个人中心完善信息',
    confirmText: '确认跳过',
    cancelText: '继续设置',
    success: (res) => {
      if (res.confirm) {
        // 用户确认跳过
        uni.switchTab({
          url: '/pages/index/index'
        });
      }
    }
  });
}

// 页面标题
uni.setNavigationBarTitle({
  title: '完善个人信息'
});
</script>

<style lang="scss" scoped>
.user-info-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 40rpx 0;
}

.header-section {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}

.form-section {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}

.info-section {
  padding: 40rpx;
  background-color: #ffffff;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-icon {
  font-size: 32rpx;
  margin-right: 20rpx;
}

.info-text {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}

.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}
</style>
