{"version": 3, "file": "webview.js", "sources": ["pages/webview/webview.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvd2Vidmlldy93ZWJ2aWV3LnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"webview-container\">\r\n    <web-view :src=\"url\" v-if=\"url\"></web-view>\r\n    <view v-else class=\"loading-container\">\r\n      <text class=\"loading-text\">加载中...</text>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      url: ''\r\n    };\r\n  },\r\n  onLoad(options) {\r\n    console.log('webview页面接收参数:', options);\r\n    if (options && options.url) {\r\n      this.url = decodeURIComponent(options.url);\r\n      console.log('解码后的URL:', this.url);\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.webview-container {\r\n  width: 100%;\r\n  height: 100vh;\r\n}\r\n\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 100vh;\r\n}\r\n\r\n.loading-text {\r\n  color: #999;\r\n  font-size: 14px;\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/webview/webview.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni"], "mappings": ";;AAUA,MAAK,YAAU;AAAA,EACb,OAAO;AACL,WAAO;AAAA,MACL,KAAK;AAAA;EAER;AAAA,EACD,OAAO,SAAS;AACdA,kBAAA,MAAA,MAAA,OAAA,mCAAY,kBAAkB,OAAO;AACrC,QAAI,WAAW,QAAQ,KAAK;AAC1B,WAAK,MAAM,mBAAmB,QAAQ,GAAG;AACzCA,oBAAA,MAAA,MAAA,OAAA,mCAAY,YAAY,KAAK,GAAG;AAAA,IAClC;AAAA,EACF;AACF;;;;;;;;;ACtBA,GAAG,WAAW,eAAe;"}