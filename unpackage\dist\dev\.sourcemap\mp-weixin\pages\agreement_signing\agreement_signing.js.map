{"version": 3, "file": "agreement_signing.js", "sources": ["pages/agreement_signing/agreement_signing.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWdyZWVtZW50X3NpZ25pbmcvYWdyZWVtZW50X3NpZ25pbmcudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"solution-confirm-container\">\r\n\t\t<!-- 工单基本信息 -->\r\n\t\t<view class=\"work-order-card\">\r\n\t\t\t<view class=\"work-order-header\">\r\n\t\t\t\t<view class=\"work-order-title\">\r\n\t\t\t\t\t<text>工单号: </text>\r\n\t\t\t\t\t<text class=\"work-order-id\">{{workOrderData.id || 'MED20230001'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"work-order-status\">\r\n\t\t\t\t\t<text class=\"status-label\" :class=\"{'status-processing': workOrderData.status === '进行中'}\">{{workOrderData.status || '进行中'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"work-order-date\">发起日期: {{workOrderData.createDate || '2023-11-01'}}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 进度条 -->\r\n\t\t<view class=\"progress-bar\">\r\n\t\t\t<view class=\"progress-steps\">\r\n\t\t\t\t<view class=\"progress-step completed\">\r\n\t\t\t\t\t<view class=\"step-circle\">1</view>\r\n\t\t\t\t\t<view class=\"step-line completed\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">调解确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step completed\">\r\n\t\t\t\t\t<view class=\"step-circle\">2</view>\r\n\t\t\t\t\t<view class=\"step-line completed\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">方案确认</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step active\">\r\n\t\t\t\t\t<view class=\"step-circle\">3</view>\r\n\t\t\t\t\t<view class=\"step-line\"></view>\r\n\t\t\t\t\t<view class=\"step-label\">协议签署</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"progress-step\">\r\n\t\t\t\t\t<view class=\"step-circle\">4</view>\r\n\t\t\t\t\t<view class=\"step-label\">完成</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 协议 -->\r\n\t\t<view class=\"solutions-container\">\r\n\t\t\t<view class=\"solution-card\">\r\n\t\t\t\t<view class=\"solution-bg\"></view>\r\n\t\t\t\t<view class=\"agreement-document\">\r\n\t\t\t\t\t<view class=\"agreement-document-icon\">\r\n\t\t\t\t\t\t<i class=\"fas fa-file-pdf\"></i>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"agreement-document-title\">《调解协议》</view>\r\n\t\t\t\t\t<text class=\"agreement-document-tip\">请点击下方按钮查看完整PDF协议文件</text>\r\n\t\t\t\t\t<button class=\"agreement-document-button\" @click=\"handlePreview\">\r\n\t\t\t\t\t\t<i v-if=\"isLoadingPreview\" class=\"fas fa-spinner fa-spin\"></i>\r\n\t\t\t\t\t\t<i v-else :class=\"preview.icon\"></i>\r\n\t\t\t\t\t\t{{ isLoadingPreview ? '正在加载PDF' : preview.text }}\r\n\t\t\t\t\t</button>\r\n\t\t\t\t\t<view class=\"solution-border\"></view>\r\n\t\t\t\t\t<view class=\"confirm-signing\">\r\n\t\t\t\t\t\t<view class=\"sign\">\r\n\t\t\t\t\t\t\t<i class=\"fas fa-pen-nib\"></i>\r\n\t\t\t\t\t\t\t<view class=\"sign-title\">确认签署</view>\r\n\t\t\t\t\t\t\t<text class=\"sign-tip\">{{ isSigned ? '协议签署已完成' : '请勾选下方确认项进行电子签署' }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view \r\n\t\t\t\t\t\t\tclass=\"agreement-check\"\r\n\t\t\t\t\t\t\t:class=\"{'agreement-check-signed': isSigned}\"\r\n\t\t\t\t\t\t\t@click=\"openCanvas\">\r\n\t\t\t\t\t\t\t\t<view class=\"agreement-content\">\r\n\t\t\t\t\t\t\t\t\t<uni-data-checkbox \r\n\t\t\t\t\t\t\t\t\t\tv-model=\"agreementStatus\"\r\n\t\t\t\t\t\t\t\t\t\tmultiple\r\n\t\t\t\t\t\t\t\t\t\t:localdata=\"checkboxData\"\r\n\t\t\t\t\t\t\t\t\t\t:disabled=\"isSigned\" />\r\n\t\t\t\t\t\t\t\t\t<text class=\"electronic-signature\" :class=\"{'signature-completed': isSigned}\">\r\n\t\t\t\t\t\t\t\t\t\t{{ isSigned ? '电子签名已生效，协议具有法律效力' : '点击此处进入电子签名界面' }}\r\n\t\t\t\t\t\t\t\t\t</text>\r\n\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 底部操作按钮 -->\r\n\t\t<view class=\"action-buttons\">\r\n\t\t\t<button \r\n\t\t\t\tclass=\"confirm-button\" \r\n\t\t\t\t:class=\"buttonConfig.class\"\r\n\t\t\t\t@click=\"handleConfirm\"\r\n\t\t\t\t:disabled=\"isLoading || (!isSigned && !agreementStatus.includes('1'))\">\r\n\t\t\t\t<!-- 加载状态显示 -->\r\n\t\t\t\t<i v-if=\"isLoading\" class=\"fas fa-spinner fa-spin\"></i>\r\n\t\t\t\t<i v-else :class=\"buttonConfig.icon\"></i>\r\n\t\t\t\t{{ isLoading ? '正在确认...' : buttonConfig.text }}\r\n\t\t\t</button>\r\n\t\t</view>\r\n\r\n\t\t<!-- 引入电子签名 -->\r\n\t\t<canvas-autograph \r\n\t\t\tv-model=\"isCanvas\" \r\n\t\t\t@complete=\"complete\"\r\n\t\t\t:showSignatureLine=\"true\"\r\n\t\t\tsignatureLabel=\"签名：\"\r\n\t\t\t:signatureLineY=\"60\"\r\n\t\t\tsignatureLineColor=\"#2979ff\"\r\n\t\t></canvas-autograph>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\nimport { api } from '@/utils/api.js';\r\nimport CanvasAutograph from '@/components/canvas-autograph/canvas-autograph.vue';\r\n\r\n// 接收的页面参数\r\nconst caseNumber = ref('');\r\nconst initiateDate = ref('');\r\nconst closeDate = ref('');\r\nconst caseStatus = ref('');\r\n\r\n// 工单基本数据\r\nconst workOrderData = ref({});\r\n\r\n/**\r\n * 签名状态管理\r\n * @type {boolean} 是否已完成签名\r\n */\r\nconst isSigned = ref(false);\r\n\r\n/**\r\n * 加载状态管理\r\n * @type {boolean} 是否正在处理预览协议\r\n */\r\nconst isLoadingPreview = ref(false);\r\n\r\n/**\r\n * 动态按钮配置计算属性\r\n * @returns {Object} 按钮配置对象\r\n */\r\nconst preview = computed(() => {\r\n\treturn {\r\n\t\ticon: 'fas fa-expand',\r\n\t\ttext: '全屏查看协议内容',\r\n\t\tclass: 'preview-btn'\r\n\t};\r\n});\r\n/**\r\n * 协议同意状态\r\n * @type {string} 复选框选中值\r\n */\r\nconst agreementStatus = ref([]);\r\n/**\r\n * 签名数据存储\r\n * @type {string} base64签名图片数据\r\n */\r\nconst signatureData = ref('');\r\n\r\n/**\r\n * 加载状态管理\r\n * @type {boolean} 是否正在处理签名\r\n */\r\nconst isLoading = ref(false);\r\n\r\n/**\r\n * 复选框配置数据\r\n * @type {Array}\r\n */\r\nconst hobbys = ref([\r\n\t{\r\n\t\ttext: '我已阅读并同意《调解协议》的全部条款',\r\n\t\tvalue: '1',\r\n\t\tdisabled: false\r\n\t}\r\n]);\r\n\r\n/**\r\n * 动态复选框配置计算属性\r\n * 根据签名状态返回不同的复选框配置\r\n * @returns {Array} 复选框配置数组\r\n */\r\nconst checkboxData = computed(() => {\r\n\tif (isSigned.value) {\r\n\t\treturn [\r\n\t\t\t{\r\n\t\t\t\ttext: '协议签署完成',\r\n\t\t\t\tvalue: 0,\r\n\t\t\t\tdisabled: true\r\n\t\t\t}\r\n\t\t];\r\n\t}\r\n\treturn hobbys.value;\r\n});\r\n\r\n/**\r\n * 动态按钮配置计算属性\r\n * 根据签名状态和复选框状态返回不同的按钮配置\r\n * @returns {Object} 按钮配置对象\r\n */\r\nconst buttonConfig = computed(() => {\r\n\tif (isSigned.value) {\r\n\t\treturn {\r\n\t\t\ticon: 'fas fa-check-circle',\r\n\t\t\ttext: '协议签署完成',\r\n\t\t\tclass: 'success-btn'\r\n\t\t};\r\n\t}\r\n\treturn {\r\n\t\ticon: 'fas fa-file-signature',\r\n\t\ttext: '确认协议签署',\r\n\t\tclass: !agreementStatus.value.includes('1') ? 'disabled-btn' : 'sign-btn'\r\n\t};\r\n});\r\n/**\r\n * 保存签名状态到本地存储\r\n */\r\nconst saveSignatureState = () => {\r\n\ttry {\r\n\t\tconst stateData = {\r\n\t\t\tisSigned: isSigned.value,\r\n\t\t\tagreementStatus: agreementStatus.value,\r\n\t\t\tsignatureData: signatureData.value,\r\n\t\t\ttimestamp: Date.now()\r\n\t\t};\r\n\t\tuni.setStorageSync('agreement_signature_state', JSON.stringify(stateData));\r\n\t} catch (error) {\r\n\t\tconsole.error('保存签名状态失败:', error);\r\n\t}\r\n};\r\n\r\n/**\r\n * 从本地存储加载签名状态\r\n */\r\nconst loadSignatureState = () => {\r\n\ttry {\r\n\t\tconst savedState = uni.getStorageSync('agreement_signature_state');\r\n\t\tif (savedState) {\r\n\t\t\tconst stateData = JSON.parse(savedState);\r\n\t\t\t// 检查数据是否在24小时内有效\r\n\t\t\tif (Date.now() - stateData.timestamp < 24 * 60 * 60 * 1000) {\r\n\t\t\t\tisSigned.value = stateData.isSigned || false;\r\n\t\t\t\tagreementStatus.value = stateData.agreementStatus || [];\r\n\t\t\t\tsignatureData.value = stateData.signatureData || '';\r\n\t\t\t}\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('加载签名状态失败:', error);\r\n\t}\r\n};\r\n/**\r\n * 全屏查看协议内容按钮点击处理\r\n * 根据当前状态执行不同的操作逻辑\r\n */\r\nconst handlePreview = async () => {\r\n\ttry {\r\n\t\tisLoadingPreview.value = true;\r\n\r\n\t\t/* // 模拟加载过程\r\n\t\tawait new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n\t\t// 构建文件预览参数\r\n\t\tconst fileParams = {\r\n\t\t\tfileUrl: encodeURIComponent('http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx'),\r\n\t\t\tfileType: encodeURIComponent('pdf'),\r\n\t\t\tfileName: encodeURIComponent('调解协议.pdf'),\r\n\t\t\tcaseNumber: encodeURIComponent(caseNumber.value || '485')\r\n\t\t};\r\n\r\n\t\t// 构建URL参数字符串\r\n\t\tconst paramsString = Object.entries(fileParams)\r\n\t\t\t.map(([key, value]) => `${key}=${value}`)\r\n\t\t\t.join('&'); \r\n\t\t// 跳转到协议预览页面\r\n\t\tuni.navigateTo({\r\n\t\t\turl: `/pages/protocol_preview/protocol_preview?${paramsString}`\r\n\t\t});*/\r\n\r\n\t\t// 跳转到协议预览页面\r\n\t\tuni.navigateTo({\r\n\t\t\turl: `/pages/protocol_preview/protocol_preview`\r\n\t\t});\r\n\t} catch (error) {\r\n\t\tconsole.error('跳转失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '跳转失败，请重试',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t} finally {\r\n\t\tisLoadingPreview.value = false;\r\n\t}\r\n};\r\n/**\r\n * 底部确认按钮点击处理\r\n * 根据当前状态执行不同的操作逻辑\r\n */\r\nconst handleConfirm = async () => {\r\n\ttry {\r\n\t\t// 如果已签名，执行确认完成流程并跳转\r\n\t\tif (isSigned.value) {\r\n\t\t\tisLoading.value = true;\r\n\r\n\t\t\t// 检查按钮文本是否为\"协议签署完成\"\r\n\t\t\tconst isAgreementCompleted = buttonConfig.value.text === '协议签署完成';\r\n\r\n\t\t\tif (isAgreementCompleted) {\r\n\t\t\t\t// 模拟确认处理过程\r\n\t\t\t\tawait new Promise(resolve => setTimeout(resolve, 1500));\r\n\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '协议确认完成',\r\n\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\tduration: 1500\r\n\t\t\t\t});\r\n\r\n\t\t\t\t// 延时跳转到联系方式确认页面\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/contact_information/contact_information'\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 1500);\r\n\t\t\t} else {\r\n\t\t\t\t// 其他情况的处理逻辑\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '操作完成',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\r\n\t\t\treturn;\r\n\t\t}\r\n\r\n\t\t// 如果已勾选协议但未签名，引导用户进行签名\r\n\t\tif (agreementStatus.value.includes('1')) {\r\n\t\t\tuni.showModal({\r\n\t\t\t\ttitle: '提示',\r\n\t\t\t\tcontent: '您已同意协议条款，请进行电子签名以完成签署流程',\r\n\t\t\t\tconfirmText: '去签名',\r\n\t\t\t\tcancelText: '稍后',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\topenCanvas();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t} else {\r\n\t\t\t// 如果未勾选协议，提示先勾选\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请先勾选同意协议条款',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('处理确认操作失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '操作失败，请重试',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 电子签名弹框状态\r\n * @type {boolean}\r\n */\r\nconst isCanvas = ref(false);\r\n\r\n/**\r\n * 签名完成回调函数\r\n * 处理签名完成后的状态更新和数据保存\r\n * @param {string} signatureBase64 - 签名的base64图片数据\r\n */\r\nconst complete = async (signatureBase64) => {\r\n\ttry {\r\n\t\tisLoading.value = true;\r\n\t\tconsole.log('签名数据:', signatureBase64);\r\n\r\n\t\t// 保存签名数据到本地\r\n\t\tsignatureData.value = signatureBase64;\r\n\r\n\t\t// 调用API保存电子签名到服务器\r\n\t\tawait saveElectronicSignature(signatureBase64);\r\n\r\n\t\t// 更新签名状态\r\n\t\tisSigned.value = true;\r\n\t\tagreementStatus.value = ['1'];\r\n\r\n\t\t// 保存状态到本地存储\r\n\t\tsaveSignatureState();\r\n\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '签名保存成功',\r\n\t\t\ticon: 'success'\r\n\t\t});\r\n\t} catch (error) {\r\n\t\tconsole.error('处理签名失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '签名保存失败，请重试',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t\t// 重置签名状态\r\n\t\tisSigned.value = false;\r\n\t\tagreementStatus.value = [];\r\n\t\tsignatureData.value = '';\r\n\t} finally {\r\n\t\tisLoading.value = false;\r\n\t}\r\n};\r\n\r\n/**\r\n * 保存电子签名到服务器\r\n * @param {string} signatureBase64 - 签名的base64图片数据\r\n */\r\nconst saveElectronicSignature = async (signatureBase64) => {\r\n\tif (!caseNumber.value) {\r\n\t\tthrow new Error('案件号不能为空');\r\n\t}\r\n\r\n\ttry {\r\n\t\t// 将base64转换为临时文件\r\n\t\tconst filePath = await base64ToTempFile(signatureBase64);\r\n\r\n\t\t// 获取认证信息\r\n\t\tconst token = uni.getStorageSync('access_token') || uni.getStorageSync('token');\r\n\t\tconst tokenType = uni.getStorageSync('token_type') || 'Bearer';\r\n\r\n\t\t// 获取API URL\r\n\t\tconst apiUrl = api.electronicSignature.getUpdateSignatureUrl(caseNumber.value);\r\n\r\n\t\tconsole.log('开始上传电子签名:', {\r\n\t\t\turl: apiUrl,\r\n\t\t\tfilePath: filePath,\r\n\t\t\tcaseNumber: caseNumber.value\r\n\t\t});\r\n\r\n\t\t// 直接使用uni.uploadFile进行文件上传\r\n\t\treturn new Promise((resolve, reject) => {\r\n\t\t\tuni.uploadFile({\r\n\t\t\t\turl: apiUrl,\r\n\t\t\t\tfilePath: filePath,\r\n\t\t\t\tname: 'electronic_signature', // 确保参数名为 electronic_signature\r\n\t\t\t\tmethod: 'PUT', // 使用PUT方法\r\n\t\t\t\tformData: {\r\n\t\t\t\t\tcase_number: caseNumber.value\r\n\t\t\t\t},\r\n\t\t\t\theader: {\r\n\t\t\t\t\t'Authorization': token ? `${tokenType} ${token}` : ''\r\n\t\t\t\t},\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('电子签名上传响应:', res);\r\n\r\n\t\t\t\t\t// 检查响应状态\r\n\t\t\t\t\tif (res.statusCode === 200 || res.statusCode === 201) {\r\n\t\t\t\t\t\ttry {\r\n\t\t\t\t\t\t\tconst responseData = JSON.parse(res.data);\r\n\t\t\t\t\t\t\tif (responseData.success !== false) {\r\n\t\t\t\t\t\t\t\tconsole.log('电子签名保存成功:', responseData);\r\n\t\t\t\t\t\t\t\tresolve(responseData);\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\treject(new Error(responseData.message || '保存电子签名失败'));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t} catch (parseError) {\r\n\t\t\t\t\t\t\t// 如果解析失败，但状态码是成功的，认为上传成功\r\n\t\t\t\t\t\t\tconsole.log('响应解析失败，但状态码正常，认为上传成功');\r\n\t\t\t\t\t\t\tresolve({ success: true, data: res.data });\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treject(new Error(`服务器错误: HTTP ${res.statusCode}`));\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\tfail: (error) => {\r\n\t\t\t\t\tconsole.error('电子签名上传失败:', error);\r\n\t\t\t\t\treject(new Error(error.errMsg || '网络请求失败'));\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t});\r\n\t} catch (error) {\r\n\t\tconsole.error('保存电子签名失败:', error);\r\n\t\tthrow error;\r\n\t}\r\n};\r\n\r\n/**\r\n * 将base64图片数据转换为临时文件\r\n * @param {string} base64Data - base64图片数据\r\n * @returns {Promise<string>} 临时文件路径\r\n */\r\nconst base64ToTempFile = (base64Data) => {\r\n\treturn new Promise((resolve, reject) => {\r\n\t\ttry {\r\n\t\t\t// 移除base64前缀，获取纯base64数据\r\n\t\t\tconst base64 = base64Data.replace(/^data:image\\/\\w+;base64,/, '');\r\n\r\n\t\t\t// 生成唯一的临时文件名，使用PNG格式确保兼容性\r\n\t\t\tconst fileName = `electronic_signature_${caseNumber.value}_${Date.now()}.png`;\r\n\t\t\tconst filePath = `${wx.env.USER_DATA_PATH}/${fileName}`;\r\n\r\n\t\t\tconsole.log('开始创建临时文件:', {\r\n\t\t\t\tfileName: fileName,\r\n\t\t\t\tfilePath: filePath,\r\n\t\t\t\tbase64Length: base64.length\r\n\t\t\t});\r\n\r\n\t\t\t// 使用微信小程序文件系统API将base64数据写入文件\r\n\t\t\tconst fs = wx.getFileSystemManager();\r\n\t\t\tfs.writeFile({\r\n\t\t\t\tfilePath: filePath,\r\n\t\t\t\tdata: base64,\r\n\t\t\t\tencoding: 'base64', // 指定编码为base64\r\n\t\t\t\tsuccess: () => {\r\n\t\t\t\t\tconsole.log('临时文件创建成功:', {\r\n\t\t\t\t\t\tfilePath: filePath,\r\n\t\t\t\t\t\tfileName: fileName\r\n\t\t\t\t\t});\r\n\t\t\t\t\tresolve(filePath);\r\n\t\t\t\t},\r\n\t\t\t\tfail: (error) => {\r\n\t\t\t\t\tconsole.error('创建临时文件失败:', error);\r\n\t\t\t\t\treject(new Error(`创建临时文件失败: ${error.errMsg || error.message}`));\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('base64转文件处理失败:', error);\r\n\t\t\treject(new Error(`base64转文件失败: ${error.message}`));\r\n\t\t}\r\n\t});\r\n};\r\n\r\n/**\r\n * 打开电子签名界面\r\n * 检查签名状态，已签名则提示，未签名则打开签名界面\r\n */\r\nconst openCanvas = () => {\r\n\ttry {\r\n\t\tif (isSigned.value) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '协议已签署，无需重复操作',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tif (isLoading.value) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '正在处理中，请稍候',\r\n\t\t\t\ticon: 'loading'\r\n\t\t\t});\r\n\t\t\treturn;\r\n\t\t}\r\n\t\t\r\n\t\tisCanvas.value = true;\r\n\t} catch (error) {\r\n\t\tconsole.error('打开签名界面失败:', error);\r\n\t\tuni.showToast({\r\n\t\t\ttitle: '打开签名界面失败',\r\n\t\t\ticon: 'error'\r\n\t\t});\r\n\t}\r\n};\r\n\r\n// 页面加载时获取参数 - uni-app标准方式\r\nonLoad((options) => {\r\n\tconsole.log('协议签署页面参数:', options);\r\n\r\n\t// 获取调解案件ID参数\r\n\tif (options.case_number) {\r\n\t\ttry {\r\n\t\t\tcaseNumber.value = decodeURIComponent(options.case_number);\r\n\t\t\tconsole.log('接收到调解ID:', caseNumber.value);\r\n\t\t\t// 更新工单数据\r\n\t\t\tworkOrderData.value.id = caseNumber.value;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('case_number参数解码失败:', error);\r\n\t\t\tcaseNumber.value = options.case_number;\r\n\t\t\tworkOrderData.value.id = options.case_number;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取创建日期参数\r\n\tif (options.initiate_date) {\r\n\t\ttry {\r\n\t\t\tinitiateDate.value = decodeURIComponent(options.initiate_date);\r\n\t\t\tconsole.log('接收到日期:', initiateDate.value);\r\n\t\t\t// 更新工单数据\r\n\t\t\tworkOrderData.value.createDate = initiateDate.value;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('initiate_date参数解码失败:', error);\r\n\t\t\tinitiateDate.value = options.initiate_date;\r\n\t\t\tworkOrderData.value.createDate = options.initiate_date;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取关闭日期参数\r\n\tif (options.close_date) {\r\n\t\ttry {\r\n\t\t\tcloseDate.value = decodeURIComponent(options.close_date);\r\n\t\t\tconsole.log('关闭日期:', closeDate.value);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('close_date参数解码失败:', error);\r\n\t\t\tcloseDate.value = options.close_date;\r\n\t\t}\r\n\t}\r\n\r\n\t// 获取调解状态参数\r\n\tif (options.case_status_cn) {\r\n\t\ttry {\r\n\t\t\tcaseStatus.value = decodeURIComponent(options.case_status_cn);\r\n\t\t\tconsole.log('接收到调解状态:', caseStatus.value);\r\n\t\t\t// 更新工单数据\r\n\t\t\tworkOrderData.value.status = caseStatus.value;\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('case_status_cn参数解码失败:', error);\r\n\t\t\tcaseStatus.value = options.case_status_cn;\r\n\t\t\tworkOrderData.value.status = options.case_status_cn;\r\n\t\t}\r\n\t}\r\n});\r\n\r\n/**\r\n * 页面加载时执行\r\n * 恢复之前保存的签名状态\r\n */\r\nonMounted(() => {\r\n\tloadSignatureState();\r\n});\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n:root{\r\n\t// 主题色系\r\n\t--primary-color:#3b7eeb;\r\n\t--primary-dark:#2c62c9; \r\n\t--primary-light: #e6f0ff;\r\n\t--success-color: #52c41a;\r\n\t--success-dark: #45a049;\r\n\r\n\t// 文字颜色\r\n\t--text-primary: #333;\r\n\t--text-secondary: var(--text-secondary);\r\n\t--text-disabled: #999;\r\n\t\r\n\t// 背景色\r\n\t--bg-primary: #fff;\r\n\t--bg-secondary: #f5f5f5;\r\n\t--bg-disabled: #87B0F2;\r\n\t\r\n\t// 边框和阴影\r\n\t--border-radius: 12rpx;\r\n\t--border-radius-large: 24rpx;\r\n\t--box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t--transition-duration: 0.3s;\r\n}\r\n.solution-confirm-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: var(--bg-secondary);\r\n\tpadding: 30rpx;\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n/* 工单卡片样式 */\r\n.work-order-card {\r\n\tbackground-color: var(--bg-primary);\r\n\tborder-radius: var(--border-radius);\r\n\tpadding: 30rpx;\r\n\tbox-shadow: var(--box-shadow);\r\n}\r\n\r\n.work-order-header {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n\tfont-size: 30rpx;\r\n\tcolor: var(--text-primary);\r\n\tfont-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n\tpadding: 6rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n\tfont-size: 26rpx;\r\n\tpadding: 8rpx 20rpx;\r\n\tborder-radius: 30rpx;\r\n\tbackground-color: #f0f0f0;\r\n\tcolor: var(--bg-primary);\r\n}\r\n\r\n.status-processing {\r\n\tbackground-color: #1890ff;\r\n\tcolor: var(--bg-primary);\r\n}\r\n\r\n.work-order-date {\r\n\tfont-size: 28rpx;\r\n\tcolor: var(--text-secondary);\r\n}\r\n\r\n/* 进度条样式 */\r\n/* .progress-bar {\r\n\tbackground-color: var(--bg-primary);\r\n\tborder-radius: var(--border-radius);\r\n\tpadding: 30rpx;\r\n\tbox-shadow: var(--box-shadow);\r\n} */\r\n\r\n.progress-steps {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tposition: relative;\r\n}\r\n\r\n.progress-step {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\talign-items: center;\r\n\tflex: 1;\r\n\tposition: relative;\r\n}\r\n\r\n.step-circle {\r\n\twidth: 60rpx;\r\n\theight: 60rpx;\r\n\tborder-radius: 50%;\r\n\tbackground-color: #e0e0e0;\r\n\tcolor: var(--bg-primary);\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n\tz-index: 2;\r\n\ttransition: all var(--transition-duration) ease;\r\n}\r\n\r\n.step-line {\r\n\tposition: absolute;\r\n\ttop: 30rpx;\r\n\tleft: 50%;\r\n\tright: -50%;\r\n\theight: 4rpx;\r\n\tbackground-color: #e0e0e0;\r\n\tz-index: 1;\r\n\ttransition: all var(--transition-duration) ease;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n\tdisplay: none;\r\n}\r\n\r\n.step-label {\r\n\tfont-size: 24rpx;\r\n\tcolor: var(--text-disabled);\r\n\ttext-align: center;\r\n\ttransition: all var(--transition-duration) ease;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n\tbackground-color: var(--primary-color);\r\n}\r\n\r\n.progress-step.active .step-label {\r\n\tcolor: var(--primary-color);\r\n\tfont-weight: bold;\r\n}\r\n\r\n.progress-step.completed .step-circle {\r\n\tbackground-color: var(--primary-color);\r\n}\r\n\r\n.step-line.completed {\r\n\tbackground-color: var(--primary-color);\r\n}\r\n\r\n.progress-step.completed .step-label {\r\n\tcolor: var(--primary-color);\r\n}\r\n\r\n/* 方案容器 */\r\n.solutions-container {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n\tgap: 30rpx;\r\n}\r\n\r\n/* 方案卡片样式 */\r\n.solution-card {\r\n\tbackground-color: linear-gradient(135deg, rgb(255, 255, 255) 0%, rgb(248, 250, 252) 100%);;\r\n\tposition: relative;\r\n    overflow: hidden;\r\n\tborder: 2rpx solid var(--primary-color);\r\n\tborder-radius: var(--border-radius-large);\r\n\ttransition: all var(--transition-duration) ease;\r\n}\r\n\r\n.solution-bg{\r\n\tposition: absolute;\r\n    top: -40rpx;\r\n    right: -40rpx;\r\n    width: 200rpx;\r\n    height: 200rpx;\r\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\r\n    border-radius: 50%;\r\n    opacity: 0.1;\r\n}\r\n.agreement-document{\r\n\ttext-align: center;\r\n    padding: 60rpx 40rpx;\r\n}\r\n.agreement-document-icon{\r\n\twidth: 160rpx;\r\n    height: 160rpx;\r\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\tmargin: 0 auto 40rpx;\r\n    box-shadow: 0 16rpx 40rpx rgba(59, 126, 235, 0.3);\r\n    position: relative;\r\n    z-index: 2;\r\n\t.fas{\r\n\t\tcolor: var(--bg-primary);\r\n    \tfont-size: 64rpx;\r\n\t}\r\n}\r\n.agreement-document-title{\r\n\tmargin: 0 0 16rpx 0;\r\n    color: var(--text-primary);\r\n    font-size: 40rpx;\r\n    font-weight: bold;\r\n}\r\n.agreement-document-tip{\r\n    font-size: 28rpx;\r\n    color: var(--text-secondary);\r\n    line-height: 1.5;\r\n}\r\n.agreement-document-button{\r\n\tdisplay: flex;\r\n    align-items: center;\r\n\tjustify-content: center;\r\n    font-size: 32rpx;\r\n    text-align: center;\r\n    font-weight: 500;\r\n    box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;\r\n\t// min-width: 400rpx;  // 固定最小宽度\r\n    height: 96rpx;\r\n\twhite-space: nowrap; // 防止文字换行\r\n    line-height: 1.5;\r\n    padding: 24rpx 40rpx;\r\n    border-radius: 16rpx;\r\n    transition: all var(--transition-duration) ease;\r\n    border-width: initial;\r\n    border-style: none;\r\n    border-color: initial;\r\n    border-image: initial;\r\n    margin: 50rpx 110rpx 30rpx 110rpx;\r\n    transform: scale(1);\r\n    will-change: transform; // 启用硬件加速\r\n\tbackground-color: var(--primary-color);\r\n    color: var(--bg-primary);\r\n\t.fas{\r\n\t\tmargin-right: 20rpx;\r\n    \tfont-size: 36rpx;\r\n\t\t// width: 36rpx; // 固定图标宽度\r\n        // display: inline-block;\r\n\t}\r\n}\r\n.solution-border{\r\n\twidth: 120rpx;\r\n    height: 6rpx;\r\n    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);\r\n    border-radius: 4rpx;\r\n    margin: 40rpx auto;\r\n}\r\n.confirm-signing{\r\n\tmargin-top: 50rpx;\r\n\t.sign{\r\n\t\tmargin-bottom: 40rpx;\r\n\t\t.fas{\r\n\t\t\tfont-size: 80rpx;\r\n\t\t\tcolor: var(--primary-color);\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t}\r\n\t}\r\n\t.sign-title{\r\n\t\tmargin: 0 0 16rpx 0;\r\n\t\tcolor: var(--text-primary);\r\n\t\tfont-size: 36rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\t.sign-tip{\r\n\t\tmargin: 0;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: var(--text-secondary);\r\n\t}\r\n\t.agreement-check{\r\n\t\tbackground-color: var(--bg-primary);\r\n\t\tborder: 4rpx solid var(--primary-light);\r\n\t\tborder-radius: var(--border-radius-large);\r\n\t\tpadding: 36rpx;\r\n\t\tmargin: 40rpx 0;\r\n\t\ttransition: all var(--transition-duration) ease;\r\n\t\t&:hover {\r\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(59, 126, 235, 0.1);\r\n\t\t}\r\n\t}\r\n\t/* 签名完成状态样式 */\r\n\t.agreement-check-signed{\r\n\t\tborder-color: var(--success-color) !important;\r\n\t\tbackground-color: rgb(246, 255, 237) !important;\r\n\t\t&:hover {\r\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(82, 196, 26, 0.1);\r\n\t\t}\r\n\t}\r\n\t.agreement-content{\r\n\t\tdisplay: flex;\r\n\t\ttext-align: left;\r\n    \tflex-direction: column;\r\n\t}\r\n\t.electronic-signature{\r\n\t\tcolor: var(--text-secondary);\r\n\t\tfont-size: 26rpx;\r\n\t\tline-height: 1.4;\r\n\t\tmargin-left: 57rpx;\r\n\t\tdisplay: block;\r\n\t\ttransition: all var(--transition-duration) ease;\r\n\t}\r\n\t.signature-completed{\r\n\t\tcolor: var(--success-color) !important;\r\n\t}\r\n}\r\n:deep(.checklist-text){\r\n    font-size: 30rpx !important;\r\n    color: var(--text-primary) !important;\r\n    font-weight: bold !important;\r\n\tmargin-left: 10rpx !important;\r\n    line-height: 1.5 !important;\r\n\ttransition: all var(--transition-duration) ease !important;\r\n}\r\n// 签名完成后复选框文字样式\r\n.agreement-check-signed :deep(.checklist-text){\r\n    color: var(--success-color) !important;\r\n}\r\n:deep(.checkbox__inner){\r\n\twidth: 40rpx !important;\r\n    height: 40rpx !important;\r\n    border: 4rpx solid var(--primary-color) !important;\r\n    border-radius: 8rpx !important;\r\n    background-color: var(--bg-primary);\r\n    z-index: 1 !important;\r\n    display: flex !important;\r\n    justify-content: center !important;\r\n    align-items: center !important;\r\n    margin-right: 15rpx !important;\r\n    transition: all var(--transition-duration) ease !important;\r\n}\r\n:deep(.checkbox__inner-icon){\r\n\theight: 18rpx !important;\r\n    width: 10rpx !important;\r\n\tborder-right-width: 4rpx !important;\r\n\tborder-bottom-width: 4rpx !important;\r\n\topacity: 1 !important;\r\n}\r\n:deep(.checklist-group .checklist-box.is--default.is-disable .checkbox__inner) {\r\n    background-color: #3b7eeb !important;\r\n    border-color: #3b7eeb!important;\r\n}\r\n// 签名完成后复选框边框样式\r\n.agreement-check-signed :deep(.checkbox__inner){\r\n    border-color: var(--success-color) !important;\r\n}\r\n\r\n/* 底部按钮 */\r\n.action-buttons {\r\n\tpadding: 20rpx 0;\r\n}\r\n\r\n.confirm-button {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tline-height: 90rpx;\r\n\ttext-align: center;\r\n\tborder-radius: 16rpx;\r\n\tfont-size: 32rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n\ttransition: all var(--transition-duration) ease;\r\n\t.fas{\r\n\t\tmargin-right: 20rpx;\r\n    \tfont-size: 36rpx;\r\n\t\tcolor: var(--bg-primary);\r\n\t}\r\n\t&:active {\r\n\t\ttransform: scale(0.98);\r\n\t}\r\n}\r\n.disabled-btn{\r\n\tbackground-color: var(--bg-disabled) !important;\r\n\tcolor: var(--bg-primary) !important;\r\n}\r\n\r\n.sign-btn{\r\n\tbackground-color: var(--primary-color) !important;\r\n\tcolor: var(--bg-primary) !important;\r\n\t&:hover {\r\n\t\tbackground-color: var(--primary-dark) !important;\r\n\t}\r\n}\r\n\r\n.success-btn{\r\n\tbackground: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%) !important;\r\n\tcolor: var(--bg-primary) !important;\r\n\t&:hover {\r\n\t\ttransform: none;\r\n\t}\r\n\t\r\n\t&:active {\r\n\t\ttransform: none;\r\n\t}\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/agreement_signing/agreement_signing.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "computed", "uni", "api", "wx", "onLoad", "onMounted"], "mappings": ";;;;;;;;;;;AAiHA,MAAM,kBAAkB,MAAW;;;;AAGnC,UAAM,aAAaA,cAAAA,IAAI,EAAE;AACzB,UAAM,eAAeA,cAAAA,IAAI,EAAE;AAC3B,UAAM,YAAYA,cAAAA,IAAI,EAAE;AACxB,UAAM,aAAaA,cAAAA,IAAI,EAAE;AAGzB,UAAM,gBAAgBA,cAAAA,IAAI,CAAA,CAAE;AAM5B,UAAM,WAAWA,cAAAA,IAAI,KAAK;AAM1B,UAAM,mBAAmBA,cAAAA,IAAI,KAAK;AAMlC,UAAM,UAAUC,cAAQ,SAAC,MAAM;AAC9B,aAAO;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACA,CAAC;AAKD,UAAM,kBAAkBD,cAAAA,IAAI,CAAA,CAAE;AAK9B,UAAM,gBAAgBA,cAAAA,IAAI,EAAE;AAM5B,UAAM,YAAYA,cAAAA,IAAI,KAAK;AAM3B,UAAM,SAASA,cAAAA,IAAI;AAAA,MAClB;AAAA,QACC,MAAM;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,MACV;AAAA,IACF,CAAC;AAOD,UAAM,eAAeC,cAAQ,SAAC,MAAM;AACnC,UAAI,SAAS,OAAO;AACnB,eAAO;AAAA,UACN;AAAA,YACC,MAAM;AAAA,YACN,OAAO;AAAA,YACP,UAAU;AAAA,UACV;AAAA,QACJ;AAAA,MACE;AACD,aAAO,OAAO;AAAA,IACf,CAAC;AAOD,UAAM,eAAeA,cAAQ,SAAC,MAAM;AACnC,UAAI,SAAS,OAAO;AACnB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,QACV;AAAA,MACE;AACD,aAAO;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,OAAO,CAAC,gBAAgB,MAAM,SAAS,GAAG,IAAI,iBAAiB;AAAA,MACjE;AAAA,IACA,CAAC;AAID,UAAM,qBAAqB,MAAM;AAChC,UAAI;AACH,cAAM,YAAY;AAAA,UACjB,UAAU,SAAS;AAAA,UACnB,iBAAiB,gBAAgB;AAAA,UACjC,eAAe,cAAc;AAAA,UAC7B,WAAW,KAAK,IAAK;AAAA,QACxB;AACEC,sBAAG,MAAC,eAAe,6BAA6B,KAAK,UAAU,SAAS,CAAC;AAAA,MACzE,SAAQ,OAAO;AACfA,mGAAc,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAKA,UAAM,qBAAqB,MAAM;AAChC,UAAI;AACH,cAAM,aAAaA,cAAAA,MAAI,eAAe,2BAA2B;AACjE,YAAI,YAAY;AACf,gBAAM,YAAY,KAAK,MAAM,UAAU;AAEvC,cAAI,KAAK,IAAG,IAAK,UAAU,YAAY,KAAK,KAAK,KAAK,KAAM;AAC3D,qBAAS,QAAQ,UAAU,YAAY;AACvC,4BAAgB,QAAQ,UAAU,mBAAmB,CAAA;AACrD,0BAAc,QAAQ,UAAU,iBAAiB;AAAA,UACjD;AAAA,QACD;AAAA,MACD,SAAQ,OAAO;AACfA,mGAAc,aAAa,KAAK;AAAA,MAChC;AAAA,IACF;AAKA,UAAM,gBAAgB,YAAY;AACjC,UAAI;AACH,yBAAiB,QAAQ;AAuBzBA,sBAAAA,MAAI,WAAW;AAAA,UACd,KAAK;AAAA,QACR,CAAG;AAAA,MACD,SAAQ,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,wDAAA,SAAS,KAAK;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,UAAW;AACT,yBAAiB,QAAQ;AAAA,MACzB;AAAA,IACF;AAKA,UAAM,gBAAgB,YAAY;AACjC,UAAI;AAEH,YAAI,SAAS,OAAO;AACnB,oBAAU,QAAQ;AAGlB,gBAAM,uBAAuB,aAAa,MAAM,SAAS;AAEzD,cAAI,sBAAsB;AAEzB,kBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,IAAI,CAAC;AAEtDA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACf,CAAK;AAGD,uBAAW,MAAM;AAChBA,4BAAAA,MAAI,WAAW;AAAA,gBACd,KAAK;AAAA,cACX,CAAM;AAAA,YACD,GAAE,IAAI;AAAA,UACX,OAAU;AAENA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACX,CAAK;AAAA,UACD;AAED;AAAA,QACA;AAGD,YAAI,gBAAgB,MAAM,SAAS,GAAG,GAAG;AACxCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,SAAS,CAAC,QAAQ;AACjB,kBAAI,IAAI,SAAS;AAChB;cACA;AAAA,YACD;AAAA,UACL,CAAI;AAAA,QACJ,OAAS;AAENA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AAAA,QACD;AAAA,MACD,SAAQ,OAAO;AACfA,mGAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACH,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAMA,UAAM,WAAWF,cAAAA,IAAI,KAAK;AAO1B,UAAM,WAAW,OAAO,oBAAoB;AAC3C,UAAI;AACH,kBAAU,QAAQ;AAClBE,sBAAA,MAAA,MAAA,OAAA,wDAAY,SAAS,eAAe;AAGpC,sBAAc,QAAQ;AAGtB,cAAM,wBAAwB,eAAe;AAG7C,iBAAS,QAAQ;AACjB,wBAAgB,QAAQ,CAAC,GAAG;AAG5B;AAEAA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD,SAAQ,OAAO;AACfA,mGAAc,WAAW,KAAK;AAC9BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAED,iBAAS,QAAQ;AACjB,wBAAgB,QAAQ;AACxB,sBAAc,QAAQ;AAAA,MACxB,UAAW;AACT,kBAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAMA,UAAM,0BAA0B,OAAO,oBAAoB;AAC1D,UAAI,CAAC,WAAW,OAAO;AACtB,cAAM,IAAI,MAAM,SAAS;AAAA,MACzB;AAED,UAAI;AAEH,cAAM,WAAW,MAAM,iBAAiB,eAAe;AAGvD,cAAM,QAAQA,cAAG,MAAC,eAAe,cAAc,KAAKA,oBAAI,eAAe,OAAO;AAC9E,cAAM,YAAYA,cAAG,MAAC,eAAe,YAAY,KAAK;AAGtD,cAAM,SAASC,UAAAA,IAAI,oBAAoB,sBAAsB,WAAW,KAAK;AAE7ED,sBAAAA,MAAY,MAAA,OAAA,wDAAA,aAAa;AAAA,UACxB,KAAK;AAAA,UACL;AAAA,UACA,YAAY,WAAW;AAAA,QAC1B,CAAG;AAGD,eAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvCA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK;AAAA,YACL;AAAA,YACA,MAAM;AAAA;AAAA,YACN,QAAQ;AAAA;AAAA,YACR,UAAU;AAAA,cACT,aAAa,WAAW;AAAA,YACxB;AAAA,YACD,QAAQ;AAAA,cACP,iBAAiB,QAAQ,GAAG,SAAS,IAAI,KAAK,KAAK;AAAA,YACnD;AAAA,YACD,SAAS,CAAC,QAAQ;AACjBA,uGAAY,aAAa,GAAG;AAG5B,kBAAI,IAAI,eAAe,OAAO,IAAI,eAAe,KAAK;AACrD,oBAAI;AACH,wBAAM,eAAe,KAAK,MAAM,IAAI,IAAI;AACxC,sBAAI,aAAa,YAAY,OAAO;AACnCA,kCAAY,MAAA,MAAA,OAAA,wDAAA,aAAa,YAAY;AACrC,4BAAQ,YAAY;AAAA,kBAC5B,OAAc;AACN,2BAAO,IAAI,MAAM,aAAa,WAAW,UAAU,CAAC;AAAA,kBACpD;AAAA,gBACD,SAAQ,YAAY;AAEpBA,gCAAAA,MAAY,MAAA,OAAA,wDAAA,sBAAsB;AAClC,0BAAQ,EAAE,SAAS,MAAM,MAAM,IAAI,KAAI,CAAE;AAAA,gBACzC;AAAA,cACP,OAAY;AACN,uBAAO,IAAI,MAAM,eAAe,IAAI,UAAU,EAAE,CAAC;AAAA,cACjD;AAAA,YACD;AAAA,YACD,MAAM,CAAC,UAAU;AAChBA,4BAAA,MAAA,MAAA,SAAA,wDAAc,aAAa,KAAK;AAChC,qBAAO,IAAI,MAAM,MAAM,UAAU,QAAQ,CAAC;AAAA,YAC1C;AAAA,UACL,CAAI;AAAA,QACJ,CAAG;AAAA,MACD,SAAQ,OAAO;AACfA,mGAAc,aAAa,KAAK;AAChC,cAAM;AAAA,MACN;AAAA,IACF;AAOA,UAAM,mBAAmB,CAAC,eAAe;AACxC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,YAAI;AAEH,gBAAM,SAAS,WAAW,QAAQ,4BAA4B,EAAE;AAGhE,gBAAM,WAAW,wBAAwB,WAAW,KAAK,IAAI,KAAK,KAAK;AACvE,gBAAM,WAAW,GAAGE,mBAAG,IAAI,cAAc,IAAI,QAAQ;AAErDF,wBAAAA,MAAA,MAAA,OAAA,wDAAY,aAAa;AAAA,YACxB;AAAA,YACA;AAAA,YACA,cAAc,OAAO;AAAA,UACzB,CAAI;AAGD,gBAAM,KAAKE,mBAAG;AACd,aAAG,UAAU;AAAA,YACZ;AAAA,YACA,MAAM;AAAA,YACN,UAAU;AAAA;AAAA,YACV,SAAS,MAAM;AACdF,4BAAAA,MAAY,MAAA,OAAA,wDAAA,aAAa;AAAA,gBACxB;AAAA,gBACA;AAAA,cACN,CAAM;AACD,sBAAQ,QAAQ;AAAA,YAChB;AAAA,YACD,MAAM,CAAC,UAAU;AAChBA,4BAAA,MAAA,MAAA,SAAA,wDAAc,aAAa,KAAK;AAChC,qBAAO,IAAI,MAAM,aAAa,MAAM,UAAU,MAAM,OAAO,EAAE,CAAC;AAAA,YAC9D;AAAA,UACL,CAAI;AAAA,QACD,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,wDAAA,kBAAkB,KAAK;AACrC,iBAAO,IAAI,MAAM,gBAAgB,MAAM,OAAO,EAAE,CAAC;AAAA,QACjD;AAAA,MACH,CAAE;AAAA,IACF;AAMA,UAAM,aAAa,MAAM;AACxB,UAAI;AACH,YAAI,SAAS,OAAO;AACnBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AAED,YAAI,UAAU,OAAO;AACpBA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACV,CAAI;AACD;AAAA,QACA;AAED,iBAAS,QAAQ;AAAA,MACjB,SAAQ,OAAO;AACfA,mGAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACT,CAAG;AAAA,MACD;AAAA,IACF;AAGAG,kBAAM,OAAC,CAAC,YAAY;AACnBH,oBAAA,MAAA,MAAA,OAAA,wDAAY,aAAa,OAAO;AAGhC,UAAI,QAAQ,aAAa;AACxB,YAAI;AACH,qBAAW,QAAQ,mBAAmB,QAAQ,WAAW;AACzDA,wBAAY,MAAA,MAAA,OAAA,wDAAA,YAAY,WAAW,KAAK;AAExC,wBAAc,MAAM,KAAK,WAAW;AAAA,QACpC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,wDAAA,sBAAsB,KAAK;AACzC,qBAAW,QAAQ,QAAQ;AAC3B,wBAAc,MAAM,KAAK,QAAQ;AAAA,QACjC;AAAA,MACD;AAGD,UAAI,QAAQ,eAAe;AAC1B,YAAI;AACH,uBAAa,QAAQ,mBAAmB,QAAQ,aAAa;AAC7DA,wBAAY,MAAA,MAAA,OAAA,wDAAA,UAAU,aAAa,KAAK;AAExC,wBAAc,MAAM,aAAa,aAAa;AAAA,QAC9C,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,wDAAA,wBAAwB,KAAK;AAC3C,uBAAa,QAAQ,QAAQ;AAC7B,wBAAc,MAAM,aAAa,QAAQ;AAAA,QACzC;AAAA,MACD;AAGD,UAAI,QAAQ,YAAY;AACvB,YAAI;AACH,oBAAU,QAAQ,mBAAmB,QAAQ,UAAU;AACvDA,wBAAY,MAAA,MAAA,OAAA,wDAAA,SAAS,UAAU,KAAK;AAAA,QACpC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,wDAAA,qBAAqB,KAAK;AACxC,oBAAU,QAAQ,QAAQ;AAAA,QAC1B;AAAA,MACD;AAGD,UAAI,QAAQ,gBAAgB;AAC3B,YAAI;AACH,qBAAW,QAAQ,mBAAmB,QAAQ,cAAc;AAC5DA,wBAAY,MAAA,MAAA,OAAA,wDAAA,YAAY,WAAW,KAAK;AAExC,wBAAc,MAAM,SAAS,WAAW;AAAA,QACxC,SAAQ,OAAO;AACfA,wBAAc,MAAA,MAAA,SAAA,wDAAA,yBAAyB,KAAK;AAC5C,qBAAW,QAAQ,QAAQ;AAC3B,wBAAc,MAAM,SAAS,QAAQ;AAAA,QACrC;AAAA,MACD;AAAA,IACF,CAAC;AAMDI,kBAAAA,UAAU,MAAM;AACf;IACD,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChnBD,GAAG,WAAW,eAAe;"}