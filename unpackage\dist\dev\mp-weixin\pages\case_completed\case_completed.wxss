/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-1cf6b09e:root {
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --primary-color: #3b7eeb;
  --border-color: #e0e0e0;
  --transition-normal: all 0.3s ease;
}
.work-order-detail-container.data-v-1cf6b09e {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.work-order-card.data-v-1cf6b09e {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.work-order-header.data-v-1cf6b09e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.work-order-title.data-v-1cf6b09e {
  font-size: 29rpx;
  color: #333;
  font-weight: bold;
}
.work-order-status.data-v-1cf6b09e {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}
.status-label.data-v-1cf6b09e {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #52c41a;
  color: #fff;
}
.status-pending.data-v-1cf6b09e {
  background-color: #52c41a;
}
.work-order-date.data-v-1cf6b09e {
  font-size: 28rpx;
  color: #666;
}
.progress-steps.data-v-1cf6b09e {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.progress-step.data-v-1cf6b09e {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}
.step-circle.data-v-1cf6b09e {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}
.step-line.data-v-1cf6b09e {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}
.progress-step:last-child .step-line.data-v-1cf6b09e {
  display: none;
}
.step-line.active.data-v-1cf6b09e {
  background-color: #2979ff;
}
.step-label.data-v-1cf6b09e {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.progress-step.active .step-circle.data-v-1cf6b09e {
  background-color: #2979ff;
}
.progress-step.active .step-label.data-v-1cf6b09e {
  color: #2979ff;
  font-weight: bold;
}
.info-section.data-v-1cf6b09e {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title.data-v-1cf6b09e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
}
.info-item.data-v-1cf6b09e {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx 0;
  color: #333;
  font-size: 32rpx;
}
.info-item.data-v-1cf6b09e:last-child {
  border-bottom: none;
}
.reduction_amount.data-v-1cf6b09e {
  color: #52c41a;
}
.info-label.data-v-1cf6b09e {
  font-weight: bold;
}
.notarization-pending.data-v-1cf6b09e {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d6 100%);
  border: 2rpx solid #ffd591;
  cursor: pointer;
}
.notarization-pending.data-v-1cf6b09e:hover {
  background: linear-gradient(135deg, #fff2d6 0%, #ffec99 100%);
  border-color: #ffa940;
  box-shadow: 0 8rpx 24rpx rgba(255, 140, 0, 0.15);
}
.notarization-status-card.data-v-1cf6b09e {
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  transition: all 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  position: relative;
  overflow: hidden;
}
.notarization-status-card-completed.data-v-1cf6b09e {
  display: block;
  opacity: 1;
  transform: translateY(0rpx);
}
.notarization-completed.data-v-1cf6b09e {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border: 2rpx solid #b7eb8f;
}
.notarization-details.data-v-1cf6b09e {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 2rpx solid #d9f7be;
}
.notarization-detail-row.data-v-1cf6b09e {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}
.notarization-detail-row .notarization-detail-label.data-v-1cf6b09e {
  color: var(--text-secondary);
  font-weight: bold;
}
.notarization-detail-row .notarization-detail-value.data-v-1cf6b09e {
  color: var(--text-color);
  font-weight: bold;
}
.notarization-status-header.data-v-1cf6b09e {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.notarization-status-header .fa-certificate.data-v-1cf6b09e {
  color: #52c41a;
  font-size: 40rpx;
}
.notarization-status-header .fa-check-circle.data-v-1cf6b09e {
  color: #52c41a;
  font-size: 32rpx;
}
.notarization-pending .notarization-status-icon.data-v-1cf6b09e {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}
.notarization-status-icon.data-v-1cf6b09e {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.notarization-status-icon .fa-certificate.data-v-1cf6b09e {
  color: #ff8c00;
  font-size: 40rpx;
}
.notarization-completed .notarization-status-icon.data-v-1cf6b09e {
  background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);
}
.notarization-status-info.data-v-1cf6b09e {
  flex: 1;
  min-width: 0;
}
.notarization-status-title.data-v-1cf6b09e {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: var(--text-color);
}
.notarization-status-desc.data-v-1cf6b09e {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}
.notarization-action-arrow.data-v-1cf6b09e {
  margin-left: 16rpx;
  transition: transform 0.3s ease;
}
.notarization-action-arrow .fa-chevron-right.data-v-1cf6b09e {
  color: #ff8c00;
  font-size: 32rpx;
}
.notarization-benefits.data-v-1cf6b09e {
  display: flex;
  gap: 40rpx;
  margin-top: 16rpx;
}
.notarization-benefit-item.data-v-1cf6b09e {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--text-secondary);
}
.notarization-benefit-item .fa-shield-alt.data-v-1cf6b09e, .notarization-benefit-item .fa-gavel.data-v-1cf6b09e {
  color: #ff8c00;
  margin-right: 16rpx;
}
.payment-memo-card.data-v-1cf6b09e {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border: 2rpx solid #ffcc02;
  border-radius: 24rpx;
  padding: 36rpx;
  margin-bottom: 40rpx;
  margin-top: 0;
}
.payment-memo-header.data-v-1cf6b09e {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.payment-memo-icon.data-v-1cf6b09e {
  width: 48rpx;
  height: 48rpx;
  color: #ff8f00;
  align-items: center;
  display: flex;
  text-align: center;
}
.payment-memo-title.data-v-1cf6b09e {
  font-size: 32rpx;
  font-weight: 600;
  color: #e65100;
}
.payment-memo-content.data-v-1cf6b09e {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: bold;
  word-break: break-all;
  line-height: 1.4;
  border: 2rpx solid rgba(255, 152, 0, 0.2);
}
.payment-memo-actions.data-v-1cf6b09e {
  margin-top: 24rpx;
  text-align: right;
}
.payment-memo-tip.data-v-1cf6b09e {
  display: flex;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #e65100;
  line-height: 1.4;
}
.payment-memo-tip .fa-info-circle.data-v-1cf6b09e {
  margin-right: 10rpx;
}
.btn-copy.btn-orange.data-v-1cf6b09e {
  background-color: #ff8f00;
  border-color: #ff8f00;
  color: white;
}
.btn-copy.btn-orange.data-v-1cf6b09e:hover {
  background-color: #f57700;
  border-color: #f57700;
}
.btn-copy.btn-orange.copied.data-v-1cf6b09e {
  background-color: var(--success-color);
  border-color: var(--success-color);
}
.btn-copy.btn-orange.copied .fa-check.data-v-1cf6b09e {
  margin-left: 20rpx;
}
.btn-copy.data-v-1cf6b09e {
  display: inline-flex;
  align-items: center;
  gap: 10rpx;
}
.btn-sm.data-v-1cf6b09e {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  border-radius: 12rpx;
  height: 80rpx;
}
.payment-channel-card.data-v-1cf6b09e {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  border: 2rpx solid var(--border-color);
  transition: var(--transition-normal);
}
.payment-channel-card .payment-channel-header.data-v-1cf6b09e {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}
.payment-channel-card .payment-channel-icon.data-v-1cf6b09e {
  width: 90rpx;
  height: 90rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
  flex-shrink: 0;
}
.payment-channel-card .payment-channel-icon .fa-university.data-v-1cf6b09e, .payment-channel-card .payment-channel-icon .fa-weixin.data-v-1cf6b09e, .payment-channel-card .payment-channel-icon .fa-alipay.data-v-1cf6b09e {
  color: white;
  font-size: 40rpx;
}
.payment-channel-card .payment-channel-info.data-v-1cf6b09e {
  flex: 1;
}
.payment-channel-card .payment-channel-qrcode-container.data-v-1cf6b09e {
  text-align: center;
  margin-top: 15px;
}
.payment-channel-card .payment-channel-qrcode-container .payment-channel-qrcode.data-v-1cf6b09e {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #999;
}
.payment-channel-card .payment-channel-name.data-v-1cf6b09e {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 8rpx;
}
.payment-channel-card .payment-channel-desc.data-v-1cf6b09e {
  font-size: 26rpx;
  color: var(--text-secondary);
}
.payment-channel-card .payment-channel-details.data-v-1cf6b09e {
  padding-top: 30rpx;
  border-top: 2rpx solid rgba(0, 0, 0, 0.06);
}
.payment-channel-card .payment-detail-row.data-v-1cf6b09e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.payment-channel-card .payment-detail-row .payment-detail-label.data-v-1cf6b09e {
  font-size: 28rpx;
  color: var(--text-secondary);
  flex-shrink: 0;
  margin-right: 30rpx;
}
.payment-channel-card .payment-detail-row .payment-detail-value.data-v-1cf6b09e {
  font-size: 28rpx;
  color: var(--text-color);
  font-weight: bold;
  flex: 1;
  text-align: right;
  word-break: break-all;
}
.action-buttons.data-v-1cf6b09e {
  margin-top: 40rpx;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}
.confirm-button.data-v-1cf6b09e {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  background-color: #2979ff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm-button .fas.data-v-1cf6b09e {
  margin-right: 10rpx;
}
.confirm-button.svg-btn.data-v-1cf6b09e {
  background-color: #52c41a;
}
.confirm-button.svg-btn.data-v-1cf6b09e:active {
  background-color: #45a049;
}