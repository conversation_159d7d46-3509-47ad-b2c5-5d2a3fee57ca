<template>
	<view class="whole canvas-autograph flexc" @touchmove.prevent.stop @wheel.prevent.stop v-show="modelValue">
		<canvas class="scroll-view" id="mycanvas" canvas-id="mycanvas" 
			@touchstart="touchstart" 
			@touchmove="touchmove" 
			@touchend="touchend"/>
		<!-- 操作按钮区域 - 水平排版 -->
		<view class="action-buttons">
			<view class="btn btn-clear" @click="clear">
				<text>清空</text>
			</view>
			<view class="btn btn-confirm" @click="confirm">
				<text>确认</text>
			</view>
			<view class="btn btn-cancel" @click="cancel">
				<text>取消</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	/*
		使用如下
		<canvas-autograph v-model="isCanvas" @complete="complete"/>
		
		// 打开、关闭
		let isCanvas = ref(false)
		// 确认事件
		const complete = e=>{
			console.log(e)
		}
	
	*/
	import { ref, reactive, watch, getCurrentInstance } from 'vue'
	
	const emits = defineEmits(['update:modelValue','complete'])
	
	const props = defineProps({
		modelValue:Boolean,
	})
	const _this = getCurrentInstance()
	watch(()=>props.modelValue,e=>{
		// 这里可进行 tabbar 的 显示、隐藏  不要也可以
		// 自己写
	},{
		immediate:true, // 是否默认执行一次  默认为false
	})
	
	let points = reactive([]) //路径点集合 
	
	let canvaCtx = reactive(uni.createCanvasContext('mycanvas', _this))   //创建绘图对象
	//设置画笔样式
	canvaCtx.lineWidth = 4;
	canvaCtx.lineCap = 'round'
	canvaCtx.lineJoin = 'round'
	
	
	//触摸开始，获取到起点
	const touchstart = e=>{
		let startX = e.changedTouches[0].x
		let startY = e.changedTouches[0].y
		let startPoint = {X:startX,Y:startY}
		points.push(startPoint);
		//每次触摸开始，开启新的路径
		canvaCtx.beginPath();
	}
	//触摸移动，获取到路径点
	const touchmove = e=>{
		let moveX = e.changedTouches[0].x
		let moveY = e.changedTouches[0].y
		let movePoint = {X:moveX,Y:moveY}
		points.push(movePoint)       //存点
		let len = points.length
		if(len>=2){
			draw()
		}
	}
	//绘制路径
	const draw = ()=> {
		let point1 = points[0]
		let point2 = points[1]
		points.shift()
		canvaCtx.moveTo(point1.X, point1.Y)
		canvaCtx.lineTo(point2.X, point2.Y)
		canvaCtx.stroke()
		canvaCtx.draw(true)
	}
	// 触摸结束，将未绘制的点清空防止对后续路径产生干扰
	const touchend = e=>{                   
		points = [];
	}
	// 清空画布
	const clear = ()=>{
		return uni.getSystemInfo()
		.then(res=>{
			canvaCtx.clearRect(0, 0, res.windowWidth, res.windowHeight);
			canvaCtx.draw(true);
			return res
		})
		.catch(err=>{
			// console.log(err);
		})
	}
	// 确认
	const confirm = ()=>{
		uni.canvasToTempFilePath({ canvasId: 'mycanvas', }, _this, _this.parent)
		.then(res=>{
			console.log(res.tempFilePath);
			emits('complete',res.tempFilePath)
			cancel()
		})
	}
	// 取消
	const cancel = ()=>{
		clear().then(res=>emits('update:modelValue',false))
	}

</script>

<style scoped lang="scss">
	.canvas-autograph {
		position: fixed;
		z-index: 998;
		width: 100vw;
		height: 100vh;
		top: 0;
		left: 0;
		.scroll-view {
			width: 100%;
			height: 100%;
			background-color: #FFFFFF;
		}
		
		.action-buttons {
			position: absolute;
			bottom: 40rpx;
			left: 0;
			width: 100%;
			display: flex;
			justify-content: center;
			padding: 0 30rpx;
			
			.btn {
				min-width: 160rpx;
				height: 80rpx;
				margin: 0 20rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: #FFFFFF;
				font-size: 28rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
			}
			
			.btn-clear {
				background-color: #F4F4F5;
				color: #909399;
			}
			
			.btn-confirm {
				background-color: #409EFF;
			}
			
			.btn-cancel {
				background-color: #F67D7D;
			}
		}
	
	}
</style>
