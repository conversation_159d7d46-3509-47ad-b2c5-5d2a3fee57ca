"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "real_case",
  setup(__props) {
    const searchKeyword = common_vendor.ref("");
    const activeFilter = common_vendor.ref("all");
    const loading = common_vendor.ref(false);
    const caseList = common_vendor.ref([]);
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/real_case/real_case.vue:119", "案例展示页面已加载，开始获取后台数据");
    });
    const filteredCases = common_vendor.computed(() => {
      let filtered = caseList.value;
      if (activeFilter.value !== "all") {
        filtered = filtered.filter((item) => item.category === activeFilter.value);
      }
      if (searchKeyword.value.trim()) {
        const keyword = searchKeyword.value.trim().toLowerCase();
        filtered = filtered.filter(
          (item) => item.title.toLowerCase().includes(keyword) || item.type.toLowerCase().includes(keyword) || item.summary.toLowerCase().includes(keyword)
        );
      }
      return filtered;
    });
    const navigateTo = () => {
      common_vendor.index.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent("https://www.eeclat.cn/jump_mp/#wechat_redirect")}`
      });
    };
    const navigateToDetail = async (caseId) => {
      try {
        await recordUserOperation("案例展示", "查看详情", caseId);
        common_vendor.index.navigateTo({
          url: `/pages/case_detail/case_detail?id=${caseId}`
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/real_case/real_case.vue:255", "记录操作日志失败:", error);
        common_vendor.index.navigateTo({
          url: `/pages/case_detail/case_detail?id=${caseId}`
        });
      }
    };
    const recordUserOperation = async (menuName, buttonName, extraData = "") => {
      try {
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const browserPath = currentPage.route;
        const logData = {
          button_name: buttonName,
          // 按钮名称
          button_type: buttonName,
          // 按钮类型
          page_url: `/${browserPath}`,
          // 浏览器路径
          page_plate: menuName
          // 菜单名称
          // operation_time: new Date().toISOString(), // 操作时间
          // extra_data: extraData.toString() // 额外数据（如案例ID）
        };
        await utils_api.api.operationLog.recordOperation(logData);
        common_vendor.index.__f__("log", "at pages/real_case/real_case.vue:282", "用户操作日志记录成功:", logData);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/real_case/real_case.vue:285", "记录用户操作日志失败:", error);
      }
    };
    const formatAmount = (amount) => {
      if (!amount)
        return "0.00";
      return amount.toLocaleString("zh-CN", { minimumFractionDigits: 2 });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => navigateTo()),
        b: common_vendor.f(filteredCases.value, (caseItem, k0, i0) => {
          return {
            a: common_vendor.t(caseItem.title),
            b: common_vendor.t(caseItem.date),
            c: common_vendor.t(formatAmount(caseItem.debtAmount)),
            d: common_vendor.t(caseItem.reductionRate),
            e: common_vendor.t(caseItem.summary),
            f: caseItem.id,
            g: common_vendor.o(($event) => navigateToDetail(caseItem.id), caseItem.id)
          };
        }),
        c: filteredCases.value.length === 0 && !loading.value
      }, filteredCases.value.length === 0 && !loading.value ? {} : {}, {
        d: loading.value
      }, loading.value ? {} : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-54ea8e22"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/real_case/real_case.js.map
