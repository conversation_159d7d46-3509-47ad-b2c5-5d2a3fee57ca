{"version": 3, "file": "app-initializer.js", "sources": ["utils/app-initializer.js"], "sourcesContent": ["// 应用初始化工具\nimport { isDebug } from '@/config/env.js';\nimport { \n  initPerformanceOptimization, \n  destroyPerformanceOptimization,\n  performanceMonitor,\n  memoryOptimizer \n} from '@/utils/performance-optimizer.js';\nimport wechatAuth from '@/utils/wechat-auth.js';\n\n/**\n * 应用初始化器\n */\nclass AppInitializer {\n  constructor() {\n    this.initialized = false;\n    this.initPromise = null;\n  }\n\n  /**\n   * 初始化应用\n   */\n  async init() {\n    if (this.initialized) {\n      return this.initPromise;\n    }\n\n    if (this.initPromise) {\n      return this.initPromise;\n    }\n\n    this.initPromise = this._performInit();\n    return this.initPromise;\n  }\n\n  /**\n   * 执行初始化\n   */\n  async _performInit() {\n    const monitor = performanceMonitor;\n    monitor.start('app-init');\n\n    try {\n      if (isDebug()) {\n        console.log('开始应用初始化...');\n      }\n\n      // 1. 初始化性能优化\n      await this._initPerformanceOptimization();\n\n      // 2. 初始化微信认证\n      await this._initWechatAuth();\n\n      // 3. 初始化全局错误处理\n      await this._initGlobalErrorHandler();\n\n      // 4. 初始化页面生命周期监听\n      await this._initPageLifecycle();\n\n      this.initialized = true;\n\n      if (isDebug()) {\n        console.log('应用初始化完成');\n      }\n\n      monitor.end('app-init');\n      return { success: true };\n    } catch (error) {\n      monitor.end('app-init');\n      console.error('应用初始化失败:', error);\n      return { success: false, error };\n    }\n  }\n\n  /**\n   * 初始化性能优化\n   */\n  async _initPerformanceOptimization() {\n    try {\n      initPerformanceOptimization();\n      \n      // 添加微信认证相关的清理任务\n      memoryOptimizer.addCleanupTask(() => {\n        // 清理过期的登录信息\n        try {\n          const tokenExpireTime = uni.getStorageSync('token_expire_time');\n          if (tokenExpireTime && Date.now() > tokenExpireTime) {\n            wechatAuth.clearLoginInfo();\n          }\n        } catch (error) {\n          console.error('清理过期登录信息失败:', error);\n        }\n      });\n\n      if (isDebug()) {\n        console.log('性能优化初始化完成');\n      }\n    } catch (error) {\n      console.error('性能优化初始化失败:', error);\n    }\n  }\n\n  /**\n   * 初始化微信认证\n   */\n  async _initWechatAuth() {\n    try {\n      // 检查登录状态但不自动登录\n      await wechatAuth.checkLoginStatus();\n      \n      if (isDebug()) {\n        console.log('微信认证初始化完成');\n      }\n    } catch (error) {\n      console.error('微信认证初始化失败:', error);\n    }\n  }\n\n  /**\n   * 初始化全局错误处理\n   */\n  async _initGlobalErrorHandler() {\n    try {\n      // 监听未捕获的Promise错误\n      if (typeof uni !== 'undefined' && uni.onUnhandledRejection) {\n        uni.onUnhandledRejection((event) => {\n          console.error('未处理的Promise错误:', event.reason);\n          \n          // 在调试模式下显示错误\n          if (isDebug()) {\n            uni.showToast({\n              title: '发生未处理错误',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        });\n      }\n\n      // 监听JS错误\n      /* if (typeof uni !== 'undefined' && uni.onError) {\n        uni.onError((error) => {\n          console.error('JS错误:', error);\n          \n          // 在调试模式下显示错误\n          if (isDebug()) {\n            uni.showToast({\n              title: 'JS执行错误',\n              icon: 'none',\n              duration: 2000\n            });\n          }\n        });\n      } */\n\n      if (isDebug()) {\n        console.log('全局错误处理初始化完成');\n      }\n    } catch (error) {\n      console.error('全局错误处理初始化失败:', error);\n    }\n  }\n\n  /**\n   * 初始化页面生命周期监听\n   */\n  async _initPageLifecycle() {\n    try {\n      // 监听页面显示\n      if (typeof uni !== 'undefined' && uni.onAppShow) {\n        uni.onAppShow(() => {\n          if (isDebug()) {\n            console.log('应用显示');\n          }\n          \n          // 检查登录状态\n          wechatAuth.checkLoginStatus().catch(error => {\n            console.error('检查登录状态失败:', error);\n          });\n        });\n      }\n\n      // 监听页面隐藏\n      if (typeof uni !== 'undefined' && uni.onAppHide) {\n        uni.onAppHide(() => {\n          if (isDebug()) {\n            console.log('应用隐藏');\n          }\n          \n          // 执行内存清理\n          memoryOptimizer.performCleanup();\n        });\n      }\n\n      if (isDebug()) {\n        console.log('页面生命周期监听初始化完成');\n      }\n    } catch (error) {\n      console.error('页面生命周期监听初始化失败:', error);\n    }\n  }\n\n  /**\n   * 销毁应用\n   */\n  destroy() {\n    try {\n      // 销毁性能优化\n      destroyPerformanceOptimization();\n\n      // 销毁微信认证\n      if (wechatAuth && typeof wechatAuth.destroy === 'function') {\n        wechatAuth.destroy();\n      }\n\n      this.initialized = false;\n      this.initPromise = null;\n\n      if (isDebug()) {\n        console.log('应用已销毁');\n      }\n    } catch (error) {\n      console.error('应用销毁失败:', error);\n    }\n  }\n\n  /**\n   * 获取初始化状态\n   */\n  isInitialized() {\n    return this.initialized;\n  }\n}\n\n// 创建全局实例\nexport const appInitializer = new AppInitializer();\n\n/**\n * 快捷初始化函数\n */\nexport async function initApp() {\n  return await appInitializer.init();\n}\n\n/**\n * 快捷销毁函数\n */\nexport function destroyApp() {\n  appInitializer.destroy();\n}\n\n/**\n * 检查应用是否已初始化\n */\nexport function isAppInitialized() {\n  return appInitializer.isInitialized();\n}\n"], "names": ["performanceMonitor", "isDebug", "uni", "initPerformanceOptimization", "memoryOptimizer", "wechatAuth", "destroyPerformanceOptimization"], "mappings": ";;;;;AAaA,MAAM,eAAe;AAAA,EACnB,cAAc;AACZ,SAAK,cAAc;AACnB,SAAK,cAAc;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,OAAO;AACX,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK;AAAA,IACb;AAED,QAAI,KAAK,aAAa;AACpB,aAAO,KAAK;AAAA,IACb;AAED,SAAK,cAAc,KAAK;AACxB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,eAAe;AACnB,UAAM,UAAUA,2BAAAA;AAChB,YAAQ,MAAM,UAAU;AAExB,QAAI;AACF,UAAIC,WAAO,QAAA,GAAI;AACbC,sBAAAA,qDAAY,YAAY;AAAA,MACzB;AAGD,YAAM,KAAK;AAGX,YAAM,KAAK;AAGX,YAAM,KAAK;AAGX,YAAM,KAAK;AAEX,WAAK,cAAc;AAEnB,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAY,MAAA,OAAA,kCAAA,SAAS;AAAA,MACtB;AAED,cAAQ,IAAI,UAAU;AACtB,aAAO,EAAE,SAAS;IACnB,SAAQ,OAAO;AACd,cAAQ,IAAI,UAAU;AACtBA,2EAAc,YAAY,KAAK;AAC/B,aAAO,EAAE,SAAS,OAAO;IAC1B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,+BAA+B;AACnC,QAAI;AACFC,iCAAAA;AAGAC,iCAAe,gBAAC,eAAe,MAAM;AAEnC,YAAI;AACF,gBAAM,kBAAkBF,cAAAA,MAAI,eAAe,mBAAmB;AAC9D,cAAI,mBAAmB,KAAK,IAAG,IAAK,iBAAiB;AACnDG,6BAAU,WAAC,eAAc;AAAA,UAC1B;AAAA,QACF,SAAQ,OAAO;AACdH,wBAAA,MAAA,MAAA,SAAA,kCAAc,eAAe,KAAK;AAAA,QACnC;AAAA,MACT,CAAO;AAED,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,qDAAY,WAAW;AAAA,MACxB;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,kCAAA,cAAc,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,kBAAkB;AACtB,QAAI;AAEF,YAAMG,iBAAAA,WAAW;AAEjB,UAAIJ,WAAO,QAAA,GAAI;AACbC,sBAAAA,sDAAY,WAAW;AAAA,MACxB;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,mCAAA,cAAc,KAAK;AAAA,IAClC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,0BAA0B;AAC9B,QAAI;AAEF,UAAI,OAAOA,cAAG,UAAK,eAAeA,cAAAA,MAAI,sBAAsB;AAC1DA,4BAAI,qBAAqB,CAAC,UAAU;AAClCA,wBAAc,MAAA,MAAA,SAAA,mCAAA,kBAAkB,MAAM,MAAM;AAG5C,cAAID,WAAO,QAAA,GAAI;AACbC,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO;AAAA,cACP,MAAM;AAAA,cACN,UAAU;AAAA,YACxB,CAAa;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACF;AAkBD,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAY,MAAA,OAAA,mCAAA,aAAa;AAAA,MAC1B;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAA,MAAA,MAAA,SAAA,mCAAc,gBAAgB,KAAK;AAAA,IACpC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,MAAM,qBAAqB;AACzB,QAAI;AAEF,UAAI,OAAOA,cAAG,UAAK,eAAeA,cAAAA,MAAI,WAAW;AAC/CA,sBAAG,MAAC,UAAU,MAAM;AAClB,cAAID,WAAO,QAAA,GAAI;AACbC,0BAAAA,MAAA,MAAA,OAAA,mCAAY,MAAM;AAAA,UACnB;AAGDG,2BAAAA,WAAW,iBAAgB,EAAG,MAAM,WAAS;AAC3CH,0BAAA,MAAA,MAAA,SAAA,mCAAc,aAAa,KAAK;AAAA,UAC5C,CAAW;AAAA,QACX,CAAS;AAAA,MACF;AAGD,UAAI,OAAOA,cAAG,UAAK,eAAeA,cAAAA,MAAI,WAAW;AAC/CA,sBAAG,MAAC,UAAU,MAAM;AAClB,cAAID,WAAO,QAAA,GAAI;AACbC,0BAAAA,MAAA,MAAA,OAAA,mCAAY,MAAM;AAAA,UACnB;AAGDE,qCAAe,gBAAC,eAAc;AAAA,QACxC,CAAS;AAAA,MACF;AAED,UAAIH,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAA,MAAA,OAAA,mCAAY,eAAe;AAAA,MAC5B;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAA,MAAA,MAAA,SAAA,mCAAc,kBAAkB,KAAK;AAAA,IACtC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,UAAU;AACR,QAAI;AAEFI,iCAAAA;AAGA,UAAID,+BAAc,OAAOA,4BAAW,YAAY,YAAY;AAC1DA,yBAAU,WAAC,QAAO;AAAA,MACnB;AAED,WAAK,cAAc;AACnB,WAAK,cAAc;AAEnB,UAAIJ,WAAO,QAAA,GAAI;AACbC,sBAAAA,MAAA,MAAA,OAAA,mCAAY,OAAO;AAAA,MACpB;AAAA,IACF,SAAQ,OAAO;AACdA,oBAAc,MAAA,MAAA,SAAA,mCAAA,WAAW,KAAK;AAAA,IAC/B;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKD,gBAAgB;AACd,WAAO,KAAK;AAAA,EACb;AACH;AAGO,MAAM,iBAAiB,IAAI;AAK3B,eAAe,UAAU;AAC9B,SAAO,MAAM,eAAe;AAC9B;AAKO,SAAS,aAAa;AAC3B,iBAAe,QAAO;AACxB;;;"}