"use strict";
const DEFAULT_AVATAR_BASE64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNFNUU3RUIiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iIzlDQTNBRiIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiM5Q0EzQUYiLz48L2c+PC9zdmc+";
const MALE_AVATAR_BASE64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNEREY0RkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iIzM5OEVGNyIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiMzOThFRjciLz48L2c+PC9zdmc+";
const FEMALE_AVATAR_BASE64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGRUY3RkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iI0VDNEE5OSIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiNFQzRBOTkiLz48L2c+PC9zdmc+";
const PLACEHOLDER_AVATAR_BASE64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGM0Y0RjYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjMwIiBmaWxsPSIjRDFENURCIiBvcGFjaXR5PSIwLjUiLz48L3N2Zz4=";
const ERROR_AVATAR_BASE64 = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGRUY3RkYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjQwIiBmaWxsPSIjRkVGMkYyIiBzdHJva2U9IiNGODcxNzEiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNGODcxNzEiPj88L3RleHQ+PC9zdmc+";
function getBase64Avatar(type = "default", gender = null) {
  if (type === "default" && gender !== null) {
    switch (parseInt(gender)) {
      case 0:
        return MALE_AVATAR_BASE64;
      case 1:
        return FEMALE_AVATAR_BASE64;
      default:
        return DEFAULT_AVATAR_BASE64;
    }
  }
  switch (type) {
    case "male":
      return MALE_AVATAR_BASE64;
    case "female":
      return FEMALE_AVATAR_BASE64;
    case "placeholder":
    case "loading":
      return PLACEHOLDER_AVATAR_BASE64;
    case "error":
      return ERROR_AVATAR_BASE64;
    default:
      return DEFAULT_AVATAR_BASE64;
  }
}
exports.getBase64Avatar = getBase64Avatar;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/default-avatar-base64.js.map
