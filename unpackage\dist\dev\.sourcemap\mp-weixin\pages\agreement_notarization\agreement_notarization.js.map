{"version": 3, "file": "agreement_notarization.js", "sources": ["pages/agreement_notarization/agreement_notarization.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvYWdyZWVtZW50X25vdGFyaXphdGlvbi9hZ3JlZW1lbnRfbm90YXJpemF0aW9uLnZ1ZQ"], "sourcesContent": ["<template>\r\n  <view class=\"work-order-detail-container\">\r\n    <!-- 案件基本信息 -->\r\n    <view class=\"work-order-card\">\r\n      <view class=\"work-order-header\">\r\n        <view class=\"work-order-title\">\r\n          <text>调解案件号: </text>\r\n          <text class=\"work-order-id\">{{\r\n            workOrderData.id || \"MED20230001\"\r\n          }}</text>\r\n        </view>\r\n        <view class=\"work-order-status\">\r\n          <text\r\n            class=\"status-label\"\r\n            :class=\"{ 'status-pending': workOrderData.status === '已完成' }\"\r\n            >{{ workOrderData.status || \"已完成\" }}</text\r\n          >\r\n        </view>\r\n      </view>\r\n      <view class=\"work-order-date\"\r\n        >发起日期: {{ workOrderData.createDate || \"2023-11-01\" }}</view\r\n      >\r\n    </view>\r\n\r\n    <!-- 进度条 -->\r\n    <view class=\"progress-bar\">\r\n      <view class=\"progress-steps\">\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">1</view>\r\n          <view class=\"step-line active\"></view>\r\n          <view class=\"step-label\">调解确认</view>\r\n        </view>\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">2</view>\r\n          <view class=\"step-line active\"></view>\r\n          <view class=\"step-label\">方案确认</view>\r\n        </view>\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">3</view>\r\n          <view class=\"step-line active\"></view>\r\n          <view class=\"step-label\">协议签署</view>\r\n        </view>\r\n        <view class=\"progress-step active\">\r\n          <view class=\"step-circle\">4</view>\r\n          <view class=\"step-label\">完成</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 协议公证 -->\r\n    <view class=\"card mt-20\">\r\n      <!-- 装饰性背景元素 -->\r\n      <view\r\n        style=\"\r\n          position: absolute;\r\n          top: -15px;\r\n          right: -15px;\r\n          width: 80px;\r\n          height: 80px;\r\n          background: rgba(255, 193, 7, 0.1);\r\n          border-radius: 50%;\r\n        \"\r\n      >\r\n      </view>\r\n      <view\r\n        style=\"\r\n          position: absolute;\r\n          bottom: -10px;\r\n          left: -10px;\r\n          width: 60px;\r\n          height: 60px;\r\n          background: rgba(255, 193, 7, 0.08);\r\n          border-radius: 50%;\r\n        \"\r\n      >\r\n      </view>\r\n\r\n      <view style=\"text-align: center; position: relative; z-index: 2\">\r\n        <view\r\n          style=\"\r\n            width: 70px;\r\n            height: 70px;\r\n            background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%);\r\n            border-radius: 50%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin: 0 auto 20px;\r\n            box-shadow: 0 6px 16px rgba(255, 143, 0, 0.3);\r\n          \"\r\n        >\r\n          <i\r\n            class=\"fas fa-certificate\"\r\n            style=\"font-size: 30px; color: white\"\r\n          ></i>\r\n        </view>\r\n        <h3\r\n          style=\"\r\n            margin: 0 0 12px 0;\r\n            color: #e65100;\r\n            font-size: 20px;\r\n            font-weight: 600;\r\n          \"\r\n        >\r\n          协议公证服务\r\n        </h3>\r\n        <view\r\n          style=\"\r\n            margin: 0;\r\n            font-size: 15px;\r\n            color: #bf360c;\r\n            line-height: 1.6;\r\n            font-weight: 500;\r\n          \"\r\n          >为您的调解协议提供法律保障</view\r\n        >\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"info-section\">\r\n      <view class=\"section-title\"\r\n        ><i\r\n          class=\"fas fa-question-circle\"\r\n          style=\"\r\n            color: var(--primary-color);\r\n            margin-right: 10px;\r\n            font-size: 20px;\r\n          \"\r\n        ></i\r\n        >什么是协议公证？</view\r\n      >\r\n      <div\r\n        style=\"\r\n          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);\r\n          border-radius: 12px;\r\n          padding: 20px;\r\n          margin-bottom: 20px;\r\n          border: 1px solid rgba(0, 0, 0, 0.05);\r\n        \"\r\n      >\r\n        <p\r\n          style=\"\r\n            font-size: 15px;\r\n            color: var(--text-color);\r\n            line-height: 1.7;\r\n            margin: 0;\r\n            text-align: justify;\r\n          \"\r\n        >\r\n          协议公证是指公证机构根据当事人的申请，依照法定程序对已签署的调解协议内容进行审查并予以证明的活动。经公证的调解协议具有<text\r\n            style=\"color: var(--primary-color)\"\r\n            >强制执行效力</text\r\n          >，双方当事人均需严格按照协议条款约定进行履约。\r\n        </p>\r\n      </div>\r\n    </view>\r\n\r\n    <!-- 免费公证服务 -->\r\n    <view class=\"free-service-card\">\r\n      <view class=\"service-header\">\r\n        <view class=\"service-icon\">\r\n          <i class=\"fas fa-gift\"></i>\r\n        </view>\r\n        <view class=\"service-title-container\">\r\n          <text class=\"service-title\">免费公证服务</text>\r\n          <text class=\"service-subtitle\">专业法律保障，零费用申请</text>\r\n        </view>\r\n      </view>\r\n      <view class=\"service-content\">\r\n\r\n        <view class=\"price-section\">\r\n          <view class=\"price-item\">\r\n            <text class=\"price-label\">公证服务费用</text>\r\n            <view class=\"price-content\">\r\n              <text class=\"original-price\">¥200.00</text>\r\n              <text class=\"free-price\">免费</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"price-item\">\r\n            <text class=\"price-label\">您需支付的费用</text>\r\n            <text class=\"final-price\">¥0.00</text>\r\n          </view>\r\n        </view>\r\n\r\n        <view class=\"service-tips\">\r\n          <i class=\"fas fa-info-circle\"></i>\r\n          <text>本调解中心与某市公证处合作提供， 经公证支付的费用</text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 确认公证申请 -->\r\n    <view class=\"notarization-decision\">\r\n      <view class=\"decision-header\">\r\n        <i\r\n          class=\"fas fa-clipboard-check\"\r\n          style=\"\r\n            color: var(--primary-color);\r\n            font-size: 20px;\r\n            margin-right: 8px;\r\n          \"\r\n        ></i>\r\n        <text class=\"decision-title\">确认公证申请</text>\r\n      </view>\r\n      <view class=\"decision-description\">\r\n        请选择是否对本调解协议进行公证。公证后，\r\n        协议具有法律强制执行力，双方当事人均需 严格按照协议条款进行履约。\r\n      </view>\r\n\r\n      <!-- 申请协议公证 -->\r\n      <view \r\n        class=\"decision-option\" \r\n        :class=\"{ 'selected-apply': notarizationChoice === 'apply' }\"\r\n        @click=\"handleSelectChoice('apply')\"\r\n      >\r\n        <view class=\"option-header\">\r\n          <view class=\"radio-container\">\r\n            <view \r\n              class=\"radio-button\"\r\n              :class=\"{ 'radio-checked-apply': notarizationChoice === 'apply' }\"\r\n            >\r\n              <i \r\n                v-if=\"notarizationChoice === 'apply'\" \r\n                class=\"fas fa-check\"\r\n                style=\"color: white; font-size: 12px;\"\r\n              ></i>\r\n            </view>\r\n          </view>\r\n          <text class=\"option-title\">申请协议公证</text>\r\n          <text class=\"option-badge\">推荐</text>\r\n        </view>\r\n        <view class=\"option-description\">\r\n          为协议进行免费公证确认协议法律效力。公证后，\r\n          协议具有强制执行力，双方当事人均需严格履行。\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 暂不公证 -->\r\n      <view \r\n        class=\"decision-option\" \r\n        :class=\"{ 'selected-skip': notarizationChoice === 'skip' }\"\r\n        @click=\"handleSelectChoice('skip')\"\r\n      >\r\n        <view class=\"option-header\">\r\n          <view class=\"radio-container\">\r\n            <view \r\n              class=\"radio-button\"\r\n              :class=\"{ 'radio-checked-skip': notarizationChoice === 'skip' }\"\r\n            >\r\n              <i \r\n                v-if=\"notarizationChoice === 'skip'\" \r\n                class=\"fas fa-check\"\r\n                style=\"color: white; font-size: 12px;\"\r\n              ></i>\r\n            </view>\r\n          </view>\r\n          <text class=\"option-title\">暂不公证</text>\r\n        </view>\r\n        <view class=\"option-description\">\r\n          协议仅具有合同效力，但不具备强制执行效力。\r\n          如后续需要强制执行，需另行申请公证或诉讼。\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 底部操作按钮 -->\r\n    <view class=\"action-buttons\">\r\n      <button class=\"confirm-button\" @click=\"handleConfirmChoice\">\r\n        <i class=\"fas fa-file-contract\"></i>\r\n        {{ notarizationChoice === 'apply' ? '确认公证申请' : '确认暂不公证' }}\r\n      </button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from \"vue\";\r\nimport { api } from \"@/utils/api.js\";\r\n\r\n// 接收参数\r\nconst orderId = ref(\"\");\r\n\r\n// 工单数据\r\nconst workOrderData = ref({\r\n  id: \"MED20230001\",\r\n  status: \"已完成\",\r\n  createDate: \"2023-11-01\",\r\n  creditor: \"某银行信用卡中心\",\r\n  amount: \"50,000.00\",\r\n  reduction_amount: \"10,000.00\",\r\n  monthlyRepayment: \"2,083.33\",\r\n  notarizationStatus: \"未公证\",\r\n  paymentMemo: \"MED20230003_张先生_123456_华泰民商事调解中心\",\r\n});\r\n\r\nconst isCopied = ref(false);\r\n\r\n// 公证选择状态('apply' | 'skip')\r\nconst notarizationChoice = ref('apply');\r\n// 选择公证选项\r\nconst handleSelectChoice = async (choice) => {\r\n  notarizationChoice.value = choice;\r\n  \r\n  // 记录操作日志\r\n  const buttonName = choice === 'apply' ? '选择申请协议公证' : '选择暂不公证';\r\n  await recordOperationLog('协议公证', buttonName);\r\n};\r\n\r\n// 确认选择\r\nconst handleConfirmChoice = async () => {\r\n  if (notarizationChoice.value === 'apply') {\r\n    await handleApplyNotarization();\r\n  } else {\r\n    await handleSkipNotarization();\r\n  }\r\n};\r\n\r\n// 申请协议公证 - 移除选择状态设置，因为已在handleSelectChoice中处理\r\nconst handleApplyNotarization = async () => {\r\n  // 记录操作日志\r\n  await recordOperationLog('协议公证', '确认公证申请');\r\n  \r\n  uni.showModal({\r\n    title: '确认申请',\r\n    content: '确认申请免费协议公证服务？公证完成后协议将具有法律强制执行效力。',\r\n    confirmText: '确认申请',\r\n    cancelText: '取消',\r\n    success: async (res) => {\r\n      if (res.confirm) {\r\n        uni.showLoading({\r\n          title: '申请中...'\r\n        });\r\n        \r\n        try {\r\n          // 检查token\r\n          const token = uni.getStorageSync('token') || getCurrentPages()[0]?.$page?.options?.token;\r\n          if (!token) {\r\n            uni.hideLoading();\r\n            uni.showToast({\r\n              title: '请先登录',\r\n              icon: 'none'\r\n            });\r\n            return;\r\n          }\r\n          \r\n          // 调用申请公证API\r\n          const result = await api.notarization.apply({\r\n            workOrderId: orderId.value || workOrderData.value.id\r\n          });\r\n          \r\n          uni.hideLoading();\r\n          \r\n          if (result.code === 0) {\r\n            uni.showToast({\r\n              title: '申请成功',\r\n              icon: 'success'\r\n            });\r\n            \r\n            // 更新工单状态\r\n            workOrderData.value.notarizationStatus = '申请中';\r\n            \r\n            // 可以跳转到申请结果页面或继续下一步\r\n            setTimeout(() => {\r\n              uni.navigateTo({\r\n                url: `/pages/notarization_result/notarization_result?id=${orderId.value}&type=apply`\r\n              });\r\n            }, 1500);\r\n          } else {\r\n            uni.showToast({\r\n              title: result.message || '申请失败',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        } catch (error) {\r\n          uni.hideLoading();\r\n          console.error('申请公证失败:', error);\r\n          uni.showToast({\r\n            title: '申请失败，请稍后重试',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 暂不公证 - 移除选择状态设置，因为已在handleSelectChoice中处理\r\nconst handleSkipNotarization = async () => {\r\n  // 记录操作日志\r\n  await recordOperationLog('协议公证', '确认暂不公证');\r\n  \r\n  uni.showModal({\r\n    title: '确认选择',\r\n    content: '确认暂不进行协议公证？您可以在协议签署完成后随时申请公证服务。',\r\n    confirmText: '确认',\r\n    cancelText: '取消',\r\n    success: async (res) => {\r\n      if (res.confirm) {\r\n        uni.showLoading({\r\n          title: '处理中...'\r\n        });\r\n        \r\n        try {\r\n          // 检查token\r\n          const token = uni.getStorageSync('token') || getCurrentPages()[0]?.$page?.options?.token;\r\n          if (!token) {\r\n            uni.hideLoading();\r\n            uni.showToast({\r\n              title: '请先登录',\r\n              icon: 'none'\r\n            });\r\n            return;\r\n          }\r\n          \r\n          // 调用跳过公证API\r\n          const result = await api.notarization.skip({\r\n            workOrderId: orderId.value || workOrderData.value.id\r\n          });\r\n          \r\n          uni.hideLoading();\r\n          \r\n          if (result.code === 0) {\r\n            uni.showToast({\r\n              title: '已确认',\r\n              icon: 'success'\r\n            });\r\n            \r\n            // 更新工单状态\r\n            workOrderData.value.notarizationStatus = '暂不公证';\r\n            \r\n            // 跳转到下一步或完成页面\r\n            setTimeout(() => {\r\n              uni.navigateTo({\r\n                url: `/pages/case_completed/case_completed?id=${orderId.value}`\r\n              });\r\n            }, 1500);\r\n          } else {\r\n            uni.showToast({\r\n              title: result.message || '操作失败',\r\n              icon: 'none'\r\n            });\r\n          }\r\n        } catch (error) {\r\n          uni.hideLoading();\r\n          console.error('跳过公证失败:', error);\r\n          uni.showToast({\r\n            title: '操作失败，请稍后重试',\r\n            icon: 'none'\r\n          });\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n  // 获取页面参数\r\n  const pages = getCurrentPages();\r\n  const currentPage = pages[pages.length - 1];\r\n  const options = currentPage.$page?.options;\r\n\r\n  if (options && options.id) {\r\n    orderId.value = options.id;\r\n    console.log(\"接收到工单ID:\", orderId.value);\r\n    fetchWorkOrderDetail(orderId.value);\r\n  } else {\r\n    // 默认使用模拟数据\r\n    fetchWorkOrderDetail();\r\n  }\r\n});\r\n\r\n// 获取调解确认数据\r\nconst fetchWorkOrderDetail = (id) => {\r\n  if (id) {\r\n    // 使用API获取数据\r\n    uni.showLoading({\r\n      title: \"加载中...\",\r\n    });\r\n\r\n    api.workOrder\r\n      .getDetail(id)\r\n      .then((res) => {\r\n        uni.hideLoading();\r\n        if (res.code === 0) {\r\n          workOrderData.value = res.data;\r\n        } else {\r\n          uni.showToast({\r\n            title: res.message || \"获取调解确认失败\",\r\n            icon: \"none\",\r\n          });\r\n        }\r\n      })\r\n      .catch((err) => {\r\n        uni.hideLoading();\r\n        console.error(\"获取调解确认失败\", err);\r\n        uni.showToast({\r\n          title: \"获取调解确认失败\",\r\n          icon: \"none\",\r\n        });\r\n      });\r\n  } else {\r\n    // 使用模拟数据\r\n    setTimeout(() => {\r\n      console.log(\"调解确认数据已加载（模拟）\");\r\n    }, 500);\r\n  }\r\n};\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n:root {\r\n  --text-color: #333;\r\n  --text-secondary: #666;\r\n  --success-color: #52c41a;\r\n  --warning-color: #faad14;\r\n  --primary-color: #3b7eeb;\r\n  --primary-dark: #2c62c9;\r\n  --border-color: #e0e0e0;\r\n  --transition-normal: all 0.3s ease;\r\n}\r\n.work-order-detail-container {\r\n  min-height: 100vh;\r\n  background-color: #f5f5f5;\r\n  padding: 30rpx;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 30rpx;\r\n}\r\n\r\n.work-order-card {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.work-order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.work-order-title {\r\n  font-size: 30rpx;\r\n  color: #333;\r\n  font-weight: bold;\r\n}\r\n\r\n.work-order-status {\r\n  padding: 6rpx 20rpx;\r\n  border-radius: 30rpx;\r\n}\r\n\r\n.status-label {\r\n  font-size: 26rpx;\r\n  padding: 8rpx 20rpx;\r\n  border-radius: 30rpx;\r\n  background-color: #52c41a;\r\n  color: #fff;\r\n}\r\n\r\n.status-pending {\r\n  background-color: #52c41a;\r\n}\r\n\r\n.work-order-date {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n}\r\n\r\n.progress-steps {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  position: relative;\r\n}\r\n\r\n.progress-step {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  flex: 1;\r\n  position: relative;\r\n}\r\n\r\n.step-circle {\r\n  width: 60rpx;\r\n  height: 60rpx;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 28rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  z-index: 2;\r\n}\r\n\r\n.step-line {\r\n  position: absolute;\r\n  top: 30rpx;\r\n  left: 50%;\r\n  right: -50%;\r\n  height: 4rpx;\r\n  background-color: #e0e0e0;\r\n  z-index: 1;\r\n}\r\n\r\n.progress-step:last-child .step-line {\r\n  display: none;\r\n}\r\n.step-line.active {\r\n  background-color: #2979ff;\r\n}\r\n.step-label {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-align: center;\r\n}\r\n\r\n.progress-step.active .step-circle {\r\n  background-color: #2979ff;\r\n}\r\n\r\n.progress-step.active .step-label {\r\n  color: #2979ff;\r\n  font-weight: bold;\r\n}\r\n\r\n.card {\r\n  background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%);\r\n  border: 2rpx solid #ffd54f;\r\n  border-radius: 32rpx;\r\n  padding: 50rpx 40rpx;\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.info-section {\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  padding: 30rpx;\r\n  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.section-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  margin-bottom: 20rpx;\r\n  position: relative;\r\n  display: inline-flex;\r\n}\r\n\r\n/* 免费公证服务卡片样式 */\r\n.free-service-card {\r\n  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);\r\n  border: 2rpx solid #b3e5fc;\r\n  border-radius: 24rpx;\r\n  padding: 40rpx;\r\n  margin-bottom: 40rpx;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.free-service-card::before {\r\n  content: '';\r\n  position: absolute;\r\n  top: -20rpx;\r\n  right: -20rpx;\r\n  width: 120rpx;\r\n  height: 120rpx;\r\n  background: #d9f0ff;\r\n  border-radius: 50%;\r\n}\r\n\r\n.service-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n  .service-icon {\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);\r\n    border-radius: 50%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-right: 16rpx;\r\n    .fas {\r\n      color: #fff; \r\n      font-size: 40rpx;\r\n    }\r\n  }\r\n}\r\n\r\n.service-title {\r\n  font-size: 36rpx;\r\n  font-weight: bold;\r\n  color: #3b7eeb;\r\n  margin-bottom: 10rpx;\r\n}\r\n\r\n.service-subtitle {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  display: block;\r\n}\r\n\r\n.price-section {\r\n  background: white;\r\n  border-radius: 16rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 30rpx;\r\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.price-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.price-item:last-child {\r\n  margin-bottom: 0;\r\n  padding-top: 20rpx;\r\n  border-top: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.price-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.price-content {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.original-price {\r\n  font-size: 24rpx;\r\n  color: #999;\r\n  text-decoration: line-through;\r\n  margin-right: 15rpx;\r\n}\r\n\r\n.free-price {\r\n  font-size: 32rpx;\r\n  color: var(--success-color);\r\n  font-weight: bold;\r\n}\r\n\r\n.final-price {\r\n  font-size: 36rpx;\r\n  color: var(--success-color);\r\n  font-weight: bold;\r\n}\r\n\r\n.service-tips {\r\n  display: flex;\r\n  align-items: center;\r\n  font-size: 24rpx;\r\n  color: var(--success-color);\r\n  line-height: 1.5;\r\n  .fas {\r\n    color: var(--success-color);\r\n    font-size: 28rpx; \r\n    margin-right: 12rpx;\r\n  }\r\n}\r\n\r\n/* 公证决策模块样式更新 */\r\n.notarization-decision {\r\n  background: white;\r\n  border-radius: 24rpx;\r\n  padding: 40rpx;\r\n  margin-bottom: 40rpx;\r\n  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.decision-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.decision-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n}\r\n\r\n.decision-description {\r\n  font-size: 28rpx;\r\n  color: #666;\r\n  line-height: 1.6;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.decision-option {\r\n  border: 3rpx solid #e0e0e0;\r\n  border-radius: 20rpx;\r\n  padding: 30rpx;\r\n  margin-bottom: 20rpx;\r\n  transition: all 0.3s ease;\r\n  cursor: pointer;\r\n  background: #fafafa;\r\n}\r\n\r\n.decision-option:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.decision-option:active {\r\n  transform: scale(0.98);\r\n}\r\n\r\n/* 选中申请协议公证的样式 */\r\n.decision-option.selected-apply {\r\n  border-color: #3b7eeb;\r\n  background: #3b7eeb;\r\n}\r\n\r\n.decision-option.selected-apply .option-title {\r\n  color: white;\r\n}\r\n\r\n.decision-option.selected-apply .option-description {\r\n  color: rgba(255, 255, 255, 0.9);\r\n}\r\n\r\n.decision-option.selected-apply .option-badge {\r\n  background: rgba(255, 255, 255, 0.2);\r\n  color: white;\r\n}\r\n\r\n/* 选中暂不公证的样式 */\r\n.decision-option.selected-skip {\r\n  border-color: #faad14;\r\n  background: rgb(255, 247, 230);\r\n}\r\n\r\n.decision-option.selected-skip .option-title {\r\n  color: #faad14;\r\n}\r\n\r\n.decision-option.selected-skip .option-description {\r\n  color: #b8860b;\r\n}\r\n\r\n.option-header {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 15rpx;\r\n  position: relative;\r\n}\r\n\r\n.radio-container {\r\n  margin-right: 20rpx;\r\n}\r\n\r\n.radio-button {\r\n  width: 40rpx;\r\n  height: 40rpx;\r\n  border: 3rpx solid #e0e0e0;\r\n  border-radius: 50%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: all 0.3s ease;\r\n  background: white;\r\n}\r\n\r\n/* 申请协议公证选中时的单选框样式 */\r\n.radio-button.radio-checked-apply {\r\n  border-color: #3b7eeb;\r\n  background: var(--primary-color);\r\n}\r\n\r\n/* 暂不公证选中时的单选框样式 */\r\n.radio-button.radio-checked-skip {\r\n  border-color: #faad14;\r\n  background: #faad14;\r\n}\r\n\r\n.option-title {\r\n  font-size: 30rpx;\r\n  font-weight: bold;\r\n  color: #333;\r\n  flex: 1;\r\n}\r\n\r\n.option-badge {\r\n  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);\r\n  color: white;\r\n  font-size: 20rpx;\r\n  padding: 6rpx 16rpx;\r\n  border-radius: 20rpx;\r\n  font-weight: bold;\r\n}\r\n\r\n.option-description {\r\n  font-size: 26rpx;\r\n  color: #666;\r\n  line-height: 1.5;\r\n  margin-left: 60rpx;\r\n}\r\n\r\n// 底部按钮\r\n.action-buttons {\r\n  margin-top: 40rpx;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.confirm-button {\r\n  width: 100%;\r\n  height: 90rpx;\r\n  line-height: 90rpx;\r\n  text-align: center;\r\n  border-radius: 16rpx;\r\n  font-size: 32rpx;\r\n  background-color: #2979ff;\r\n  color: #fff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  .fas {\r\n    margin-right: 10rpx;\r\n  }\r\n}\r\n</style>", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/agreement_notarization/agreement_notarization.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "uni", "api", "onMounted"], "mappings": ";;;;;;AAuRA,UAAM,UAAUA,cAAAA,IAAI,EAAE;AAGtB,UAAM,gBAAgBA,cAAAA,IAAI;AAAA,MACxB,IAAI;AAAA,MACJ,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,UAAU;AAAA,MACV,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,aAAa;AAAA,IACf,CAAC;AAEgBA,kBAAG,IAAC,KAAK;AAG1B,UAAM,qBAAqBA,cAAAA,IAAI,OAAO;AAEtC,UAAM,qBAAqB,OAAO,WAAW;AAC3C,yBAAmB,QAAQ;AAG3B,YAAM,aAAa,WAAW,UAAU,aAAa;AACrD,YAAM,mBAAmB,QAAQ,UAAU;AAAA,IAC7C;AAGA,UAAM,sBAAsB,YAAY;AACtC,UAAI,mBAAmB,UAAU,SAAS;AACxC,cAAM,wBAAuB;AAAA,MACjC,OAAS;AACL,cAAM,uBAAsB;AAAA,MAC7B;AAAA,IACH;AAGA,UAAM,0BAA0B,YAAY;AAE1C,YAAM,mBAAmB,QAAQ,QAAQ;AAEzCC,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,OAAO,QAAQ;;AACtB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACjB,CAAS;AAED,gBAAI;AAEF,oBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO,OAAK,iCAAiB,EAAC,CAAC,MAAnB,mBAAsB,UAAtB,mBAA6B,YAA7B,mBAAsC;AACnF,kBAAI,CAAC,OAAO;AACVA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AACD;AAAA,cACD;AAGD,oBAAM,SAAS,MAAMC,cAAI,aAAa,MAAM;AAAA,gBAC1C,aAAa,QAAQ,SAAS,cAAc,MAAM;AAAA,cAC9D,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS,GAAG;AACrBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD,8BAAc,MAAM,qBAAqB;AAGzC,2BAAW,MAAM;AACfA,gCAAAA,MAAI,WAAW;AAAA,oBACb,KAAK,qDAAqD,QAAQ,KAAK;AAAA,kBACvF,CAAe;AAAA,gBACF,GAAE,IAAI;AAAA,cACnB,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO,OAAO,WAAW;AAAA,kBACzB,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,kEAAc,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGA,UAAM,yBAAyB,YAAY;AAEzC,YAAM,mBAAmB,QAAQ,QAAQ;AAEzCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,OAAO,QAAQ;;AACtB,cAAI,IAAI,SAAS;AACfA,0BAAAA,MAAI,YAAY;AAAA,cACd,OAAO;AAAA,YACjB,CAAS;AAED,gBAAI;AAEF,oBAAM,QAAQA,cAAAA,MAAI,eAAe,OAAO,OAAK,iCAAiB,EAAC,CAAC,MAAnB,mBAAsB,UAAtB,mBAA6B,YAA7B,mBAAsC;AACnF,kBAAI,CAAC,OAAO;AACVA,8BAAG,MAAC,YAAW;AACfA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AACD;AAAA,cACD;AAGD,oBAAM,SAAS,MAAMC,cAAI,aAAa,KAAK;AAAA,gBACzC,aAAa,QAAQ,SAAS,cAAc,MAAM;AAAA,cAC9D,CAAW;AAEDD,4BAAG,MAAC,YAAW;AAEf,kBAAI,OAAO,SAAS,GAAG;AACrBA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO;AAAA,kBACP,MAAM;AAAA,gBACpB,CAAa;AAGD,8BAAc,MAAM,qBAAqB;AAGzC,2BAAW,MAAM;AACfA,gCAAAA,MAAI,WAAW;AAAA,oBACb,KAAK,2CAA2C,QAAQ,KAAK;AAAA,kBAC7E,CAAe;AAAA,gBACF,GAAE,IAAI;AAAA,cACnB,OAAiB;AACLA,8BAAAA,MAAI,UAAU;AAAA,kBACZ,OAAO,OAAO,WAAW;AAAA,kBACzB,MAAM;AAAA,gBACpB,CAAa;AAAA,cACF;AAAA,YACF,SAAQ,OAAO;AACdA,4BAAG,MAAC,YAAW;AACfA,4BAAA,MAAA,MAAA,SAAA,kEAAc,WAAW,KAAK;AAC9BA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,OAAO;AAAA,gBACP,MAAM;AAAA,cAClB,CAAW;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGAE,kBAAAA,UAAU,MAAM;;AAEd,YAAM,QAAQ;AACd,YAAM,cAAc,MAAM,MAAM,SAAS,CAAC;AAC1C,YAAM,WAAU,iBAAY,UAAZ,mBAAmB;AAEnC,UAAI,WAAW,QAAQ,IAAI;AACzB,gBAAQ,QAAQ,QAAQ;AACxBF,sBAAA,MAAA,MAAA,OAAA,kEAAY,YAAY,QAAQ,KAAK;AACrC,6BAAqB,QAAQ,KAAK;AAAA,MACtC,OAAS;AAEL;MACD;AAAA,IACH,CAAC;AAGD,UAAM,uBAAuB,CAAC,OAAO;AACnC,UAAI,IAAI;AAENA,sBAAAA,MAAI,YAAY;AAAA,UACd,OAAO;AAAA,QACb,CAAK;AAEDC,kBAAAA,IAAI,UACD,UAAU,EAAE,EACZ,KAAK,CAAC,QAAQ;AACbD,wBAAG,MAAC,YAAW;AACf,cAAI,IAAI,SAAS,GAAG;AAClB,0BAAc,QAAQ,IAAI;AAAA,UACpC,OAAe;AACLA,0BAAAA,MAAI,UAAU;AAAA,cACZ,OAAO,IAAI,WAAW;AAAA,cACtB,MAAM;AAAA,YAClB,CAAW;AAAA,UACF;AAAA,QACT,CAAO,EACA,MAAM,CAAC,QAAQ;AACdA,wBAAG,MAAC,YAAW;AACfA,wBAAA,MAAA,MAAA,SAAA,kEAAc,YAAY,GAAG;AAC7BA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UAChB,CAAS;AAAA,QACT,CAAO;AAAA,MACP,OAAS;AAEL,mBAAW,MAAM;AACfA,wBAAAA,MAAY,MAAA,OAAA,kEAAA,eAAe;AAAA,QAC5B,GAAE,GAAG;AAAA,MACP;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;;;;ACzfA,GAAG,WAAW,eAAe;"}