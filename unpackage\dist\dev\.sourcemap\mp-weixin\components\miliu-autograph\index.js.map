{"version": 3, "file": "index.js", "sources": ["components/miliu-autograph/miliu-autograph.vue"], "sourcesContent": ["<template>\r\n\t<!-- \r\n\t\t电子签名组件\r\n\t\t整体容器使用fixed定位，覆盖整个屏幕\r\n\t\t防止触摸事件穿透和滚动\r\n\t-->\r\n\t<view class=\"whole canvas-autograph flexc\" \r\n\t\************************ \r\n\t\******************** \r\n\t\tv-show=\"modelValue\">\r\n\t\t\r\n\t\t<!-- 加载提示 -->\r\n\t\t<view class=\"loading-mask\" v-if=\"isLoading\">\r\n\t\t\t<view class=\"loading-content\">\r\n\t\t\t\t<text class=\"loading-text\">加载中...</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 画布区域 -->\r\n\t\t<canvas \r\n\t\t\tclass=\"scroll-view\" \r\n\t\t\tid=\"mycanvas\" \r\n\t\t\tcanvas-id=\"mycanvas\" \r\n\t\t\t@touchstart=\"touchstart\" \r\n\t\t\t@touchmove=\"touchmove\" \r\n\t\t\t@touchend=\"touchend\"/>\r\n\t\t\r\n\t\t<!-- 签名区域指示 -->\r\n\t\t<view class=\"signature-area-indicator\" v-if=\"showSignatureArea\">\r\n\t\t\t<view class=\"signature-label\">{{ signatureLabel }}</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 操作按钮区域 -->\r\n\t\t<view class=\"fun-box\">\r\n\t\t\t<!-- 清空按钮 -->\r\n\t\t\t<view class=\"fun-box-btn clear flex\" @click=\"clear\">\r\n\t\t\t\t<text>清空</text>\r\n\t\t\t</view>\r\n\t\t\t<!-- 确认按钮 -->\r\n\t\t\t<view class=\"fun-box-btn confirm flex\" @click=\"confirm\">\r\n\t\t\t\t<text>确认</text>\r\n\t\t\t</view>\r\n\t\t\t<!-- 取消按钮 -->\r\n\t\t\t<view class=\"fun-box-btn cancel flex\" @click=\"cancel\">\r\n\t\t\t\t<text>取消</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t\r\n\t\t<!-- 操作提示 -->\r\n\t\t<view class=\"tip-text\">\r\n\t\t\t<text>请在指定区域进行签名</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\n\t/**\r\n\t * 电子签名组件 (miliu-autograph)\r\n\t * \r\n\t * 使用示例:\r\n\t * <miliu-autograph \r\n\t *   v-model=\"isCanvas\" \r\n\t *   @complete=\"complete\" \r\n\t *   :showSignatureLine=\"true\"\r\n\t *   signatureLabel=\"签名\"\r\n\t * />\r\n\t * \r\n\t * 参数说明:\r\n\t * - v-model: 控制组件显示/隐藏的布尔值\r\n\t * - @complete: 签名完成后的回调，参数为签名图片的临时路径\r\n\t * - showSignatureLine: 是否显示签名线，默认为true\r\n\t * - signatureLineY: 签名线的Y坐标位置，默认为60%的屏幕高度\r\n\t * - signatureLabel: 签名线旁边的标签文本，默认为\"签名：\"\r\n\t * - signatureLineColor: 签名线的颜色，默认为\"#999999\"\r\n\t * \r\n\t * 使用方式:\r\n\t * import { ref } from 'vue';\r\n\t * \r\n\t * // 控制组件显示/隐藏\r\n\t * const isCanvas = ref(false);\r\n\t * \r\n\t * // 确认事件处理\r\n\t * const complete = path => {\r\n\t *   console.log('签名图片路径:', path);\r\n\t *   // 在这里处理签名图片\r\n\t * }\r\n\t * \r\n\t * // 打开签名面板\r\n\t * function openCanvas() {\r\n\t *   isCanvas.value = true;\r\n\t * }\r\n\t */\r\n\timport { ref, reactive, watch, getCurrentInstance, onMounted, onBeforeUnmount } from 'vue'\r\n\t\r\n\t// 定义组件事件\r\n\tconst emits = defineEmits(['update:modelValue', 'complete'])\r\n\t\r\n\t// 定义组件属性\r\n\tconst props = defineProps({\r\n\t\t/**\r\n\t\t * 控制组件显示/隐藏\r\n\t\t */\r\n\t\tmodelValue: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t/**\r\n\t\t * 画笔颜色\r\n\t\t */\r\n\t\tpenColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#000000'\r\n\t\t},\r\n\t\t/**\r\n\t\t * 画笔宽度\r\n\t\t */\r\n\t\tpenWidth: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 4\r\n\t\t},\r\n\t\t/**\r\n\t\t * 是否显示签名线\r\n\t\t */\r\n\t\tshowSignatureLine: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t/**\r\n\t\t * 签名线的Y坐标位置（百分比，0-100）\r\n\t\t */\r\n\t\tsignatureLineY: {\r\n\t\t\ttype: Number,\r\n\t\t\tdefault: 60\r\n\t\t},\r\n\t\t/**\r\n\t\t * 签名线旁边的标签文本\r\n\t\t */\r\n\t\tsignatureLabel: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"签名：\"\r\n\t\t},\r\n\t\t/**\r\n\t\t * 签名线颜色\r\n\t\t */\r\n\t\tsignatureLineColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: \"#999999\"\r\n\t\t},\r\n\t\t/**\r\n\t\t * 是否显示签名区域指示\r\n\t\t */\r\n\t\tshowSignatureArea: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t})\r\n\t\r\n\t// 获取组件实例\r\n\tconst instance = getCurrentInstance()\r\n\t\r\n\t// 状态变量\r\n\tconst isLoading = ref(false)       // 加载状态\r\n\tconst hasDrawn = ref(false)        // 是否已绘制内容\r\n\tlet points = reactive([])          // 路径点集合\r\n\tlet canvaCtx = null                // Canvas上下文\r\n\tlet canvasWidth = 0                // 画布宽度\r\n\tlet canvasHeight = 0               // 画布高度\r\n\tlet signatureLinePosition = 0      // 签名线位置\r\n\t\r\n\t/**\r\n\t * 监听组件显示状态变化\r\n\t * 当组件显示时初始化Canvas上下文\r\n\t */\r\n\twatch(() => props.modelValue, (newValue) => {\r\n\t\tif (newValue) {\r\n\t\t\t// 显示组件时初始化Canvas\r\n\t\t\tinitCanvas()\r\n\t\t}\r\n\t}, {\r\n\t\timmediate: true // 立即执行\r\n\t})\r\n\t\r\n\t/**\r\n\t * 组件挂载时的初始化\r\n\t */\r\n\tonMounted(() => {\r\n\t\tconsole.log('电子签名组件已挂载')\r\n\t})\r\n\t\r\n\t/**\r\n\t * 组件卸载前清理资源\r\n\t */\r\n\tonBeforeUnmount(() => {\r\n\t\t// 释放资源\r\n\t\tcanvaCtx = null\r\n\t\tpoints = []\r\n\t})\r\n\t\r\n\t/**\r\n\t * 初始化Canvas上下文\r\n\t */\r\n\tconst initCanvas = () => {\r\n\t\tisLoading.value = true\r\n\t\t\r\n\t\t// 延迟执行，确保DOM已更新\r\n\t\tsetTimeout(() => {\r\n\t\t\ttry {\r\n\t\t\t\t// 获取系统信息以确定画布尺寸\r\n\t\t\t\tuni.getSystemInfo()\r\n\t\t\t\t.then(res => {\r\n\t\t\t\t\tcanvasWidth = res.windowWidth\r\n\t\t\t\t\tcanvasHeight = res.windowHeight\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 计算签名线位置\r\n\t\t\t\t\tsignatureLinePosition = canvasHeight * props.signatureLineY / 100\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 创建Canvas上下文\r\n\t\t\t\t\tcanvaCtx = uni.createCanvasContext('mycanvas', instance)\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (canvaCtx) {\r\n\t\t\t\t\t\t// 设置画笔样式\r\n\t\t\t\t\t\tcanvaCtx.lineWidth = props.penWidth\r\n\t\t\t\t\t\tcanvaCtx.strokeStyle = props.penColor\r\n\t\t\t\t\t\tcanvaCtx.lineCap = 'round'\r\n\t\t\t\t\t\tcanvaCtx.lineJoin = 'round'\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// 绘制签名线\r\n\t\t\t\t\t\tif (props.showSignatureLine) {\r\n\t\t\t\t\t\t\tdrawSignatureLine()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\tconsole.log('Canvas上下文创建成功')\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconsole.error('Canvas上下文创建失败')\r\n\t\t\t\t\t\t// 显示错误提示\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '画布初始化失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\t.catch(err => {\r\n\t\t\t\t\tconsole.error('获取系统信息失败:', err)\r\n\t\t\t\t})\r\n\t\t\t\t.finally(() => {\r\n\t\t\t\t\tisLoading.value = false\r\n\t\t\t\t})\r\n\t\t\t} catch (e) {\r\n\t\t\t\tconsole.error('初始化Canvas时出错:', e)\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '画布初始化错误',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t})\r\n\t\t\t\tisLoading.value = false\r\n\t\t\t}\r\n\t\t}, 100)\r\n\t}\r\n\t\r\n\t/**\r\n\t * 绘制签名线\r\n\t */\r\n\tconst drawSignatureLine = () => {\r\n\t\tif (!canvaCtx) return\r\n\t\t\r\n\t\t// 保存当前状态\r\n\t\tcanvaCtx.save()\r\n\t\t\r\n\t\t// 设置签名线样式\r\n\t\tcanvaCtx.lineWidth = 1\r\n\t\tcanvaCtx.strokeStyle = props.signatureLineColor\r\n\t\t\r\n\t\t// 绘制签名线\r\n\t\tconst padding = 40\r\n\t\tconst lineStartX = padding\r\n\t\tconst lineEndX = canvasWidth - padding\r\n\t\t\r\n\t\tcanvaCtx.beginPath()\r\n\t\tcanvaCtx.moveTo(lineStartX, signatureLinePosition)\r\n\t\tcanvaCtx.lineTo(lineEndX, signatureLinePosition)\r\n\t\tcanvaCtx.stroke()\r\n\t\t\r\n\t\t// 绘制签名标签\r\n\t\tif (props.signatureLabel) {\r\n\t\t\tcanvaCtx.fillStyle = props.signatureLineColor\r\n\t\t\tcanvaCtx.font = '14px sans-serif'\r\n\t\t\tcanvaCtx.textAlign = 'right'\r\n\t\t\tcanvaCtx.fillText(props.signatureLabel, lineStartX - 5, signatureLinePosition + 5)\r\n\t\t}\r\n\t\t\r\n\t\t// 应用绘制\r\n\t\tcanvaCtx.draw(true)\r\n\t\t\r\n\t\t// 恢复之前的状态\r\n\t\tcanvaCtx.restore()\r\n\t\t\r\n\t\t// 重新设置画笔样式\r\n\t\tcanvaCtx.lineWidth = props.penWidth\r\n\t\tcanvaCtx.strokeStyle = props.penColor\r\n\t}\r\n\t\r\n\t/**\r\n\t * 触摸开始事件处理\r\n\t * 记录起始点并开始新的绘制路径\r\n\t */\r\n\tconst touchstart = e => {\r\n\t\tif (!canvaCtx) {\r\n\t\t\tconsole.error('Canvas上下文未创建')\r\n\t\t\treturn\r\n\t\t}\r\n\t\t\r\n\t\tlet startX = e.changedTouches[0].x\r\n\t\tlet startY = e.changedTouches[0].y\r\n\t\tlet startPoint = { X: startX, Y: startY }\r\n\t\tpoints.push(startPoint)\r\n\t\t\r\n\t\t// 开始新的绘制路径\r\n\t\tcanvaCtx.beginPath()\r\n\t\thasDrawn.value = true\r\n\t}\r\n\t\r\n\t/**\r\n\t * 触摸移动事件处理\r\n\t * 记录移动点并绘制路径\r\n\t */\r\n\tconst touchmove = e => {\r\n\t\tif (!canvaCtx) return\r\n\t\t\r\n\t\tlet moveX = e.changedTouches[0].x\r\n\t\tlet moveY = e.changedTouches[0].y\r\n\t\tlet movePoint = { X: moveX, Y: moveY }\r\n\t\tpoints.push(movePoint)       // 存点\r\n\t\t\r\n\t\tlet len = points.length\r\n\t\tif (len >= 2) {\r\n\t\t\tdraw()\r\n\t\t}\r\n\t}\r\n\t\r\n\t/**\r\n\t * 绘制路径\r\n\t * 连接前后两个点形成线段\r\n\t */\r\n\tconst draw = () => {\r\n\t\tif (!canvaCtx) return\r\n\t\t\r\n\t\tlet point1 = points[0]\r\n\t\tlet point2 = points[1]\r\n\t\tpoints.shift()\r\n\t\t\r\n\t\tcanvaCtx.moveTo(point1.X, point1.Y)\r\n\t\tcanvaCtx.lineTo(point2.X, point2.Y)\r\n\t\tcanvaCtx.stroke()\r\n\t\tcanvaCtx.draw(true)\r\n\t}\r\n\t\r\n\t/**\r\n\t * 触摸结束事件处理\r\n\t * 清空点集合，防止影响下次绘制\r\n\t */\r\n\tconst touchend = e => {\r\n\t\tpoints = []\r\n\t}\r\n\t\r\n\t/**\r\n\t * 清空画布\r\n\t * 获取系统信息以确定清除区域大小\r\n\t */\r\n\tconst clear = () => {\r\n\t\tif (!canvaCtx) {\r\n\t\t\tconsole.error('Canvas上下文未创建')\r\n\t\t\treturn Promise.reject('Canvas上下文未创建')\r\n\t\t}\r\n\t\t\r\n\t\treturn uni.getSystemInfo()\r\n\t\t.then(res => {\r\n\t\t\tcanvaCtx.clearRect(0, 0, res.windowWidth, res.windowHeight)\r\n\t\t\tcanvaCtx.draw(true)\r\n\t\t\thasDrawn.value = false\r\n\t\t\t\r\n\t\t\t// 重新绘制签名线\r\n\t\t\tif (props.showSignatureLine) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tdrawSignatureLine()\r\n\t\t\t\t}, 50)\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn res\r\n\t\t})\r\n\t\t.catch(err => {\r\n\t\t\tconsole.error('清空画布时出错:', err)\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '清空画布失败',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn Promise.reject(err)\r\n\t\t})\r\n\t}\r\n\t\r\n\t/**\r\n\t * 确认签名\r\n\t * 将画布内容转换为图片并通过事件返回\r\n\t */\r\n\tconst confirm = () => {\r\n\t\tif (!canvaCtx) {\r\n\t\t\tconsole.error('Canvas上下文未创建')\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '画布未准备好',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\t\t\r\n\t\tif (!hasDrawn.value) {\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '请先进行签名',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t\treturn\r\n\t\t}\r\n\t\t\r\n\t\tisLoading.value = true\r\n\t\t\r\n\t\ttry {\r\n\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\tcanvasId: 'mycanvas',\r\n\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\tconsole.log('签名保存成功:', res.tempFilePath)\r\n\t\t\t\t\temits('complete', res.tempFilePath)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '签名已保存',\r\n\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tcancel()\r\n\t\t\t\t},\r\n\t\t\t\tfail: (err) => {\r\n\t\t\t\t\tconsole.error('保存签名失败:', err)\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '保存签名失败',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t},\r\n\t\t\t\tcomplete: () => {\r\n\t\t\t\t\tisLoading.value = false\r\n\t\t\t\t}\r\n\t\t\t}, instance)\r\n\t\t} catch (e) {\r\n\t\t\tconsole.error('转换画布为图片时出错:', e)\r\n\t\t\tisLoading.value = false\r\n\t\t\tuni.showToast({\r\n\t\t\t\ttitle: '保存签名出错',\r\n\t\t\t\ticon: 'none'\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\t\r\n\t/**\r\n\t * 取消签名\r\n\t * 清空画布并关闭组件\r\n\t */\r\n\tconst cancel = () => {\r\n\t\tif (canvaCtx) {\r\n\t\t\tclear()\r\n\t\t\t.then(() => {\r\n\t\t\t\tconsole.log('关闭电子签名组件')\r\n\t\t\t\temits('update:modelValue', false)\r\n\t\t\t})\r\n\t\t\t.catch(() => {\r\n\t\t\t\t// 即使清空失败也要关闭组件\r\n\t\t\t\temits('update:modelValue', false)\r\n\t\t\t})\r\n\t\t} else {\r\n\t\t\t// 如果canvas上下文不存在，直接关闭\r\n\t\t\tconsole.log('关闭电子签名组件(无需清空)')\r\n\t\t\temits('update:modelValue', false)\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t/* 电子签名组件样式 */\r\n\t.canvas-autograph {\r\n\t\tposition: fixed;\r\n\t\tz-index: 998;\r\n\t\twidth: 100vw;\r\n\t\theight: 100vh;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\t\r\n\t\t/* 画布区域 */\r\n\t\t.scroll-view {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t}\r\n\t\t\r\n\t\t/* 签名区域指示 */\r\n\t\t.signature-area-indicator {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tpointer-events: none; /* 确保不会阻止触摸事件 */\r\n\t\t\t\r\n\t\t\t.signature-label {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tleft: 40rpx;\r\n\t\t\t\ttop: calc(60% - 40rpx); /* 默认位置，与签名线对应 */\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tcolor: #999999;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t/* 操作按钮容器 */\r\n\t\t.fun-box {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 0;\r\n\t\t\tbottom: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n\t\t\t\r\n\t\t\t/* 按钮通用样式 */\r\n\t\t\t.fun-box-btn {\r\n\t\t\t\twidth: 100rpx;\r\n\t\t\t\theight: 160rpx;\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\tborder-radius: 20rpx;\r\n\t\t\t\tborder: 1rpx solid #C0C0C0;\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\t\r\n\t\t\t\t/* 文本旋转 */\r\n\t\t\t\ttext {\r\n\t\t\t\t\ttransform: rotate(90deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t/* 清空按钮 */\r\n\t\t\t.clear {\r\n\t\t\t\tcolor: #909399;\r\n\t\t\t\tbackground-color: #F4F4F5;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t/* 确认按钮 */\r\n\t\t\t.confirm {\r\n\t\t\t\tbackground-color: #409EFF;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t/* 取消按钮 */\r\n\t\t\t.cancel {\r\n\t\t\t\tbackground-color: #F67D7D;\r\n\t\t\t}\r\n\t\t}\r\n\t\t\r\n\t\t/* 提示文本 */\r\n\t\t.tip-text {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 30rpx;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: #909399;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t}\r\n\t\t\r\n\t\t/* 加载遮罩 */\r\n\t\t.loading-mask {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t\tbackground-color: rgba(0, 0, 0, 0.3);\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tz-index: 999;\r\n\t\t\t\r\n\t\t\t.loading-content {\r\n\t\t\t\tbackground-color: rgba(0, 0, 0, 0.7);\r\n\t\t\t\tpadding: 30rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\r\n\t\t\t\t.loading-text {\r\n\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style> "], "names": ["getCurrentInstance", "ref", "reactive", "watch", "onMounted", "uni", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+FC,UAAM,QAAQ;AAGd,UAAM,QAAQ;AA4Dd,UAAM,WAAWA,cAAAA,mBAAoB;AAGrC,UAAM,YAAYC,cAAG,IAAC,KAAK;AAC3B,UAAM,WAAWA,cAAG,IAAC,KAAK;AAC1B,QAAI,SAASC,cAAQ,SAAC,EAAE;AACxB,QAAI,WAAW;AACf,QAAI,cAAc;AAClB,QAAI,eAAe;AACnB,QAAI,wBAAwB;AAM5BC,kBAAAA,MAAM,MAAM,MAAM,YAAY,CAAC,aAAa;AAC3C,UAAI,UAAU;AAEb,mBAAY;AAAA,MACZ;AAAA,IACH,GAAI;AAAA,MACF,WAAW;AAAA;AAAA,IACb,CAAE;AAKDC,kBAAAA,UAAU,MAAM;AACfC,oBAAAA,MAAA,MAAA,OAAA,yDAAY,WAAW;AAAA,IACzB,CAAE;AAKDC,kBAAAA,gBAAgB,MAAM;AAErB,iBAAW;AACX,eAAS,CAAE;AAAA,IACb,CAAE;AAKD,UAAM,aAAa,MAAM;AACxB,gBAAU,QAAQ;AAGlB,iBAAW,MAAM;AAChB,YAAI;AAEHD,wBAAAA,MAAI,cAAe,EAClB,KAAK,SAAO;AACZ,0BAAc,IAAI;AAClB,2BAAe,IAAI;AAGnB,oCAAwB,eAAe,MAAM,iBAAiB;AAG9D,uBAAWA,cAAG,MAAC,oBAAoB,YAAY,QAAQ;AAEvD,gBAAI,UAAU;AAEb,uBAAS,YAAY,MAAM;AAC3B,uBAAS,cAAc,MAAM;AAC7B,uBAAS,UAAU;AACnB,uBAAS,WAAW;AAGpB,kBAAI,MAAM,mBAAmB;AAC5B,kCAAmB;AAAA,cACnB;AAEDA,4BAAAA,MAAY,MAAA,OAAA,yDAAA,eAAe;AAAA,YACjC,OAAY;AACNA,4BAAAA,MAAc,MAAA,SAAA,yDAAA,eAAe;AAE7BA,4BAAAA,MAAI,UAAU;AAAA,gBACb,OAAO;AAAA,gBACP,MAAM;AAAA,cACb,CAAO;AAAA,YACD;AAAA,UACN,CAAK,EACA,MAAM,SAAO;AACbA,0BAAAA,8EAAc,aAAa,GAAG;AAAA,UACnC,CAAK,EACA,QAAQ,MAAM;AACd,sBAAU,QAAQ;AAAA,UACvB,CAAK;AAAA,QACD,SAAQ,GAAG;AACXA,wBAAAA,MAAA,MAAA,SAAA,yDAAc,iBAAiB,CAAC;AAChCA,wBAAAA,MAAI,UAAU;AAAA,YACb,OAAO;AAAA,YACP,MAAM;AAAA,UACX,CAAK;AACD,oBAAU,QAAQ;AAAA,QAClB;AAAA,MACD,GAAE,GAAG;AAAA,IACN;AAKD,UAAM,oBAAoB,MAAM;AAC/B,UAAI,CAAC;AAAU;AAGf,eAAS,KAAM;AAGf,eAAS,YAAY;AACrB,eAAS,cAAc,MAAM;AAG7B,YAAM,UAAU;AAChB,YAAM,aAAa;AACnB,YAAM,WAAW,cAAc;AAE/B,eAAS,UAAW;AACpB,eAAS,OAAO,YAAY,qBAAqB;AACjD,eAAS,OAAO,UAAU,qBAAqB;AAC/C,eAAS,OAAQ;AAGjB,UAAI,MAAM,gBAAgB;AACzB,iBAAS,YAAY,MAAM;AAC3B,iBAAS,OAAO;AAChB,iBAAS,YAAY;AACrB,iBAAS,SAAS,MAAM,gBAAgB,aAAa,GAAG,wBAAwB,CAAC;AAAA,MACjF;AAGD,eAAS,KAAK,IAAI;AAGlB,eAAS,QAAS;AAGlB,eAAS,YAAY,MAAM;AAC3B,eAAS,cAAc,MAAM;AAAA,IAC7B;AAMD,UAAM,aAAa,OAAK;AACvB,UAAI,CAAC,UAAU;AACdA,sBAAAA,MAAA,MAAA,SAAA,yDAAc,cAAc;AAC5B;AAAA,MACA;AAED,UAAI,SAAS,EAAE,eAAe,CAAC,EAAE;AACjC,UAAI,SAAS,EAAE,eAAe,CAAC,EAAE;AACjC,UAAI,aAAa,EAAE,GAAG,QAAQ,GAAG,OAAQ;AACzC,aAAO,KAAK,UAAU;AAGtB,eAAS,UAAW;AACpB,eAAS,QAAQ;AAAA,IACjB;AAMD,UAAM,YAAY,OAAK;AACtB,UAAI,CAAC;AAAU;AAEf,UAAI,QAAQ,EAAE,eAAe,CAAC,EAAE;AAChC,UAAI,QAAQ,EAAE,eAAe,CAAC,EAAE;AAChC,UAAI,YAAY,EAAE,GAAG,OAAO,GAAG,MAAO;AACtC,aAAO,KAAK,SAAS;AAErB,UAAI,MAAM,OAAO;AACjB,UAAI,OAAO,GAAG;AACb,aAAM;AAAA,MACN;AAAA,IACD;AAMD,UAAM,OAAO,MAAM;AAClB,UAAI,CAAC;AAAU;AAEf,UAAI,SAAS,OAAO,CAAC;AACrB,UAAI,SAAS,OAAO,CAAC;AACrB,aAAO,MAAO;AAEd,eAAS,OAAO,OAAO,GAAG,OAAO,CAAC;AAClC,eAAS,OAAO,OAAO,GAAG,OAAO,CAAC;AAClC,eAAS,OAAQ;AACjB,eAAS,KAAK,IAAI;AAAA,IAClB;AAMD,UAAM,WAAW,OAAK;AACrB,eAAS,CAAE;AAAA,IACX;AAMD,UAAM,QAAQ,MAAM;AACnB,UAAI,CAAC,UAAU;AACdA,sBAAAA,MAAA,MAAA,SAAA,yDAAc,cAAc;AAC5B,eAAO,QAAQ,OAAO,cAAc;AAAA,MACpC;AAED,aAAOA,cAAAA,MAAI,cAAe,EACzB,KAAK,SAAO;AACZ,iBAAS,UAAU,GAAG,GAAG,IAAI,aAAa,IAAI,YAAY;AAC1D,iBAAS,KAAK,IAAI;AAClB,iBAAS,QAAQ;AAGjB,YAAI,MAAM,mBAAmB;AAC5B,qBAAW,MAAM;AAChB,8BAAmB;AAAA,UACnB,GAAE,EAAE;AAAA,QACL;AAED,eAAO;AAAA,MACV,CAAG,EACA,MAAM,SAAO;AACbA,sBAAAA,MAAA,MAAA,SAAA,yDAAc,YAAY,GAAG;AAC7BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AACD,eAAO,QAAQ,OAAO,GAAG;AAAA,MAC5B,CAAG;AAAA,IACD;AAMD,UAAM,UAAU,MAAM;AACrB,UAAI,CAAC,UAAU;AACdA,sBAAAA,MAAA,MAAA,SAAA,yDAAc,cAAc;AAC5BA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AACD;AAAA,MACA;AAED,UAAI,CAAC,SAAS,OAAO;AACpBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AACD;AAAA,MACA;AAED,gBAAU,QAAQ;AAElB,UAAI;AACHA,sBAAAA,MAAI,qBAAqB;AAAA,UACxB,UAAU;AAAA,UACV,SAAS,CAAC,QAAQ;AACjBA,sGAAY,WAAW,IAAI,YAAY;AACvC,kBAAM,YAAY,IAAI,YAAY;AAClCA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACZ,CAAM;AACD,mBAAQ;AAAA,UACR;AAAA,UACD,MAAM,CAAC,QAAQ;AACdA,0BAAAA,8EAAc,WAAW,GAAG;AAC5BA,0BAAAA,MAAI,UAAU;AAAA,cACb,OAAO;AAAA,cACP,MAAM;AAAA,YACZ,CAAM;AAAA,UACD;AAAA,UACD,UAAU,MAAM;AACf,sBAAU,QAAQ;AAAA,UAClB;AAAA,QACD,GAAE,QAAQ;AAAA,MACX,SAAQ,GAAG;AACXA,sBAAAA,MAAc,MAAA,SAAA,yDAAA,eAAe,CAAC;AAC9B,kBAAU,QAAQ;AAClBA,sBAAAA,MAAI,UAAU;AAAA,UACb,OAAO;AAAA,UACP,MAAM;AAAA,QACV,CAAI;AAAA,MACD;AAAA,IACD;AAMD,UAAM,SAAS,MAAM;AACpB,UAAI,UAAU;AACb,cAAO,EACN,KAAK,MAAM;AACXA,wBAAAA,MAAA,MAAA,OAAA,yDAAY,UAAU;AACtB,gBAAM,qBAAqB,KAAK;AAAA,QACpC,CAAI,EACA,MAAM,MAAM;AAEZ,gBAAM,qBAAqB,KAAK;AAAA,QACpC,CAAI;AAAA,MACJ,OAAS;AAENA,sBAAAA,MAAY,MAAA,OAAA,yDAAA,gBAAgB;AAC5B,cAAM,qBAAqB,KAAK;AAAA,MAChC;AAAA,IACD;;;;;;;;;;;;;;;;;;;;;;;;;;"}