/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-info-page.data-v-759141ee {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 40rpx 0;
}
.header-section.data-v-759141ee {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  background-color: #ffffff;
  margin-bottom: 20rpx;
}
.title.data-v-759141ee {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}
.subtitle.data-v-759141ee {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.5;
}
.form-section.data-v-759141ee {
  background-color: #ffffff;
  margin-bottom: 20rpx;
}
.info-section.data-v-759141ee {
  padding: 40rpx;
  background-color: #ffffff;
}
.info-item.data-v-759141ee {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.info-item.data-v-759141ee:last-child {
  margin-bottom: 0;
}
.info-icon.data-v-759141ee {
  font-size: 32rpx;
  margin-right: 20rpx;
}
.info-text.data-v-759141ee {
  font-size: 28rpx;
  color: #666666;
  flex: 1;
}
.loading-mask.data-v-759141ee {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.loading-content.data-v-759141ee {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}