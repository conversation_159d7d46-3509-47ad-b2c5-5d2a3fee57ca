{"version": 3, "file": "contact-fab.js", "sources": ["components/contact-fab/contact-fab.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovd29yay_kuI3oia_otYTkuqfns7vnu58vbm9uLXBlcmZvcm1pbmctYXNzZXRzL2NvbXBvbmVudHMvY29udGFjdC1mYWIvY29udGFjdC1mYWIudnVl"], "sourcesContent": ["<template>\r\n  <view class=\"contact-fab\" :class=\"{ 'contact-fab-open': isOpen }\">\r\n    <!-- 主按钮 -->\r\n    <view class=\"contact-fab-button\" @click=\"toggleFab\">\r\n      <text v-if=\"isOpen\" class=\"fa fas fa-xmark\"></text>\r\n      <text v-else class=\"fa fas fa-headset\"></text>\r\n    </view>\r\n    \r\n    <!-- 展开的菜单项 - 白色背景矩形 -->\r\n    <view class=\"contact-fab-menu\" v-if=\"isOpen\">\r\n      <!-- 电话咨询 -->\r\n      <view class=\"contact-fab-item\" @click=\"handlePhone\">\r\n        <view class=\"contact-fab-item-icon\">\r\n          <text class=\"fa fas fa-phone\"></text>\r\n        </view>\r\n        <text class=\"contact-fab-item-label\">电话咨询</text>\r\n      </view>\r\n      \r\n      <!-- 分隔线 -->\r\n      <view class=\"contact-fab-divider\"></view>\r\n      \r\n      <!-- 微信咨询 -->\r\n      <view class=\"contact-fab-item\" @click=\"handleWechat\">\r\n        <view class=\"contact-fab-item-icon\">\r\n          <text class=\"fab fa-weixin\"></text>\r\n        </view>\r\n        <text class=\"contact-fab-item-label\">微信咨询</text>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 遮罩层 -->\r\n    <view class=\"contact-fab-mask\" v-if=\"isOpen\" @click=\"closeFab\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue';\r\n\r\nconst isOpen = ref(false);\r\n\r\nconst toggleFab = () => {\r\n  isOpen.value = !isOpen.value;\r\n};\r\n\r\nconst closeFab = () => {\r\n  isOpen.value = false;\r\n};\r\n\r\nconst handlePhone = () => {\r\n  // 处理电话咨询\r\n  uni.makePhoneCall({\r\n    phoneNumber: '10086', // 替换为实际电话号码\r\n    success: () => {\r\n      console.log('拨打电话成功');\r\n      closeFab();\r\n    },\r\n    fail: (err) => {\r\n      console.error('拨打电话失败', err);\r\n    }\r\n  });\r\n};\r\n\r\nconst handleWechat = () => {\r\n  // 处理微信咨询，可以复制微信号或打开小程序客服\r\n  uni.setClipboardData({\r\n    data: 'YourWeChatID', // 替换为实际微信号\r\n    success: () => {\r\n      uni.showToast({\r\n        title: '微信号已复制',\r\n        icon: 'success'\r\n      });\r\n      closeFab();\r\n    }\r\n  });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.contact-fab {\r\n  position: fixed;\r\n  right: 30rpx;\r\n  bottom: 120rpx;\r\n  z-index: 999;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.contact-fab-button {\r\n  width: 100rpx;\r\n  height: 100rpx;\r\n  border-radius: 50%;\r\n  background-color: #2979ff;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  box-shadow: 0 2rpx 10rpx rgba(41, 121, 255, 0.3);\r\n  z-index: 1001;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.contact-fab-button .fa {\r\n  font-size: 40rpx;\r\n  color: #fff;\r\n}\r\n\r\n.contact-fab-menu {\r\n  position: absolute;\r\n  bottom: 120rpx;\r\n  right: 0;\r\n  background-color: #fff;\r\n  border-radius: 12rpx;\r\n  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);\r\n  z-index: 1000;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20rpx 0;\r\n  margin-bottom: 20rpx;\r\n  animation: fadeIn 0.3s ease-out;\r\n  width: 360rpx;\r\n}\r\n\r\n.contact-fab-item {\r\n  display: flex;\r\n  flex-direction: row;\r\n  align-items: center;\r\n  justify-content: center;\r\n  padding: 20rpx 0;\r\n}\r\n\r\n.contact-fab-item-icon {\r\n  margin-right: 20rpx;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.contact-fab-item-icon .fa,\r\n.contact-fab-item-icon .fab {\r\n  font-size: 40rpx;\r\n}\r\n\r\n.contact-fab-item-label {\r\n  font-size: 28rpx;\r\n  color: #333;\r\n}\r\n\r\n.fa-phone {\r\n  color: #2979ff;\r\n}\r\n\r\n.fa-weixin {\r\n  color: #07c160;\r\n}\r\n\r\n.contact-fab-divider {\r\n  width: 90%;\r\n  height: 2rpx;\r\n  background-color: #eee;\r\n  margin: 0 auto;\r\n}\r\n\r\n.contact-fab-mask {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: transparent;\r\n  z-index: 998;\r\n}\r\n\r\n/* 动画效果 */\r\n.contact-fab-open .contact-fab-button {\r\n  transform: rotate(135deg);\r\n}\r\n\r\n@keyframes fadeIn {\r\n  from {\r\n    opacity: 0;\r\n    transform: translateY(20rpx);\r\n  }\r\n  to {\r\n    opacity: 1;\r\n    transform: translateY(0);\r\n  }\r\n}\r\n</style> ", "import Component from 'D:/work/不良资产系统/non-performing-assets/components/contact-fab/contact-fab.vue'\nwx.createComponent(Component)"], "names": ["ref", "uni"], "mappings": ";;;;;AAsCA,UAAM,SAASA,cAAAA,IAAI,KAAK;AAExB,UAAM,YAAY,MAAM;AACtB,aAAO,QAAQ,CAAC,OAAO;AAAA,IACzB;AAEA,UAAM,WAAW,MAAM;AACrB,aAAO,QAAQ;AAAA,IACjB;AAEA,UAAM,cAAc,MAAM;AAExBC,oBAAAA,MAAI,cAAc;AAAA,QAChB,aAAa;AAAA;AAAA,QACb,SAAS,MAAM;AACbA,wBAAAA,mEAAY,QAAQ;AACpB;QACD;AAAA,QACD,MAAM,CAAC,QAAQ;AACbA,wBAAc,MAAA,MAAA,SAAA,gDAAA,UAAU,GAAG;AAAA,QAC5B;AAAA,MACL,CAAG;AAAA,IACH;AAEA,UAAM,eAAe,MAAM;AAEzBA,oBAAAA,MAAI,iBAAiB;AAAA,QACnB,MAAM;AAAA;AAAA,QACN,SAAS,MAAM;AACbA,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO;AAAA,YACP,MAAM;AAAA,UACd,CAAO;AACD;QACD;AAAA,MACL,CAAG;AAAA,IACH;;;;;;;;;;;;;;;;;;;;;ACzEA,GAAG,gBAAgB,SAAS;"}