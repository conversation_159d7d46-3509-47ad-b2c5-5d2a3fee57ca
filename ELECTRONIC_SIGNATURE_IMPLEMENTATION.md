# 电子签名功能实现说明

## 功能概述

在Vue组件 `pages/agreement_signing/agreement_signing.vue` 中实现了以下功能：

### 1. 电子签名保存功能

当用户完成电子签名后，系统会自动调用PUT接口保存电子签名图片到服务器。

#### 实现细节：

- **接口地址**: `/mediation_management/mediation_case/wechat/{case_number}/update_electronic_signature/`
- **HTTP方法**: PUT
- **请求格式**: form-data
- **请求参数**:
  - `case_number` (字符串, 必需): 案件号，用于标识要更新电子签名的案件
  - `electronic_signature` (文件, 可选): 电子签名图片文件，支持jpg、png格式

#### 核心函数：

1. **`complete(signatureBase64)`**: 签名完成回调函数
   - 接收base64格式的签名数据
   - 调用API保存到服务器
   - 更新本地签名状态
   - 显示成功/失败提示

2. **`saveElectronicSignature(signatureBase64)`**: 保存电子签名到服务器
   - 将base64数据转换为临时文件
   - 使用API模块上传文件
   - 处理响应和错误

3. **`base64ToTempFile(base64Data)`**: 将base64转换为临时文件
   - 移除base64前缀
   - 使用微信小程序文件系统API创建临时文件
   - 返回文件路径供上传使用

### 2. 页面跳转逻辑

当底部按钮显示文本为"协议签署完成"时，在签名保存成功后会自动跳转到联系方式确认页面。

#### 实现细节：

- **跳转条件**: 按钮文本为"协议签署完成"且用户已完成签名
- **跳转目标**: `pages/contact_information/contact_information.vue`
- **跳转方式**: 使用 `uni.navigateTo()` 进行页面导航

#### 核心函数：

**`handleConfirm()`**: 底部确认按钮点击处理
- 检查签名状态和按钮文本
- 如果是"协议签署完成"状态，则跳转到联系方式确认页面
- 显示确认完成提示
- 处理其他状态的逻辑

## API配置更新

### 新增API常量定义

在 `utils/api.js` 中添加了电子签名相关的API配置：

```javascript
/**
 * 电子签名相关API
 */
export const ELECTRONIC_SIGNATURE_API = {
  UPDATE: '/mediation_management/mediation_case/wechat/{case_number}/update_electronic_signature/'
};
```

### 新增API调用方法

```javascript
// 电子签名相关
electronicSignature: {
  // 更新电子签名
  updateSignature: (caseNumber, filePath) => {
    // 使用uni.uploadFile进行文件上传
    // 自动添加认证头
    // 处理响应和错误
  }
}
```

## 错误处理

实现了完善的错误处理机制：

1. **网络错误**: 显示网络请求失败提示
2. **服务器错误**: 显示服务器错误状态码
3. **文件转换错误**: 处理base64转文件失败的情况
4. **参数验证**: 检查案件号是否为空
5. **状态回滚**: 失败时重置签名状态

## 用户体验优化

1. **加载状态**: 显示"正在处理"的加载提示
2. **成功反馈**: 显示"签名保存成功"提示
3. **错误反馈**: 显示具体的错误信息
4. **状态持久化**: 将签名状态保存到本地存储
5. **防重复操作**: 已签名状态下禁止重复签名

## 技术特点

1. **模块化设计**: 将API调用封装在独立的模块中
2. **异步处理**: 使用async/await处理异步操作
3. **类型安全**: 添加了详细的JSDoc注释
4. **兼容性**: 支持微信小程序环境
5. **可维护性**: 代码结构清晰，易于维护和扩展

## 使用方式

用户在协议签署页面：
1. 勾选"我已阅读并同意《调解协议》的全部条款"
2. 点击电子签名区域进入签名界面
3. 完成签名后自动保存到服务器
4. 点击"协议签署完成"按钮跳转到下一页面

## 注意事项

1. 确保案件号参数正确传递
2. 服务器需要支持PUT方法和form-data格式
3. 需要正确的认证token
4. 文件上传需要适当的超时设置
5. 临时文件会自动清理，无需手动删除
