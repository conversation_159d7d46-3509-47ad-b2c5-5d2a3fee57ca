"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const WECHAT_ERROR_TYPES = {
  LOGIN_FAILED: "wechat_login_failed",
  // 微信登录失败
  CODE_INVALID: "wechat_code_invalid",
  // code无效
  USER_CANCELLED: "wechat_user_cancelled",
  // 用户取消
  PROFILE_FAILED: "wechat_profile_failed",
  // 获取用户信息失败
  AVATAR_FAILED: "wechat_avatar_failed",
  // 头像选择失败
  API_LIMIT: "wechat_api_limit",
  // API调用限制
  NETWORK_ERROR: "wechat_network_error",
  // 网络错误
  SYSTEM_ERROR: "wechat_system_error"
  // 系统错误
};
function analyzeWechatError(error) {
  let message = "";
  let errCode = "";
  if (error instanceof Error) {
    message = error.message || "";
  } else if (typeof error === "string") {
    message = error;
  } else if (error && error.errMsg) {
    message = error.errMsg;
    errCode = error.errCode || "";
  } else {
    message = String(error || "");
  }
  const lowerMessage = message.toLowerCase();
  if (lowerMessage.includes("cancel") || lowerMessage.includes("取消") || message.includes("getUserProfile:fail cancel") || message.includes("chooseAvatar:fail cancel")) {
    return {
      type: WECHAT_ERROR_TYPES.USER_CANCELLED,
      level: "info",
      message: "用户取消操作",
      originalMessage: message,
      errCode
    };
  }
  if (lowerMessage.includes("network") || lowerMessage.includes("网络") || lowerMessage.includes("request:fail") || lowerMessage.includes("timeout") || lowerMessage.includes("超时")) {
    return {
      type: WECHAT_ERROR_TYPES.NETWORK_ERROR,
      level: "warning",
      message: "网络连接失败，请检查网络后重试",
      originalMessage: message,
      errCode
    };
  }
  if (errCode === "45011" || lowerMessage.includes("too many requests") || lowerMessage.includes("api limit")) {
    return {
      type: WECHAT_ERROR_TYPES.API_LIMIT,
      level: "warning",
      message: "API调用过于频繁，请稍后重试",
      originalMessage: message,
      errCode
    };
  }
  if (errCode === "40029" || lowerMessage.includes("invalid code") || lowerMessage.includes("code无效")) {
    return {
      type: WECHAT_ERROR_TYPES.CODE_INVALID,
      level: "error",
      message: "登录凭证无效，请重新登录",
      originalMessage: message,
      errCode
    };
  }
  if (lowerMessage.includes("getuserprofile") || lowerMessage.includes("获取用户信息")) {
    return {
      type: WECHAT_ERROR_TYPES.PROFILE_FAILED,
      level: "warning",
      message: "获取用户信息失败，可稍后在个人中心完善",
      originalMessage: message,
      errCode
    };
  }
  if (lowerMessage.includes("chooseavatar") || lowerMessage.includes("头像")) {
    return {
      type: WECHAT_ERROR_TYPES.AVATAR_FAILED,
      level: "warning",
      message: "头像选择失败，请重试",
      originalMessage: message,
      errCode
    };
  }
  if (lowerMessage.includes("system") || lowerMessage.includes("系统") || errCode === "busy" || lowerMessage.includes("busy")) {
    return {
      type: WECHAT_ERROR_TYPES.SYSTEM_ERROR,
      level: "error",
      message: "系统繁忙，请稍后重试",
      originalMessage: message,
      errCode
    };
  }
  return {
    type: WECHAT_ERROR_TYPES.LOGIN_FAILED,
    level: "error",
    message: "登录失败，请重试",
    originalMessage: message,
    errCode
  };
}
function showWechatError(error, options = {}) {
  const errorAnalysis = analyzeWechatError(error);
  if (config_env.isDebug()) {
    common_vendor.index.__f__("error", "at utils/wechat-error-handler.js:182", "微信错误详情:", {
      type: errorAnalysis.type,
      level: errorAnalysis.level,
      message: errorAnalysis.message,
      original: errorAnalysis.originalMessage,
      errCode: errorAnalysis.errCode
    });
  }
  if (errorAnalysis.type === WECHAT_ERROR_TYPES.USER_CANCELLED) {
    return;
  }
  const defaultOptions = {
    duration: 2e3,
    icon: "none",
    ...options
  };
  common_vendor.index.showToast({
    title: errorAnalysis.message,
    icon: defaultOptions.icon,
    duration: defaultOptions.duration
  });
}
exports.showWechatError = showWechatError;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/wechat-error-handler.js.map
