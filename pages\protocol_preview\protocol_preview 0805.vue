<template>
	<view class="protocol-preview-container">
		<!-- 预览选项 -->
		<!-- <view class="preview-options">
			<button class="option-btn image-preview" @click="viewAsImages">
				<i class="fas fa-file-text"></i>
				<text>在线预览</text>
				<text class="option-desc">长图连续阅读</text>
			</button>
		</view> -->
		
		<!-- 长图预览模式 -->
		<view v-if="showImagePreview" class="long-image-preview">
			<view class="preview-header">
				<!-- <view class="header-left">
					<button class="back-btn" @click="closeImagePreview">
						<i class="fas fa-arrow-left"></i>
					</button>
					<text class="doc-title">调解协议书</text>
				</view> -->
				<view class="header-right">
					<text class="progress-text">{{Math.round(scrollProgress)}}%</text>
				</view>
			</view>
			
			<!-- 长图容器 -->
			<scroll-view 
				class="long-scroll-container"
				scroll-y="true"
				:scroll-top="scrollTop"
				@scroll="onScroll"
				enhanced
				:show-scrollbar="false">
				
				<view class="content-container">
					<!-- 加载状态 -->
					<view v-if="isLoading" class="loading-container">
						<view class="loading-icon">
							<i class="fas fa-spinner fa-spin"></i>
						</view>
						<text class="loading-text">正在加载协议内容...</text>
					</view>
					
					<!-- 协议图片列表 -->
					<view v-else class="images-container">
						<!-- SVG文件显示 -->
						<view
							v-for="(image, index) in svgImages"
							:key="`svg-${index}`"
							class="svg-container">
							<image
								:src="image.url"
								mode="widthFix"
								class="protocol-page svg-image"
								:class="{'first-page': index === 0}"
								@load="onImageLoad(image.originalIndex)"
								@error="onImageError(image.originalIndex)"
								lazy-load />
						</view>
						<!-- 普通图片显示 -->
						<image
							v-for="(image, index) in normalImages"
							:key="`img-${index}`"
							:src="image.url"
							mode="widthFix"
							class="protocol-page"
							:class="{'first-page': index === 0}"
							@load="onImageLoad(image.originalIndex)"
							@error="onImageError(image.originalIndex)"
							lazy-load />
					</view>
					
					<!-- 底部提示 -->
					<view class="bottom-tip">
						<view class="tip-line"></view>
						<text class="tip-text">协议内容已全部加载完成</text>
						<view class="action-buttons">
							<button class="download-btn-small" @click="downloadFile">
								<i class="fas fa-download"></i>
								下载协议
							</button>
						</view>
					</view>
				</view>
			</scroll-view>
			
			<!-- 阅读进度指示器 -->
			<view class="progress-indicator">
				<view class="progress-bar">
					<view class="progress-fill" :style="{width: scrollProgress + '%'}"></view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 接收的页面参数
const receivedFileUrl = ref('');
const receivedFileType = ref('');
const receivedFileName = ref('');
const receivedCaseNumber = ref('');

// 文件信息（默认值，如果没有传递参数则使用）
const fileUrl = ref('http://*************:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx');
const pdfImagesBaseUrl = ref('http://*************:10010/pdf_images/scheme/485/');
const fileType = ref('pdf'); // 文件类型：pdf, svg, docx, etc.
const fileName = ref('调解协议.pdf');

// 预览相关
const showImagePreview = ref(false);
const pdfImages = ref([]);
const isLoading = ref(false);
const loadedImages = ref(new Set());

// 滚动相关
const scrollTop = ref(0);
const scrollProgress = ref(0);
const containerHeight = ref(0);
const contentHeight = ref(0);

// 计算属性：分离SVG和普通图片
const svgImages = computed(() => {
	return pdfImages.value
		.map((image, index) => ({ ...image, originalIndex: index }))
		.filter(image => image.type === 'svg');
});

const normalImages = computed(() => {
	return pdfImages.value
		.map((image, index) => ({ ...image, originalIndex: index }))
		.filter(image => !image.type || image.type !== 'svg');
});

/**
 * 图片预览模式
 */
const viewAsImages = async () => {
	try {
		isLoading.value = true;
		showImagePreview.value = true;
		
		// 获取PDF转换的图片列表
		const images = await getPdfImages();
		if (images.length > 0) {
			pdfImages.value = images;
		} else {
			uni.showToast({
				title: '暂无可预览内容',
				icon: 'none'
			});
			closeImagePreview();
		}
	} catch (error) {
		console.error('加载图片预览失败:', error);
		uni.showToast({
			title: '加载失败，请稍后重试',
			icon: 'error'
		});
		closeImagePreview();
	} finally {
		isLoading.value = false;
	}
};

/**
 * 获取PDF转换的图片
 */
const getPdfImages = async () => {
	// 模拟加载延时
	await new Promise(resolve => setTimeout(resolve, 1500));
	
	// 模拟图片列表（实际应该从服务器获取）
	return [
		{ url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },
		{ url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },
		{ url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },
		{ url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },
		{ url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }
	];
};

/**
 * 滚动事件处理
 */
const onScroll = (e) => {
	const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;
	
	// 更新滚动位置
	scrollTop.value = currentScrollTop;
	
	// 计算阅读进度
	if (scrollHeight > clientHeight) {
		scrollProgress.value = (currentScrollTop / (scrollHeight - clientHeight)) * 100;
	}
};

/**
 * 图片加载成功
 */
const onImageLoad = (index) => {
	loadedImages.value.add(index);
	console.log(`图片 ${index + 1} 加载成功`);
};

/**
 * 图片加载失败
 */
const onImageError = (index) => {
	console.error(`图片 ${index + 1} 加载失败`);
	uni.showToast({
		title: `第${index + 1}页加载失败`,
		icon: 'none',
		duration: 2000
	});
};

/**
 * 关闭图片预览
 */
const closeImagePreview = () => {
	showImagePreview.value = false;
	scrollTop.value = 0;
	scrollProgress.value = 0;
	loadedImages.value.clear();
};

/**
 * SVG文件预览
 */
const showSvgPreview = () => {
	try {
		isLoading.value = true;
		showImagePreview.value = true;

		// 对于SVG文件，直接显示
		pdfImages.value = [{
			url: fileUrl.value,
			page: 1,
			type: 'svg'
		}];

		console.log('SVG文件预览准备完成:', fileUrl.value);
	} catch (error) {
		console.error('SVG预览失败:', error);
		uni.showToast({
			title: 'SVG预览失败',
			icon: 'error'
		});
	} finally {
		isLoading.value = false;
	}
};

/**
 * 检测文件类型
 */
const detectFileType = (url) => {
	if (!url) return 'unknown';

	const extension = url.split('.').pop()?.toLowerCase();
	switch (extension) {
		case 'svg':
			return 'svg';
		case 'pdf':
			return 'pdf';
		case 'docx':
		case 'doc':
			return 'docx';
		case 'jpg':
		case 'jpeg':
		case 'png':
		case 'gif':
			return 'image';
		default:
			return 'unknown';
	}
};

/**
 * 下载文件
 */
const downloadFile = () => {
	uni.showLoading({ title: '正在下载...' });

	uni.downloadFile({
		url: fileUrl.value,
		success: (res) => {
			if (res.statusCode === 200) {
				// 对于SVG文件，可以直接保存到相册（如果是图片格式）
				if (fileType.value === 'svg') {
					uni.saveImageToPhotosAlbum({
						filePath: res.tempFilePath,
						success: () => {
							uni.hideLoading();
							uni.showToast({
								title: '保存成功',
								icon: 'success'
							});
						},
						fail: (error) => {
							console.error('保存SVG失败:', error);
							// 如果保存失败，尝试用文档方式打开
							openDocument(res.tempFilePath);
						}
					});
				} else {
					// 其他文件类型用文档方式打开
					openDocument(res.tempFilePath);
				}
			}
		},
		fail: (error) => {
			uni.hideLoading();
			console.error('下载失败:', error);
			uni.showToast({
				title: '下载失败，请检查网络连接',
				icon: 'error'
			});
		}
	});
};

/**
 * 打开文档
 */
const openDocument = (filePath) => {
	uni.openDocument({
		filePath: filePath,
		showMenu: true,
		success: () => {
			uni.hideLoading();
			uni.showToast({
				title: '打开成功',
				icon: 'success'
			});
		},
		fail: (error) => {
			uni.hideLoading();
			console.error('打开文档失败:', error);
			uni.showToast({
				title: '打开失败，请检查是否安装相应的阅读器',
				icon: 'none',
				duration: 3000
			});
		}
	});
};

// 页面加载时获取参数
onLoad((options) => {
	console.log('预览页面参数:', options);

	// 获取文件URL参数
	if (options.fileUrl) {
		try {
			receivedFileUrl.value = decodeURIComponent(options.fileUrl);
			fileUrl.value = receivedFileUrl.value;
			console.log('接收到文件URL:', fileUrl.value);
		} catch (error) {
			console.error('fileUrl参数解码失败:', error);
			receivedFileUrl.value = options.fileUrl;
			fileUrl.value = options.fileUrl;
		}
	}

	// 获取文件类型参数
	if (options.fileType) {
		try {
			receivedFileType.value = decodeURIComponent(options.fileType);
			fileType.value = receivedFileType.value;
			console.log('接收到文件类型:', fileType.value);
		} catch (error) {
			console.error('fileType参数解码失败:', error);
			receivedFileType.value = options.fileType;
			fileType.value = options.fileType;
		}
	}

	// 获取文件名参数
	if (options.fileName) {
		try {
			receivedFileName.value = decodeURIComponent(options.fileName);
			fileName.value = receivedFileName.value;
			console.log('接收到文件名:', fileName.value);
		} catch (error) {
			console.error('fileName参数解码失败:', error);
			receivedFileName.value = options.fileName;
			fileName.value = options.fileName;
		}
	}

	// 获取案件号参数
	if (options.caseNumber) {
		try {
			receivedCaseNumber.value = decodeURIComponent(options.caseNumber);
			console.log('接收到案件号:', receivedCaseNumber.value);
			// 根据案件号构建PDF图片基础URL
			pdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${receivedCaseNumber.value}/`;
		} catch (error) {
			console.error('caseNumber参数解码失败:', error);
			receivedCaseNumber.value = options.caseNumber;
			pdfImagesBaseUrl.value = `http://*************:10010/pdf_images/scheme/${options.caseNumber}/`;
		}
	}
});

onMounted(() => {
	// 根据文件类型设置页面标题
	const title = fileName.value ? `预览 - ${fileName.value}` : '文件预览';
	uni.setNavigationBarTitle({ title });

	// 根据文件类型选择预览方式
	if (fileType.value === 'svg') {
		// SVG文件直接显示
		showSvgPreview();
	} else if (fileType.value === 'pdf' || fileType.value === 'docx') {
		// PDF或DOCX文件转图片预览
		viewAsImages();
	} else {
		// 其他文件类型尝试图片预览
		viewAsImages();
	}
});
</script>

<style lang="scss" scoped>
.protocol-preview-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx;
}

.preview-options {
	display: flex;
	gap: 30rpx;
	margin-bottom: 40rpx;
}

.option-btn {
	flex: 1;
	background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
	border: 2rpx solid #e2e8f0;
	border-radius: 16rpx;
	padding: 40rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	
	&:active {
		transform: scale(0.98);
	}
	
	.fas {
		font-size: 48rpx;
		color: #3b7eeb;
		margin-bottom: 16rpx;
	}
	
	text {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 8rpx;
	}
	
	.option-desc {
		font-size: 24rpx !important;
		color: #666 !important;
		font-weight: normal !important;
		margin-bottom: 0 !important;
	}
}

/* 长图预览样式 */
.long-image-preview {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: #fff;
	z-index: 9999;
}

.preview-header {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 88rpx;
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(10rpx);
	border-bottom: 2rpx solid #f0f0f0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	z-index: 10000;
}

/* .header-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.back-btn {
	background: transparent;
	border: none;
	color: #333;
	font-size: 32rpx;
	padding: 8rpx;
}

.doc-title {
	font-size: 30rpx;
	font-weight: bold;
	color: #333;
} */

.progress-text {
	font-size: 26rpx;
	color: #666;
}

.long-scroll-container {
	width: 100%;
	height: 100vh;
	padding-top: 88rpx;
}

.content-container {
	min-height: calc(100vh - 88rpx);
}

.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 60vh;
	
	.loading-icon {
		margin-bottom: 30rpx;
		
		.fas {
			font-size: 48rpx;
			color: #3b7eeb;
		}
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #666;
	}
}

.images-container {
	background-color: #fff;
}

.protocol-page {
	width: 100%;
	display: block;
	border-bottom: 2rpx solid #f0f0f0;

	&.first-page {
		border-top: none;
	}
}

/* SVG容器样式 */
.svg-container {
	width: 100%;
	background-color: #fff;
	border-bottom: 2rpx solid #f0f0f0;
}

.svg-image {
	width: 100%;
	height: auto;
	display: block;
}

.bottom-tip {
	padding: 60rpx 40rpx;
	text-align: center;
	background-color: #f9f9f9;
	
	.tip-line {
		width: 100rpx;
		height: 4rpx;
		background-color: #e0e0e0;
		margin: 0 auto 30rpx;
		border-radius: 2rpx;
	}
	
	.tip-text {
		display: block;
		font-size: 28rpx;
		color: #666;
		margin-bottom: 40rpx;
	}
}

.action-buttons {
	display: flex;
	justify-content: center;
}

.download-btn-small {
	background-color: #3b7eeb;
	color: #fff;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
	display: flex;
	align-items: center;
	gap: 12rpx;
	
	&:active {
		transform: scale(0.98);
	}
}

/* 进度指示器 */
.progress-indicator {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	height: 6rpx;
	background-color: rgba(0, 0, 0, 0.1);
	z-index: 10000;
}

.progress-bar {
	width: 100%;
	height: 100%;
	position: relative;
}

.progress-fill {
	height: 100%;
	background: linear-gradient(90deg, #3b7eeb 0%, #2c62c9 100%);
	transition: width 0.3s ease;
}
</style>