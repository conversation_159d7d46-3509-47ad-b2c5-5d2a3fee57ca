"use strict";
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _easycom_uni_easyinput2 = common_vendor.resolveComponent("uni-easyinput");
  _easycom_uni_easyinput2();
}
const _easycom_uni_easyinput = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.js";
if (!Math) {
  _easycom_uni_easyinput();
}
const _sfc_main = {
  __name: "mediation_complaint",
  setup(__props) {
    common_vendor.ref(0);
    const selectedType = common_vendor.ref(0);
    const selectedCategory = common_vendor.ref("");
    const formData = common_vendor.reactive({
      caseNumber: "",
      // 关联案件编号
      description: "",
      // 详细描述
      contactPhone: ""
      // 联系电话
    });
    const categoryConfig = {
      suggestion: [
        {
          value: "process_optimization",
          text: "流程优化建议",
          desc: "对调解流程改进的建议",
          icon: "fa-cog",
          color: "#52c41a"
        },
        {
          value: "improvement",
          text: "功能改进建议",
          desc: "对系统功能的优化建议",
          icon: "fa-mobile-alt",
          color: "#1890ff"
        },
        {
          value: "service_praise",
          text: "服务表扬",
          desc: "对优质服务的表扬",
          icon: "fa-heart",
          color: "#eb2f96"
        },
        {
          value: "other_suggestion",
          text: "其他建议",
          desc: "其他类型的意见建议",
          icon: "fa-plus-circle",
          color: "#999"
        }
      ],
      complaint: [
        {
          value: "service_attitude",
          text: "服务态度问题",
          desc: "工作人员态度不佳",
          icon: "fa-user-times",
          color: "#f5222d"
        },
        {
          value: "processing_time",
          text: "处理时间过长",
          desc: "调解处理效率低下",
          icon: "fa-clock",
          color: "#faad14"
        },
        {
          value: "plan_unreasonable",
          text: "方案不合理",
          desc: "调解方案存在问题",
          icon: "fa-ban",
          color: "#722ed1"
        },
        {
          value: "system_technology",
          text: "系统技术问题",
          desc: "页面异常、功能故障等",
          icon: "fa-bug",
          color: "#fa541c"
        },
        {
          value: "other_complaint",
          text: "其他投诉",
          desc: "其他类型的服务投诉",
          icon: "fa-ellipsis-h",
          color: "#999"
        }
      ]
    };
    const currentCategories = common_vendor.computed(() => {
      if (selectedType.value === 0) {
        return categoryConfig.suggestion;
      } else if (selectedType.value === 1) {
        return categoryConfig.complaint;
      }
      return [];
    });
    const currentPlaceholder = common_vendor.computed(() => {
      if (selectedType.value === 0) {
        return "请详细描述您的意见或问题，我们会认真对待每一条反馈...";
      } else if (selectedType.value === 1) {
        return "请详细描述您的投诉或问题，我们承诺对待每一条反馈...";
      }
      return "请详细描述您的反馈内容...";
    });
    const selectType = (typeIndex) => {
      common_vendor.index.__f__("log", "at pages/mediation_complaint/mediation_complaint.vue:288", "选择反馈类型:", typeIndex === 0 ? "意见建议" : "服务投诉");
      selectedType.value = typeIndex;
      selectedCategory.value = "";
    };
    const selectCategory = (category) => {
      common_vendor.index.__f__("log", "at pages/mediation_complaint/mediation_complaint.vue:296", "选择类别:", category.text);
      selectedCategory.value = category.value;
    };
    const validateForm = () => {
      if (selectedType.value === null) {
        common_vendor.index.showToast({
          title: "请选择反馈类型",
          icon: "none"
        });
        return false;
      }
      if (!selectedCategory.value) {
        common_vendor.index.showToast({
          title: "请选择具体类别",
          icon: "none"
        });
        return false;
      }
      if (!formData.description.trim()) {
        common_vendor.index.showToast({
          title: "请填写详细描述",
          icon: "none"
        });
        return false;
      }
      if (formData.description.trim().length < 10) {
        common_vendor.index.showToast({
          title: "描述内容至少10个字符",
          icon: "none"
        });
        return false;
      }
      if (formData.description.length > 500) {
        common_vendor.index.showToast({
          title: "描述内容不能超过500字符",
          icon: "none"
        });
        return false;
      }
      if (formData.contactPhone && formData.contactPhone.trim()) {
        const phoneReg = /^1[3-9]\d{9}$/;
        if (!phoneReg.test(formData.contactPhone)) {
          common_vendor.index.showToast({
            title: "请输入正确的手机号码",
            icon: "none"
          });
          return false;
        }
      }
      return true;
    };
    const handleSubmit = () => {
      common_vendor.index.__f__("log", "at pages/mediation_complaint/mediation_complaint.vue:371", "开始提交表单");
      if (!validateForm()) {
        return;
      }
      const submitData = {
        type: selectedType.value === 0 ? "suggestion" : "complaint",
        category: selectedCategory.value,
        caseNumber: formData.caseNumber.trim() || null,
        description: formData.description.trim(),
        contactPhone: formData.contactPhone.trim() || null,
        submitTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      common_vendor.index.__f__("log", "at pages/mediation_complaint/mediation_complaint.vue:388", "提交数据:", submitData);
      common_vendor.index.showLoading({
        title: "提交中...",
        mask: true
      });
      setTimeout(() => {
        common_vendor.index.hideLoading();
        const successTitle = selectedType.value === 0 ? "意见已提交" : "投诉已提交";
        common_vendor.index.showToast({
          title: successTitle,
          icon: "success",
          duration: 2e3,
          success: () => {
            setTimeout(() => {
              common_vendor.index.navigateBack();
            }, 1500);
          }
        });
      }, 1500);
    };
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/mediation_complaint/mediation_complaint.vue:419", "投诉建议页面已加载");
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: selectedType.value === 0 ? 1 : "",
        b: common_vendor.o(($event) => selectType(0)),
        c: selectedType.value === 1 ? 1 : "",
        d: common_vendor.o(($event) => selectType(1)),
        e: selectedType.value !== null
      }, selectedType.value !== null ? {
        f: common_vendor.o(($event) => formData.caseNumber = $event),
        g: common_vendor.p({
          prefixIcon: "search",
          placeholder: "请输入调解案件编号",
          modelValue: formData.caseNumber
        }),
        h: common_vendor.f(currentCategories.value, (category, index, i0) => {
          return common_vendor.e({
            a: common_vendor.n(category.icon),
            b: category.color,
            c: common_vendor.t(category.text),
            d: common_vendor.t(category.desc),
            e: selectedCategory.value === category.value
          }, selectedCategory.value === category.value ? {} : {}, {
            f: selectedCategory.value === category.value ? 1 : "",
            g: category.value,
            h: selectedCategory.value === category.value ? 1 : "",
            i: common_vendor.o(($event) => selectCategory(category), category.value)
          });
        }),
        i: common_vendor.o(($event) => formData.description = $event),
        j: common_vendor.p({
          type: "textarea",
          placeholder: currentPlaceholder.value,
          autoHeight: true,
          modelValue: formData.description
        }),
        k: common_vendor.t(formData.description.length),
        l: common_vendor.o(($event) => formData.contactPhone = $event),
        m: common_vendor.p({
          prefixIcon: "phone",
          placeholder: "请输入您的手机号码",
          type: "number",
          maxlength: "11",
          modelValue: formData.contactPhone
        }),
        n: common_vendor.o(handleSubmit)
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-0f5f266a"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mediation_complaint/mediation_complaint.js.map
