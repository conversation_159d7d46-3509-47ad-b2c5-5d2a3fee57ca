"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const utils_appInitializer = require("./utils/app-initializer.js");
const config_env = require("./config/env.js");
if (!Math) {
  "./pages/index/index.js";
  "./pages/mine/mine.js";
  "./pages/mediation_query/mediation_query.js";
  "./pages/mediation_complaint/mediation_complaint.js";
  "./pages/real_case/real_case.js";
  "./pages/case_detail/case_detail.js";
  "./pages/work_order_detail/work_order_detail.js";
  "./pages/solution_confirm/solution_confirm.js";
  "./pages/case_completed/case_completed.js";
  "./pages/protocol_preview/protocol_preview.js";
  "./pages/contact_information/contact_information.js";
  "./pages/agreement_signing/agreement_signing.js";
  "./pages/agreement_notarization/agreement_notarization.js";
  "./pages/login/login.js";
  "./pages/auth/auth.js";
  "./pages/user-info/user-info.js";
  "./pages/user_agreement/user_agreement.js";
  "./pages/privacy_policy/privacy_policy.js";
  "./pages/webview/webview.js";
}
const _sfc_main = {
  __name: "App",
  setup(__props) {
    const globalData = {
      userInfo: null,
      systemInfo: null,
      appInitialized: false
    };
    common_vendor.index.getApp = () => ({
      globalData
    });
    async function initApp() {
      try {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at App.vue:22", "开始应用初始化...");
        }
        try {
          const systemInfo = common_vendor.index.getSystemInfoSync();
          globalData.systemInfo = systemInfo;
          if (config_env.isDebug()) {
            common_vendor.index.__f__("log", "at App.vue:31", "系统信息:", systemInfo);
          }
        } catch (e) {
          common_vendor.index.__f__("error", "at App.vue:34", "获取系统信息失败:", e);
        }
        const result = await utils_appInitializer.initApp();
        if (result.success) {
          globalData.appInitialized = true;
          if (config_env.isDebug()) {
            common_vendor.index.__f__("log", "at App.vue:44", "应用初始化成功");
          }
        } else {
          common_vendor.index.__f__("error", "at App.vue:47", "应用初始化失败:", result.error);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:50", "应用初始化异常:", error);
      }
    }
    common_vendor.onLaunch(() => {
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at App.vue:57", "App Launch - 不良资产系统");
      }
      initApp();
    });
    common_vendor.onShow(() => {
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at App.vue:66", "App Show");
      }
    });
    common_vendor.onHide(() => {
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at App.vue:74", "App Hide");
      }
    });
    if (typeof common_vendor.index !== "undefined" && common_vendor.index.onAppDestroy) {
      common_vendor.index.onAppDestroy(() => {
        if (config_env.isDebug()) {
          common_vendor.index.__f__("log", "at App.vue:84", "App Destroy");
        }
        utils_appInitializer.destroyApp();
      });
    }
    return () => {
    };
  }
};
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  return {
    app
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
