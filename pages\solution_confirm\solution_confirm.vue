<template>
	<view class="solution-confirm-container">
		<!-- 基本信息 -->
		<view class="work-order-card">
			<view class="work-order-header">
				<view class="work-order-title">
					<text>调解案件号: </text>
					<text class="work-order-id">{{basicInfo.case_number}}</text>
				</view>
				<view class="work-order-status">
					<text class="status-label" :class="{'status-processing': (basicInfo.case_status_cn || workOrderData.case_status_cn) === '进行中'}">{{basicInfo.case_status_cn || workOrderData.case_status_cn}}</text>
				</view>
			</view>
			<view class="work-order-date">发起日期: {{basicInfo.initiate_date || workOrderData.createDate}}</view>
		</view>
		
		<!-- 进度条 -->
		<view class="progress-bar">
			<view class="progress-steps">
				<view class="progress-step completed">
					<view class="step-circle">1</view>
					<view class="step-line completed"></view>
					<view class="step-label">调解确认</view>
				</view>
				<view class="progress-step active">
					<view class="step-circle">2</view>
					<view class="step-line"></view>
					<view class="step-label">方案确认</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">3</view>
					<view class="step-line"></view>
					<view class="step-label">协议签署</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">4</view>
					<view class="step-label">完成</view>
				</view>
			</view>
		</view>
		

		<!-- 工作方案详情区域 -->
		<!-- <view v-if="workPlanData.length > 0" class="work-plan-section">
			<view class="section-title">工作方案详情</view>
			<view v-for="plan in workPlanData" :key="plan.plan_id" class="plan-detail-card">
				<view class="plan-header">
					<text class="plan-name">{{ plan.plan_name }}</text>
				</view>
				<view class="plan-content">
					<view v-for="config in plan.plan_config" :key="config.id" class="config-item">
						<view class="config-title">{{ config.title }}</view>
						<view class="config-value">{{ config.value }}</view>
						<view v-if="config.expression" class="config-expression">
							<text class="expression-label">计算公式:</text>
							<text class="expression-text">{{ config.expression }}</text>
						</view>
					</view>
				</view>
			</view>
		</view> -->
		<!-- 方案列表 -->
		<view class="solutions-container">
			<!-- 显示workPlanData中的方案 -->
			<view v-if="workPlanData.length > 0">
				<view
					v-for="(plan, planIndex) in workPlanData"
					:key="plan.plan_id"
					class="solution-card"
					:class="{'selected': selectedPlanId === plan.plan_id}"
					@click="selectPlan(plan.plan_id)"
				>
					<view class="solution-header">
						<view class="solution-title-wrap">
							<text class="solution-title">{{plan.plan_name}}</text>
						</view>
						<view class="solution-select">
							<view class="radio-button" :class="{'selected': selectedPlanId === plan.plan_id}">
								<view class="radio-inner" v-if="selectedPlanId === plan.plan_id"></view>
							</view>
						</view>
					</view>

					<view class="solution-content">
						<view v-for="config in plan.plan_config" :key="config.id" class="config-detail">
							<view class="solution-item">
								<text class="solution-label">{{config.title}}</text>
								<text class="solution-value">{{config.value}}</text>
							</view>
							<!--<view v-if="config.expression" class="config-formula">
								<text class="formula-label">计算公式:</text>
								<text class="formula-text">{{config.expression}}</text>
							</view> -->
						</view>
					</view>

					<view class="solution-footer">
						<text class="view-detail" @click.stop="viewPlanDetail(plan)">查看详情</text>
					</view>
				</view>
			</view>

			<!-- 显示默认方案（当workPlanData为空时） 
			<view v-else>
				<view
					v-for="(solution, index) in solutions"
					:key="solution.id"
					class="solution-card"
					:class="{'selected': selectedSolutionIndex === index}"
					@click="selectSolution(index)"
				>
					<view class="solution-header">
						<view class="solution-title-wrap">
							<text class="solution-title">{{solution.title}}</text>
						</view>
						<view class="solution-select">
							<view class="radio-button" :class="{'selected': selectedSolutionIndex === index}">
								<view class="radio-inner" v-if="selectedSolutionIndex === index"></view>
							</view>
						</view>
					</view>

					<view class="solution-content">
						<text class="solution-label">方案内容</text>
						<text>{{solution.content}}</text>
					</view>

					<view class="solution-details">
						<view class="solution-item">
							<text class="solution-label">还款总额</text>
							<text class="solution-value">¥{{solution.totalAmount.toFixed(2)}}</text>
						</view>
						<view class="solution-item">
							<text class="solution-label">月还款额</text>
							<text class="solution-value">¥{{solution.monthlyPayment.toFixed(2)}}</text>
						</view>
					</view>

					<view class="solution-footer">
						<text class="discount-amount">减免金额: ¥{{solution.discountAmount.toFixed(2)}}</text>
						<text class="view-detail" @click.stop="viewSolutionDetail(solution)">查看详情</text>
					</view>
				</view>
			</view>-->

			<!-- 空状态提示 -->
			<view v-if="workPlanData.length === 0" class="empty-state">
				<text class="empty-text">暂无方案数据</text>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="action-buttons">
			<button class="confirm-button" @click="handleConfirm">
				<text v-if="workPlanData.length > 0 && selectedPlanId">
					确认选择{{getSelectedPlan()?.plan_name || '方案'}}
				</text>
				<text v-else-if="solutions.length > 0">
					确认选择{{solutions[selectedSolutionIndex]?.title || '方案'}}
				</text>
				<text v-else>
					确认选择
				</text>
			</button>
		</view>

		<!-- 添加联系客服悬浮按钮 -->
		<!-- <contact-fab></contact-fab> -->
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { api } from '@/utils/api.js';
// import ContactFab from '@/components/contact-fab/contact-fab.vue';

/**
 * 调解方案确认页面
 *
 * 此页面用于展示和选择多个调解方案，支持方案对比和确认功能
 * 用户可以查看方案详情，选择方案并确认选择
 */

// 接收页面参数 - 调解案件编号
const caseNumber = ref('');

// 基本数据
const workOrderData = ref({});

// 页面参数相关
const basicInfo = ref({       // 基本信息
  case_number: '',
  initiate_date: '',
  case_status_cn: '',
  mediation_progress: ''
});
const workPlanData = ref([]);      // 工作方案详情数据

// 已完成工单数据
const completedWorkOrderData = ref({
  protocol_notarization_status: '',  // 协议公证状态
  mediation_info: {},                 // 调解信息
  repayment_plan: {}                  // 还款方案
});

// 选中的方案索引（默认选中方案一）
const selectedSolutionIndex = ref(0);

// 选中的方案ID（单选）
const selectedPlanId = ref('');

// 调解方案数组 - 包含多个方案
const solutions = ref([]);

// 计算当前选中的方案
const selectedSolution = computed(() => {
	return solutions.value[selectedSolutionIndex.value];
});

// 获取选中的方案数据
const getSelectedPlan = () => {
	return workPlanData.value.find(plan => plan.plan_id === selectedPlanId.value);
};

// 页面加载时处理URL参数
onLoad((options) => {
  console.log("方案确认页面加载，参数:", options);

  // 处理URL参数
  if (options && options.case_number) {
    handleUrlParams(options);
  }
});

// 处理URL参数
const handleUrlParams = async (options) => {
  console.log("处理URL参数:", options);

  const { case_number, initiate_date, case_status_cn, mediation_progress } = options;

  // 检查是否有完整参数
  const hasCompleteParams = case_number && initiate_date && case_status_cn && mediation_progress;

  try {
    uni.showLoading({ title: "加载中..." });

    if (hasCompleteParams) {
      // 有完整参数，直接显示基本信息
      console.log("检测到完整参数，直接显示基本信息");
      basicInfo.value.case_number = decodeURIComponent(case_number);
      basicInfo.value.initiate_date = decodeURIComponent(initiate_date);
      basicInfo.value.case_status_cn = decodeURIComponent(case_status_cn);
      basicInfo.value.mediation_progress = decodeURIComponent(mediation_progress);

      // 设置caseNumber用于后续API调用
      caseNumber.value = basicInfo.value.case_number;

      // 并行获取数据以提高加载速度
      await Promise.all([
        fetchWorkPlanDetailWithoutLoading(basicInfo.value.case_number),
        fetchCompletedWorkOrderWithoutLoading(basicInfo.value.case_number)
      ]);
    } else if (case_number) {
      // 仅有案件编号，调用接口获取详细信息
      console.log("仅有案件编号，调用接口获取详细信息");
      const decodedCaseNumber = decodeURIComponent(case_number);
      caseNumber.value = decodedCaseNumber;

      // 先获取案件基本信息
      await fetchMediationSingleDetailWithoutLoading(decodedCaseNumber);

      // 然后并行获取其他数据
      await Promise.all([
        fetchWorkPlanDetailWithoutLoading(decodedCaseNumber),
        fetchCompletedWorkOrderWithoutLoading(decodedCaseNumber)
      ]);
    }
  } catch (error) {
    console.error("处理URL参数失败:", error);
    uni.showToast({
      title: "页面加载失败",
      icon: "none",
      duration: 2000,
    });
  } finally {
    uni.hideLoading();
  }
};

// 获取单条调解数据基本信息
const fetchMediationSingleDetail = async (caseNumber) => {
  try {
    uni.showLoading({ title: "加载案件信息..." });

    const result = await api.mediationQuery.getSingleDetail(caseNumber);

    if (result.state === "success" && result.data) {
      const data = result.data;
      basicInfo.value.case_number = data.case_number || caseNumber;
      basicInfo.value.initiate_date = data.initiate_date || '';
      basicInfo.value.case_status_cn = data.case_status_cn || '';
      basicInfo.value.mediation_progress = data.mediation_progress || '';
      console.log("案件基本信息获取成功:", basicInfo.value);
    } else {
      console.log("获取案件基本信息失败:", result.msg);
      uni.showToast({
        title: result.msg || "获取案件信息失败",
        icon: "none",
        duration: 2000,
      });
    }
  } catch (error) {
    console.error("获取案件详情失败:", error);
    uni.showToast({
      title: "获取案件信息失败",
      icon: "none",
      duration: 2000,
    });
  } finally {
    uni.hideLoading();
  }
};

// 获取方案详情
const fetchWorkPlanDetail = async (caseNumber) => {
	try {
		uni.showLoading({ title: "加载方案详情..." });

		const result = await api.solution.getPlanDetail(caseNumber);

		if (result.state === "success" && result.data) {
			workPlanData.value = result.data;
			console.log("方案详情获取成功:", result.data);

			// 默认选择第一个方案
			if (result.data.length > 0) {
				selectedPlanId.value = result.data[0].plan_id;
				console.log("默认选择第一个方案:", result.data[0].plan_name);
			}
		} else {
			console.log("工作方案详情获取失败:", result.msg);
			workPlanData.value = [];
			uni.showToast({
				title: result.msg || "获取方案详情失败",
				icon: "none",
				duration: 2000,
			});
		}
	} catch (error) {
		console.error("获取方案详情失败:", error);
		workPlanData.value = [];
		uni.showToast({
			title: "获取方案详情失败",
			icon: "none",
			duration: 2000,
		});
	} finally {
		uni.hideLoading();
	}
};

// 获取已完成工单信息（协议公证状态、调解信息、还款方案）
const fetchCompletedWorkOrder = async (caseNumber) => {
	try {
		uni.showLoading({ title: "加载工单信息..." });

		const result = await api.workOrder.getCompletedWorkOrder(caseNumber);

		if (result.state === "success" && result.data) {
			completedWorkOrderData.value = {
				protocol_notarization_status: result.data.protocol_notarization_status || '',
				mediation_info: result.data.mediation_info || {},
				repayment_plan: result.data.repayment_plan || {}
			};
			console.log("已完成工单信息获取成功:", completedWorkOrderData.value);
		} else {
			console.log("获取已完成工单信息失败:", result.msg);
			// 工单信息获取失败不显示错误提示，因为可能是正常情况（工单未完成）
		}
	} catch (error) {
		console.error("获取已完成工单信息失败:", error);
		// 工单信息获取失败不显示错误提示，因为可能是正常情况（工单未完成）
	} finally {
		uni.hideLoading();
	}
};

// 不带加载提示的版本 - 用于并行调用
const fetchMediationSingleDetailWithoutLoading = async (caseNumber) => {
  try {
    const result = await api.mediationQuery.getSingleDetail(caseNumber);

    if (result.state === "success" && result.data) {
      const data = result.data;
      basicInfo.value.case_number = data.case_number || caseNumber;
      basicInfo.value.initiate_date = data.initiate_date || '';
      basicInfo.value.case_status_cn = data.case_status_cn || '';
      basicInfo.value.mediation_progress = data.mediation_progress || '';
      console.log("案件基本信息获取成功:", basicInfo.value);
    } else {
      console.log("获取案件基本信息失败:", result.msg);
      throw new Error(result.msg || "获取案件信息失败");
    }
  } catch (error) {
    console.error("获取案件详情失败:", error);
    throw error;
  }
};

const fetchWorkPlanDetailWithoutLoading = async (caseNumber) => {
	try {
		const result = await api.solution.getPlanDetail(caseNumber);

		if (result.state === "success" && result.data) {
			workPlanData.value = result.data;
			console.log("方案详情获取成功:", result.data);

			// 默认选择第一个方案
			if (result.data.length > 0) {
				selectedPlanId.value = result.data[0].plan_id;
				console.log("默认选择第一个方案:", result.data[0].plan_name);
			}
		} else {
			console.log("工作方案详情获取失败:", result.msg);
			workPlanData.value = [];
		}
	} catch (error) {
		console.error("获取方案详情失败:", error);
		workPlanData.value = [];
	}
};

const fetchCompletedWorkOrderWithoutLoading = async (caseNumber) => {
	try {
		const result = await api.workOrder.getCompletedWorkOrder(caseNumber);

		if (result.state === "success" && result.data) {
			completedWorkOrderData.value = {
				protocol_notarization_status: result.data.protocol_notarization_status || '',
				mediation_info: result.data.mediation_info || {},
				repayment_plan: result.data.repayment_plan || {}
			};
			console.log("已完成工单信息获取成功:", completedWorkOrderData.value);
		} else {
			console.log("获取已完成工单信息失败:", result.msg);
		}
	} catch (error) {
		console.error("获取已完成工单信息失败:", error);
	}
};

// 生命周期钩子 - 页面加载时执行
onMounted(() => {
	console.log('使用默认方案数据');
});

/**
 * 获取方案详情数据
 * @param {String} id - ID
 */
const fetchSolutionDetail = (id) => {
	if (id) {
		// 使用API获取数据
		uni.showLoading({
			title: '加载中...'
		});

		api.solution.getDetail(id)
			.then(res => {
				uni.hideLoading();
				if (res.code === 0) {
					// 设置基本信息
					workOrderData.value = res.data.workOrder;

					// 如果API返回了多个方案，则更新方案数组
					if (res.data.solutions && res.data.solutions.length > 0) {
						solutions.value = res.data.solutions;
					}
				} else {
					uni.showToast({
						title: res.msg || '获取方案详情失败',
						icon: 'none'
					});
				}
			})
			.catch(err => {
				uni.hideLoading();
				console.error('获取方案详情失败', err);
				uni.showToast({
					title: '获取方案详情失败',
					icon: 'none'
				});
			});
	} else {
		// 使用模拟数据
		setTimeout(() => {
			console.log('调解方案数据已加载（模拟）');
		}, 500);
	}
};

/**
 * 选择方案
 * @param {Number} index - 方案索引
 */
const selectSolution = (index) => {
	selectedSolutionIndex.value = index;
	console.log('选择了方案:', solutions.value[index].title);
};

/**
 * 选择工作方案（单选）
 * @param {String} planId - 方案ID
 */
const selectPlan = (planId) => {
	selectedPlanId.value = planId;
	console.log('当前选中的方案ID:', selectedPlanId.value);
};

/**
 * 查看工作方案详情
 * @param {Object} plan - 方案对象
 */
const viewPlanDetail = (plan) => {
	const configDetails = plan.plan_config.map(config => {
		let detail = `${config.title}: ${config.value}`;
		if (config.expression) {
			detail += `\n计算公式: ${config.expression}`;
		}
		return detail;
	}).join('\n\n');

	uni.showModal({
		title: `${plan.plan_name}详情`,
		content: configDetails,
		showCancel: false
	});
};

/**
 * 查看方案详情
 * @param {Object} solution - 方案对象
 */
const viewSolutionDetail = (solution) => {
	/* uni.showModal({
		title: `${solution.title}详情`,
		content: `${solution.content}\n\n还款总额: ¥${solution.totalAmount.toFixed(2)}\n月还款额: ¥${solution.monthlyPayment.toFixed(2)}\n还款期数: ${solution.periods}期\n减免金额: ¥${solution.discountAmount.toFixed(2)}`,
		showCancel: false
	}); */
};

/**
 * 处理确认方案
 * 确认用户选择的方案并提交到服务器
 */
const handleConfirm = () => {
	// 判断是工作方案还是默认方案
	if (workPlanData.value.length > 0) {
		// 处理工作方案确认
		if (!selectedPlanId.value) {
			uni.showToast({
				title: '请选择一个方案',
				icon: 'none'
			});
			return;
		}

		const selectedPlan = getSelectedPlan();
		const planName = selectedPlan?.plan_name || '未知方案';

		// 显示确认对话框
		uni.showModal({
			title: '确认方案',
			content: `您确定要选择"${planName}"吗？`,
			success: (res) => {
				if (res.confirm) {
					// 用户点击确定
					uni.showLoading({
						title: '处理中...'
					});
					console.log(selectedPlan,'===selectedPlan')
					// 调用API确认方案
					if (caseNumber.value) {
						api.solution.confirmSolution(caseNumber.value, {
							mediation_plan : selectedPlanId.value,
						})
							.then(res => {
								uni.hideLoading();
								if (res.state === "success") {
									uni.showToast({
										title: res.msg || '方案确认成功',
										icon: 'success',
										duration: 1500
									});

									// 成功后跳转到协议签署页面
									setTimeout(() => {
										uni.navigateTo({
											url: '/pages/agreement_signing/agreement_signing',
											success: () => {
												console.log('跳转到协议签署页面');
											},
											fail: (err) => {
												console.error('跳转失败', err);
												uni.showToast({
													title: '跳转失败',
													icon: 'none'
												});
											}
										});
									}, 2000);
								} else {
									uni.showToast({
										title: res.msg || '操作失败',
										icon: 'none'
									});
								}
							})
							.catch(err => {
								uni.hideLoading();
								console.error('确认方案失败', err);
								uni.showToast({
									title: '确认方案失败',
									icon: 'none'
								});
							});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '缺少调解方案编号',
							icon: 'none'
						});
					}
				}
			}
		});
	} else {
		// 处理默认方案确认
		const selectedSolution = solutions.value[selectedSolutionIndex.value];

		if (!selectedSolution) {
			uni.showToast({
				title: '请选择一个方案',
				icon: 'none'
			});
			return;
		}

		// 显示确认对话框
		uni.showModal({
			title: '确认方案',
			content: `您确定要选择"${selectedSolution.title}"吗？`,
			success: (res) => {
				if (res.confirm) {
					// 用户点击确定
					uni.showLoading({
						title: '处理中...'
					});

					// 调用API确认方案
					if (caseNumber.value) {
						api.solution.confirmSolution(caseNumber.value, { solutionId: selectedSolution.id })
							.then(res => {
								uni.hideLoading();
								if (res.state === "success") {
									uni.showToast({
										title: res.msg || '方案确认成功',
										icon: 'success',
										duration: 1500
									});

									// 成功后跳转到协议签署页面
									setTimeout(() => {
										uni.navigateTo({
											url: '/pages/agreement_signing/agreement_signing',
											success: () => {
												console.log('跳转到协议签署页面');
											},
											fail: (err) => {
												console.error('跳转失败', err);
												uni.showToast({
													title: '跳转失败',
													icon: 'none'
												});
											}
										});
									}, 2000);
								} else {
									uni.showToast({
										title: res.msg || '操作失败',
										icon: 'none'
									});
								}
							})
							.catch(err => {
								uni.hideLoading();
								console.error('确认方案失败', err);
								uni.showToast({
									title: '确认方案失败',
									icon: 'none'
								});
							});
					} else {
						uni.hideLoading();
						uni.showToast({
							title: '缺少调解方案编号',
							icon: 'none'
						});
					}
				}
			}
		});
	}
};

</script>

<style lang="scss" scoped>
:root {
	--primary-color: #3b7eeb;
}
.solution-confirm-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 卡片样式 */
.work-order-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.work-order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.work-order-title {
	font-size: 29rpx;
	color: #333;
	font-weight: bold;
}

.work-order-status {
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
}

.status-label {
	font-size: 26rpx;
	padding: 8rpx 20rpx;
	border-radius: 30rpx;
	background-color: #f0f0f0;
	color: #fff;
}

.status-processing {
	background-color: #1890ff;
	color: #fff;
}

.work-order-date {
	font-size: 28rpx;
	color: #666;
}

/* 工作方案详情样式 */
.work-plan-section {
	margin: 30rpx 0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	padding: 0 30rpx;
}

.plan-detail-card {
	background-color: #fff;
	border-radius: 16rpx;
	margin: 0 30rpx 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.plan-header {
	padding: 25rpx 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 16rpx 16rpx 0 0;
}

.plan-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #fff;
}

.plan-content {
	padding: 20rpx 30rpx 30rpx;
}

.config-item {
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f5f5f5;
}

.config-item:last-child {
	border-bottom: none;
}

.config-title {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 10rpx;
}

.config-value {
	font-size: 32rpx;
	color: #007aff;
	font-weight: 700;
	margin-bottom: 8rpx;
}

.config-expression {
	display: flex;
	align-items: center;
	margin-top: 8rpx;
}

.expression-label {
	font-size: 24rpx;
	color: #999;
	margin-right: 10rpx;
}

.expression-text {
	font-size: 24rpx;
	color: #666;
	font-family: 'Courier New', monospace;
	background-color: #f8f9fa;
	padding: 4rpx 8rpx;
	border-radius: 6rpx;
}

/* 进度条样式 */
/* .progress-bar {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
} */

.progress-steps {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
}

.step-circle {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background-color: #e0e0e0;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 28rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
	position: relative;
	z-index: 2;
}

.step-line {
	position: absolute;
	top: 30rpx;
	left: 50%;
	right: -50%;
	height: 4rpx;
	background-color: #e0e0e0;
	z-index: 1;
}

.progress-step:last-child .step-line {
	display: none;
}

.step-label {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

.progress-step.active .step-circle {
	background-color: #2979ff;
}

.progress-step.active .step-label {
	color: #2979ff;
	font-weight: bold;
}

.progress-step.completed .step-circle {
	background-color: #2979ff;
}

.step-line.completed {
	background-color: #2979ff;
}

.progress-step.completed .step-label {
	color: #2979ff;
}

/* 方案容器 */
.solutions-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 方案卡片样式 */
.solution-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	border: 2rpx solid #eee;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
	margin-bottom: 30rpx;
}

.solution-card.selected {
	border-color: #2979ff;
	box-shadow: 0 4rpx 20rpx rgba(41, 121, 255, 0.1);
}

.solution-card:last-child {
	margin-bottom: 0;
}

.solution-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #eee;
}

.solution-title-wrap {
	display: flex;
	align-items: center;
}

.solution-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.solution-tag {
	font-size: 24rpx;
	color: #2979ff;
	margin-left: 10rpx;
}

.solution-select {
	display: flex;
	align-items: center;
}

.radio-button {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	border: 2rpx solid rgb(221, 221, 221);
	border-image: initial;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.radio-button.selected {
	border-color: #3b7eeb;
	background-color: var(--primary-color);
}

.radio-inner {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background-color: #fff;
}

.solution-content {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 30rpx;
	line-height: 1.5;
	gap: 16rpx;
	display: flex;
    flex-direction: column;
}

.solution-details {
	margin-bottom: 20rpx;
}

.solution-item {
	display: flex;
	flex-direction: column;
	padding: 10rpx 0;
	gap: 8rpx;
}

.solution-label {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.solution-value {
	font-size: 32rpx;
	color: #333;
}

.solution-footer {
	display: flex;
	justify-content: space-between;
	padding-top: 20rpx;
	border-top: 1px solid #eee;
}

/* 配置项详情样式 */
/* .config-detail {
	margin-bottom: 20rpx;
}
 */
.config-detail:last-child {
	margin-bottom: 0;
}

.config-formula {
	margin-top: 8rpx;
	padding: 8rpx 12rpx;
	background-color: #f8f9fa;
	border-radius: 6rpx;
	border-left: 3rpx solid #007aff;
}

.formula-label {
	font-size: 24rpx;
	color: #666;
	margin-right: 8rpx;
}

.formula-text {
	font-size: 24rpx;
	color: #333;
	font-family: 'Courier New', monospace;
}

/* 空状态样式 */
.empty-state {
	text-align: center;
	padding: 100rpx 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #999;
}

.discount-amount {
	font-size: 24rpx;
	color: #999;
}

.view-detail {
	font-size: 26rpx;
	color: #2979ff;
	display: flex;
	align-items: center;
}
.view-detail::before {
	content: 'i';
	display: inline-block;
	width: 30rpx;
	height: 30rpx;
	line-height: 30rpx;
	text-align: center;
	font-size: 22rpx;
	font-style: italic;
	font-weight: bold;
	border-radius: 50%;
	background-color: #2979ff;
	color: #fff;
	margin-right: 10rpx;
}

/* 联系客服区域 */
.contact-section {
	display: flex;
	gap: 30rpx;
}

.contact-button {
	flex: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	font-size: 28rpx;
}

.contact-button.phone {
	color: #2979ff;
}

.contact-button.wechat {
	color: #07c160;
}

.contact-button .fa,
.contact-button .fab {
	font-size: 32rpx;
}

/* 底部按钮 */
/* .action-buttons {
	padding: 20rpx 0;
} */

.confirm-button {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	border-radius: 16rpx;
	font-size: 32rpx;
	background-color: #2979ff;
	color: #fff;
}
</style> 