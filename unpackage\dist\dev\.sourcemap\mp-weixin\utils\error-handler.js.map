{"version": 3, "file": "error-handler.js", "sources": ["utils/error-handler.js"], "sourcesContent": ["// 错误处理增强工具\nimport { isDebug } from '@/config/env.js';\n\n/**\n * 错误类型枚举\n */\nexport const ERROR_TYPES = {\n  NETWORK: 'network',           // 网络错误\n  TIMEOUT: 'timeout',           // 超时错误\n  AUTHORIZATION: 'authorization', // 授权错误\n  VALIDATION: 'validation',     // 验证错误\n  SERVER: 'server',             // 服务器错误\n  USER_CANCELLED: 'user_cancelled', // 用户取消\n  SYSTEM: 'system',             // 系统错误\n  UNKNOWN: 'unknown'            // 未知错误\n};\n\n/**\n * 错误级别枚举\n */\nexport const ERROR_LEVELS = {\n  INFO: 'info',       // 信息级别\n  WARNING: 'warning', // 警告级别\n  ERROR: 'error',     // 错误级别\n  CRITICAL: 'critical' // 严重错误级别\n};\n\n/**\n * 错误消息映射\n */\nconst ERROR_MESSAGES = {\n  [ERROR_TYPES.NETWORK]: {\n    title: '网络连接失败',\n    message: '请检查网络连接后重试',\n    icon: 'none'\n  },\n  [ERROR_TYPES.TIMEOUT]: {\n    title: '请求超时',\n    message: '网络响应较慢，请稍后重试',\n    icon: 'none'\n  },\n  [ERROR_TYPES.AUTHORIZATION]: {\n    title: '授权失败',\n    message: '请重新授权后继续使用',\n    icon: 'none'\n  },\n  [ERROR_TYPES.VALIDATION]: {\n    title: '输入有误',\n    message: '请检查输入信息是否正确',\n    icon: 'none'\n  },\n  [ERROR_TYPES.SERVER]: {\n    title: '服务器错误',\n    message: '服务器暂时无法响应，请稍后重试',\n    icon: 'none'\n  },\n  [ERROR_TYPES.USER_CANCELLED]: {\n    title: '操作已取消',\n    message: '您已取消当前操作',\n    icon: 'none'\n  },\n  [ERROR_TYPES.SYSTEM]: {\n    title: '系统错误',\n    message: '系统出现异常，请重启应用后重试',\n    icon: 'none'\n  },\n  [ERROR_TYPES.UNKNOWN]: {\n    title: '未知错误',\n    message: '发生了未知错误，请重试',\n    icon: 'none'\n  }\n};\n\n/**\n * 错误分析器 - 根据错误信息判断错误类型\n * @param {Error|string} error 错误对象或错误消息\n * @returns {Object} 错误分析结果\n */\nexport function analyzeError(error) {\n  let message = '';\n  let stack = '';\n  \n  if (error instanceof Error) {\n    message = error.message || '';\n    stack = error.stack || '';\n  } else if (typeof error === 'string') {\n    message = error;\n  } else if (error && error.errMsg) {\n    message = error.errMsg;\n  } else {\n    message = String(error || '');\n  }\n  \n  const lowerMessage = message.toLowerCase();\n  \n  // 网络相关错误\n  if (lowerMessage.includes('network') || \n      lowerMessage.includes('网络') ||\n      lowerMessage.includes('connection') ||\n      lowerMessage.includes('连接') ||\n      lowerMessage.includes('request:fail')) {\n    return {\n      type: ERROR_TYPES.NETWORK,\n      level: ERROR_LEVELS.WARNING,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 超时错误\n  if (lowerMessage.includes('timeout') || \n      lowerMessage.includes('超时') ||\n      lowerMessage.includes('time out')) {\n    return {\n      type: ERROR_TYPES.TIMEOUT,\n      level: ERROR_LEVELS.WARNING,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 授权相关错误 - 更精确的判断\n  if ((lowerMessage.includes('auth') && !lowerMessage.includes('fail')) ||\n      (lowerMessage.includes('授权') && lowerMessage.includes('失败')) ||\n      lowerMessage.includes('permission denied') ||\n      lowerMessage.includes('权限不足') ||\n      (lowerMessage.includes('token') && lowerMessage.includes('invalid'))) {\n    return {\n      type: ERROR_TYPES.AUTHORIZATION,\n      level: ERROR_LEVELS.ERROR,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 用户取消\n  if (lowerMessage.includes('cancel') ||\n      lowerMessage.includes('取消') ||\n      lowerMessage.includes('abort') ||\n      lowerMessage.includes('中止')) {\n    return {\n      type: ERROR_TYPES.USER_CANCELLED,\n      level: ERROR_LEVELS.INFO,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 验证错误\n  if (lowerMessage.includes('valid') ||\n      lowerMessage.includes('验证') ||\n      lowerMessage.includes('format') ||\n      lowerMessage.includes('格式') ||\n      lowerMessage.includes('required') ||\n      lowerMessage.includes('必填')) {\n    return {\n      type: ERROR_TYPES.VALIDATION,\n      level: ERROR_LEVELS.WARNING,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 服务器错误\n  if (lowerMessage.includes('server') ||\n      lowerMessage.includes('服务器') ||\n      lowerMessage.includes('500') ||\n      lowerMessage.includes('502') ||\n      lowerMessage.includes('503') ||\n      lowerMessage.includes('504')) {\n    return {\n      type: ERROR_TYPES.SERVER,\n      level: ERROR_LEVELS.ERROR,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 系统错误\n  if (lowerMessage.includes('system') ||\n      lowerMessage.includes('系统') ||\n      lowerMessage.includes('crash') ||\n      lowerMessage.includes('崩溃')) {\n    return {\n      type: ERROR_TYPES.SYSTEM,\n      level: ERROR_LEVELS.CRITICAL,\n      originalMessage: message,\n      stack\n    };\n  }\n  \n  // 默认为未知错误\n  return {\n    type: ERROR_TYPES.UNKNOWN,\n    level: ERROR_LEVELS.ERROR,\n    originalMessage: message,\n    stack\n  };\n}\n\n/**\n * 获取用户友好的错误消息\n * @param {Object} errorAnalysis 错误分析结果\n * @param {Object} customMessages 自定义错误消息\n * @returns {Object} 错误消息对象\n */\nexport function getFriendlyErrorMessage(errorAnalysis, customMessages = {}) {\n  const defaultMessage = ERROR_MESSAGES[errorAnalysis.type] || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];\n  const customMessage = customMessages[errorAnalysis.type];\n  \n  return {\n    ...defaultMessage,\n    ...customMessage,\n    originalMessage: errorAnalysis.originalMessage,\n    type: errorAnalysis.type,\n    level: errorAnalysis.level\n  };\n}\n\n/**\n * 显示错误提示\n * @param {Error|string|Object} error 错误信息\n * @param {Object} options 显示选项\n */\nexport function showError(error, options = {}) {\n  const defaultOptions = {\n    duration: 2000,\n    showType: true,  // 是否在调试模式下显示错误类型\n    customMessages: {},\n    ...options\n  };\n  \n  const errorAnalysis = analyzeError(error);\n  const friendlyMessage = getFriendlyErrorMessage(errorAnalysis, defaultOptions.customMessages);\n  \n  // 在调试模式下记录详细错误信息\n  if (isDebug()) {\n    console.error('错误详情:', {\n      type: errorAnalysis.type,\n      level: errorAnalysis.level,\n      original: errorAnalysis.originalMessage,\n      friendly: friendlyMessage.message,\n      stack: errorAnalysis.stack\n    });\n  }\n  \n  // 构建显示消息\n  let displayMessage = friendlyMessage.message;\n  if (isDebug() && defaultOptions.showType) {\n    displayMessage = `[${errorAnalysis.type}] ${displayMessage}`;\n  }\n  \n  // 根据错误级别决定是否显示\n  if (errorAnalysis.level === ERROR_LEVELS.INFO && errorAnalysis.type === ERROR_TYPES.USER_CANCELLED) {\n    // 用户取消操作通常不需要显示错误提示\n    return;\n  }\n  \n  uni.showToast({\n    title: displayMessage,\n    icon: friendlyMessage.icon || 'none',\n    duration: defaultOptions.duration,\n    mask: false\n  });\n}\n\n/**\n * 错误重试处理器\n * @param {Function} operation 要重试的操作\n * @param {Object} options 重试选项\n * @returns {Promise} 操作结果\n */\nexport function withRetry(operation, options = {}) {\n  const defaultOptions = {\n    maxRetries: 3,\n    retryDelay: 1000,\n    retryDelayMultiplier: 1.5,\n    retryableErrors: [ERROR_TYPES.NETWORK, ERROR_TYPES.TIMEOUT, ERROR_TYPES.SERVER],\n    onRetry: null,\n    ...options\n  };\n  \n  return new Promise(async (resolve, reject) => {\n    let lastError = null;\n    \n    for (let attempt = 0; attempt <= defaultOptions.maxRetries; attempt++) {\n      try {\n        const result = await operation();\n        resolve(result);\n        return;\n      } catch (error) {\n        lastError = error;\n        const errorAnalysis = analyzeError(error);\n        \n        // 检查是否是可重试的错误\n        if (attempt < defaultOptions.maxRetries && \n            defaultOptions.retryableErrors.includes(errorAnalysis.type)) {\n          \n          // 计算延迟时间\n          const delay = defaultOptions.retryDelay * Math.pow(defaultOptions.retryDelayMultiplier, attempt);\n          \n          if (isDebug()) {\n            console.log(`操作失败，${delay}ms后进行第${attempt + 1}次重试:`, errorAnalysis.originalMessage);\n          }\n          \n          // 调用重试回调\n          if (typeof defaultOptions.onRetry === 'function') {\n            defaultOptions.onRetry(attempt + 1, errorAnalysis, delay);\n          }\n          \n          // 等待后重试\n          await new Promise(resolve => setTimeout(resolve, delay));\n        } else {\n          // 不可重试或达到最大重试次数\n          break;\n        }\n      }\n    }\n    \n    // 所有重试都失败了\n    reject(lastError);\n  });\n}\n\n/**\n * 错误边界处理器 - 用于包装可能出错的异步操作\n * @param {Function} operation 异步操作\n * @param {Object} options 选项\n * @returns {Promise} 处理结果\n */\nexport function withErrorBoundary(operation, options = {}) {\n  const defaultOptions = {\n    showError: true,\n    fallbackValue: null,\n    onError: null,\n    ...options\n  };\n  \n  return new Promise(async (resolve) => {\n    try {\n      const result = await operation();\n      resolve({ success: true, data: result, error: null });\n    } catch (error) {\n      const errorAnalysis = analyzeError(error);\n      \n      if (isDebug()) {\n        console.error('错误边界捕获错误:', errorAnalysis);\n      }\n      \n      // 显示错误提示\n      if (defaultOptions.showError) {\n        showError(error, options);\n      }\n      \n      // 调用错误回调\n      if (typeof defaultOptions.onError === 'function') {\n        defaultOptions.onError(errorAnalysis);\n      }\n      \n      resolve({ \n        success: false, \n        data: defaultOptions.fallbackValue, \n        error: errorAnalysis \n      });\n    }\n  });\n}\n"], "names": ["isDebug", "uni"], "mappings": ";;;AAMY,MAAC,cAAc;AAAA,EACzB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA;AAAA,EACT,eAAe;AAAA;AAAA,EACf,YAAY;AAAA;AAAA,EACZ,QAAQ;AAAA;AAAA,EACR,gBAAgB;AAAA;AAAA,EAChB,QAAQ;AAAA;AAAA,EACR,SAAS;AAAA;AACX;AAKO,MAAM,eAAe;AAAA,EAC1B,MAAM;AAAA;AAAA,EACN,SAAS;AAAA;AAAA,EACT,OAAO;AAAA;AAAA,EACP,UAAU;AAAA;AACZ;AAKA,MAAM,iBAAiB;AAAA,EACrB,CAAC,YAAY,OAAO,GAAG;AAAA,IACrB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,OAAO,GAAG;AAAA,IACrB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,aAAa,GAAG;AAAA,IAC3B,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,UAAU,GAAG;AAAA,IACxB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,MAAM,GAAG;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,cAAc,GAAG;AAAA,IAC5B,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,MAAM,GAAG;AAAA,IACpB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA,EACD,CAAC,YAAY,OAAO,GAAG;AAAA,IACrB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AACH;AAOO,SAAS,aAAa,OAAO;AAClC,MAAI,UAAU;AACd,MAAI,QAAQ;AAEZ,MAAI,iBAAiB,OAAO;AAC1B,cAAU,MAAM,WAAW;AAC3B,YAAQ,MAAM,SAAS;AAAA,EAC3B,WAAa,OAAO,UAAU,UAAU;AACpC,cAAU;AAAA,EACd,WAAa,SAAS,MAAM,QAAQ;AAChC,cAAU,MAAM;AAAA,EACpB,OAAS;AACL,cAAU,OAAO,SAAS,EAAE;AAAA,EAC7B;AAED,QAAM,eAAe,QAAQ;AAG7B,MAAI,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,YAAY,KAClC,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,cAAc,GAAG;AACzC,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,UAAU,GAAG;AACrC,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAK,aAAa,SAAS,MAAM,KAAK,CAAC,aAAa,SAAS,MAAM,KAC9D,aAAa,SAAS,IAAI,KAAK,aAAa,SAAS,IAAI,KAC1D,aAAa,SAAS,mBAAmB,KACzC,aAAa,SAAS,MAAM,KAC3B,aAAa,SAAS,OAAO,KAAK,aAAa,SAAS,SAAS,GAAI;AACxE,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,QAAQ,KAC9B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,IAAI,GAAG;AAC/B,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,QAAQ,KAC9B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,UAAU,KAChC,aAAa,SAAS,IAAI,GAAG;AAC/B,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,QAAQ,KAC9B,aAAa,SAAS,KAAK,KAC3B,aAAa,SAAS,KAAK,KAC3B,aAAa,SAAS,KAAK,KAC3B,aAAa,SAAS,KAAK,KAC3B,aAAa,SAAS,KAAK,GAAG;AAChC,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,QAAQ,KAC9B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,OAAO,KAC7B,aAAa,SAAS,IAAI,GAAG;AAC/B,WAAO;AAAA,MACL,MAAM,YAAY;AAAA,MAClB,OAAO,aAAa;AAAA,MACpB,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,SAAO;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,OAAO,aAAa;AAAA,IACpB,iBAAiB;AAAA,IACjB;AAAA,EACJ;AACA;AAQO,SAAS,wBAAwB,eAAe,iBAAiB,IAAI;AAC1E,QAAM,iBAAiB,eAAe,cAAc,IAAI,KAAK,eAAe,YAAY,OAAO;AAC/F,QAAM,gBAAgB,eAAe,cAAc,IAAI;AAEvD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,GAAG;AAAA,IACH,iBAAiB,cAAc;AAAA,IAC/B,MAAM,cAAc;AAAA,IACpB,OAAO,cAAc;AAAA,EACzB;AACA;AAOO,SAAS,UAAU,OAAO,UAAU,IAAI;AAC7C,QAAM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,UAAU;AAAA;AAAA,IACV,gBAAgB,CAAE;AAAA,IAClB,GAAG;AAAA,EACP;AAEE,QAAM,gBAAgB,aAAa,KAAK;AACxC,QAAM,kBAAkB,wBAAwB,eAAe,eAAe,cAAc;AAG5F,MAAIA,WAAO,QAAA,GAAI;AACbC,kBAAAA,MAAA,MAAA,SAAA,iCAAc,SAAS;AAAA,MACrB,MAAM,cAAc;AAAA,MACpB,OAAO,cAAc;AAAA,MACrB,UAAU,cAAc;AAAA,MACxB,UAAU,gBAAgB;AAAA,MAC1B,OAAO,cAAc;AAAA,IAC3B,CAAK;AAAA,EACF;AAGD,MAAI,iBAAiB,gBAAgB;AACrC,MAAID,WAAO,QAAA,KAAM,eAAe,UAAU;AACxC,qBAAiB,IAAI,cAAc,IAAI,KAAK,cAAc;AAAA,EAC3D;AAGD,MAAI,cAAc,UAAU,aAAa,QAAQ,cAAc,SAAS,YAAY,gBAAgB;AAElG;AAAA,EACD;AAEDC,gBAAAA,MAAI,UAAU;AAAA,IACZ,OAAO;AAAA,IACP,MAAM,gBAAgB,QAAQ;AAAA,IAC9B,UAAU,eAAe;AAAA,IACzB,MAAM;AAAA,EACV,CAAG;AACH;AAkEO,SAAS,kBAAkB,WAAW,UAAU,IAAI;AACzD,QAAM,iBAAiB;AAAA,IACrB,WAAW;AAAA,IACX,eAAe;AAAA,IACf,SAAS;AAAA,IACT,GAAG;AAAA,EACP;AAEE,SAAO,IAAI,QAAQ,OAAO,YAAY;AACpC,QAAI;AACF,YAAM,SAAS,MAAM;AACrB,cAAQ,EAAE,SAAS,MAAM,MAAM,QAAQ,OAAO,KAAI,CAAE;AAAA,IACrD,SAAQ,OAAO;AACd,YAAM,gBAAgB,aAAa,KAAK;AAExC,UAAID,WAAO,QAAA,GAAI;AACbC,sBAAc,MAAA,MAAA,SAAA,iCAAA,aAAa,aAAa;AAAA,MACzC;AAGD,UAAI,eAAe,WAAW;AAC5B,kBAAU,OAAO,OAAO;AAAA,MACzB;AAGD,UAAI,OAAO,eAAe,YAAY,YAAY;AAChD,uBAAe,QAAQ,aAAa;AAAA,MACrC;AAED,cAAQ;AAAA,QACN,SAAS;AAAA,QACT,MAAM,eAAe;AAAA,QACrB,OAAO;AAAA,MACf,CAAO;AAAA,IACF;AAAA,EACL,CAAG;AACH;;;;"}