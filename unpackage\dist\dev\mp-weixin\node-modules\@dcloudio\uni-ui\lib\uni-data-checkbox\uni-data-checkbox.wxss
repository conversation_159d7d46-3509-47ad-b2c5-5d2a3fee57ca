/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-data-loading {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 36px;
  padding-left: 10px;
  color: #999;
}
.uni-data-checklist {
  position: relative;
  z-index: 0;
  flex: 1;
}
.uni-data-checklist .checklist-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.uni-data-checklist .checklist-group.is-list {
  flex-direction: column;
}
.uni-data-checklist .checklist-group .checklist-box {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  margin: 5px 0;
  margin-right: 25px;
}
.uni-data-checklist .checklist-group .checklist-box .hidden {
  position: absolute;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checklist-text {
  font-size: 14px;
  color: #666;
  margin-left: 5px;
  line-height: 14px;
}
.uni-data-checklist .checklist-group .checklist-box .checklist-content .checkobx__list {
  border-right-width: 1px;
  border-right-color: #007aff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #007aff;
  border-bottom-style: solid;
  height: 12px;
  width: 6px;
  left: -5px;
  transform-origin: center;
  transform: rotate(45deg);
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner {
  flex-shrink: 0;
  box-sizing: border-box;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 4px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .checkbox__inner .checkbox__inner-icon {
  position: absolute;
  top: 1px;
  left: 5px;
  height: 8px;
  width: 4px;
  border-right-width: 1px;
  border-right-color: #fff;
  border-right-style: solid;
  border-bottom-width: 1px;
  border-bottom-color: #fff;
  border-bottom-style: solid;
  opacity: 0;
  transform-origin: center;
  transform: rotate(40deg);
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner {
  display: flex;
  flex-shrink: 0;
  box-sizing: border-box;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 16px;
  height: 16px;
  border: 1px solid #DCDFE6;
  border-radius: 16px;
  background-color: #fff;
  z-index: 1;
}
.uni-data-checklist .checklist-group .checklist-box .radio__inner .radio__inner-icon {
  width: 8px;
  height: 8px;
  border-radius: 10px;
  opacity: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checkbox__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .radio__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-disable .checklist-text {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checkbox__inner .checkbox__inner-icon {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .radio__inner .radio__inner-icon {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked .checklist-text {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checkbox__inner {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .checklist-text {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--default.is-checked.is-disable .radio__inner {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  transition: border-color 0.2s;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable {
  border: 1px #eee solid;
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checkbox__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .radio__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-disable .checklist-text {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checkbox__inner .checkbox__inner-icon {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .radio__inner .radio__inner-icon {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked .checklist-text {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--button.is-checked.is-disable {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag {
  margin-right: 10px;
  padding: 5px 10px;
  border: 1px #DCDFE6 solid;
  border-radius: 3px;
  background-color: #f5f5f5;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag .checklist-text {
  margin: 0;
  color: #666;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-disable {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked {
  background-color: #2979ff;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--tag.is-checked .checklist-text {
  color: #fff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list {
  display: flex;
  padding: 10px 15px;
  padding-left: 0;
  margin: 0;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-list-border {
  border-top: 1px #eee solid;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checkbox__inner {
  background-color: #F2F6FC;
  border-color: #DCDFE6;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-disable .checklist-text {
  color: #999;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner {
  border-color: #2979ff;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checkbox__inner .checkbox__inner-icon {
  opacity: 1;
  transform: rotate(45deg);
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .radio__inner {
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .radio__inner .radio__inner-icon {
  opacity: 1;
  background-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-text {
  color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked .checklist-content .checkobx__list {
  opacity: 1;
  border-color: #2979ff;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checkbox__inner {
  opacity: 0.4;
}
.uni-data-checklist .checklist-group .checklist-box.is--list.is-checked.is-disable .checklist-text {
  opacity: 0.4;
}