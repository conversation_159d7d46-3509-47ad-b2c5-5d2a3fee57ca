{"version": 3, "file": "case_detail.js", "sources": ["pages/case_detail/case_detail.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvY2FzZV9kZXRhaWwvY2FzZV9kZXRhaWwudnVl"], "sourcesContent": ["<template>\r\n\t<view class=\"case-detail-container\">\r\n\t\t<view class=\"detail-card\">\r\n\t\t\t<!-- 案例标题区域 -->\r\n\t\t\t<view class=\"case-header\">\r\n\t\t\t\t<view class=\"case-title\">{{ caseDetail.title }}</view>\r\n\t\t\t\t<view class=\"case-tags\">\r\n\t\t\t\t\t<text class=\"case-tag\" v-for=\"tag in caseDetail.tags\" :key=\"tag\">{{ tag }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 案例基本信息 -->\r\n\t\t\t<view class=\"case-info\">\r\n\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t<text class=\"info-label\">案例类型</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ caseDetail.type }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t<text class=\"info-label\">债务金额</text>\r\n\t\t\t\t\t<text class=\"info-value debt-amount\">¥{{ formatAmount(caseDetail.debtAmount) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t<text class=\"info-label\">调解结果</text>\r\n\t\t\t\t\t<text class=\"info-value resolved-amount\">¥{{ formatAmount(caseDetail.resolvedAmount) }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t<text class=\"info-label\">减免比例</text>\r\n\t\t\t\t\t<text class=\"info-value reduction-rate\">{{ caseDetail.reductionRate }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"info-row\">\r\n\t\t\t\t\t<text class=\"info-label\">调解日期</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{ caseDetail.date }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 案例详细内容 -->\r\n\t\t\t<view class=\"case-content\">\r\n\t\t\t\t<view class=\"content-section\">\r\n\t\t\t\t\t<view class=\"section-title\">案例背景</view>\r\n\t\t\t\t\t<view class=\"section-content\">{{ caseDetail.background }}</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"content-section\">\r\n\t\t\t\t\t<view class=\"section-title\">调解过程</view>\r\n\t\t\t\t\t<view class=\"section-content\">{{ caseDetail.process }}</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"content-section\">\r\n\t\t\t\t\t<view class=\"section-title\">调解结果</view>\r\n\t\t\t\t\t<view class=\"section-content\">{{ caseDetail.result }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\nimport { onLoad } from '@dcloudio/uni-app';\r\n\r\n// 案例详情数据\r\nconst caseDetail = ref({});\r\n\r\n// 页面加载时获取参数\r\nonLoad((options) => {\r\n\tconst caseId = options.id;\r\n\tif (caseId) {\r\n\t\tloadCaseDetail(caseId);\r\n\t}\r\n});\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\tconsole.log('案例详情页面已加载');\r\n});\r\n\r\n// 加载案例详情\r\nconst loadCaseDetail = (caseId) => {\r\n\t// 模拟API调用，实际项目中应该调用真实API\r\n\tconst mockData = getCaseById(caseId);\r\n\tcaseDetail.value = mockData;\r\n};\r\n\r\n// 根据ID获取案例数据（模拟数据）\r\nconst getCaseById = (id) => {\r\n\tconst cases = [\r\n\t\t{\r\n\t\t\tid: '1',\r\n\t\t\ttitle: '信用卡欠款减免案例',\r\n\t\t\ttype: '信用卡欠款',\r\n\t\t\tdebtAmount: 80000,\r\n\t\t\tresolvedAmount: 28000,\r\n\t\t\treductionRate: '减免65%',\r\n\t\t\tdate: '2023-10-15',\r\n\t\t\ttags: ['信用卡', '减免成功'],\r\n\t\t\tbackground: '张先生因疫情影响，无法按时偿还卡款，通过调解平台申请调解，最终与银行达成和解',\r\n\t\t\tprocess: '1. 提交调解申请；2. 银行方面同意调解；3. 双方协商还款方案；4. 达成一致意见',\r\n\t\t\tresult: '最终协商确定还款金额为28,000元，分36期还款，张先生表示满意'\r\n\t\t},\r\n\t\t{\r\n\t\t\tid: '2',\r\n\t\t\ttitle: '车贷逾期调解案例',\r\n\t\t\ttype: '车贷逾期',\r\n\t\t\tdebtAmount: 120000,\r\n\t\t\tresolvedAmount: 120000,\r\n\t\t\treductionRate: '减免全部罚息',\r\n\t\t\tdate: '2023-09-28',\r\n\t\t\ttags: ['车贷', '延期还款'],\r\n\t\t\tbackground: '李女士因生意周转困难，车贷逾期3个月，通过调解平台寻求帮助',\r\n\t\t\tprocess: '调解员协调双方，了解李女士实际困难，与金融机构协商延期还款方案',\r\n\t\t\tresult: '金融机构同意免除全部罚息，延期6个月还款，李女士非常感谢'\r\n\t\t}\r\n\t];\r\n\t\r\n\treturn cases.find(c => c.id === id) || {};\r\n};\r\n\r\n// 格式化金额显示\r\nconst formatAmount = (amount) => {\r\n\tif (!amount) return '0.00';\r\n\treturn amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.case-detail-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.detail-card {\r\n\tbackground-color: #ffffff;\r\n\tborder-radius: 12rpx;\r\n\toverflow: hidden;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n}\r\n\r\n.case-header {\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.case-title {\r\n\tfont-size: 36rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tline-height: 1.4;\r\n}\r\n\r\n.case-tags {\r\n\tdisplay: flex;\r\n\tflex-wrap: wrap;\r\n\tgap: 15rpx;\r\n}\r\n\r\n.case-tag {\r\n\tpadding: 8rpx 16rpx;\r\n\tbackground-color: #e3f2fd;\r\n\tcolor: #1976d2;\r\n\tfont-size: 24rpx;\r\n\tborder-radius: 20rpx;\r\n}\r\n\r\n.case-info {\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 2rpx solid #f0f0f0;\r\n}\r\n\r\n.info-row {\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tmargin-bottom: 25rpx;\r\n\tpadding: 15rpx 0;\r\n}\r\n\r\n.info-row:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.info-label {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tfont-weight: 500;\r\n}\r\n\r\n.info-value {\r\n\tfont-size: 28rpx;\r\n\tcolor: #333;\r\n\tfont-weight: 600;\r\n}\r\n\r\n.debt-amount {\r\n\tcolor: #f44336;\r\n}\r\n\r\n.resolved-amount {\r\n\tcolor: #4caf50;\r\n}\r\n\r\n.reduction-rate {\r\n\tcolor: #ff9800;\r\n}\r\n\r\n.case-content {\r\n\tpadding: 30rpx;\r\n}\r\n\r\n.content-section {\r\n\tmargin-bottom: 40rpx;\r\n}\r\n\r\n.content-section:last-child {\r\n\tmargin-bottom: 0;\r\n}\r\n\r\n.section-title {\r\n\tfont-size: 32rpx;\r\n\tfont-weight: 600;\r\n\tcolor: #333;\r\n\tmargin-bottom: 20rpx;\r\n\tposition: relative;\r\n\tpadding-left: 20rpx;\r\n}\r\n\r\n.section-title::before {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tleft: 0;\r\n\ttop: 50%;\r\n\ttransform: translateY(-50%);\r\n\twidth: 8rpx;\r\n\theight: 30rpx;\r\n\tbackground-color: #2979ff;\r\n\tborder-radius: 4rpx;\r\n}\r\n\r\n.section-content {\r\n\tfont-size: 28rpx;\r\n\tcolor: #666;\r\n\tline-height: 1.6;\r\n\ttext-align: justify;\r\n}\r\n</style> ", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/case_detail/case_detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onLoad", "onMounted", "uni"], "mappings": ";;;;;AA6DA,UAAM,aAAaA,cAAAA,IAAI,CAAA,CAAE;AAGzBC,kBAAM,OAAC,CAAC,YAAY;AACnB,YAAM,SAAS,QAAQ;AACvB,UAAI,QAAQ;AACX,uBAAe,MAAM;AAAA,MACrB;AAAA,IACF,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACfC,oBAAAA,MAAY,MAAA,OAAA,2CAAA,WAAW;AAAA,IACxB,CAAC;AAGD,UAAM,iBAAiB,CAAC,WAAW;AAElC,YAAM,WAAW,YAAY,MAAM;AACnC,iBAAW,QAAQ;AAAA,IACpB;AAGA,UAAM,cAAc,CAAC,OAAO;AAC3B,YAAM,QAAQ;AAAA,QACb;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,MAAM;AAAA,UACN,MAAM,CAAC,OAAO,MAAM;AAAA,UACpB,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,QAAQ;AAAA,QACR;AAAA,QACD;AAAA,UACC,IAAI;AAAA,UACJ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,eAAe;AAAA,UACf,MAAM;AAAA,UACN,MAAM,CAAC,MAAM,MAAM;AAAA,UACnB,YAAY;AAAA,UACZ,SAAS;AAAA,UACT,QAAQ;AAAA,QACR;AAAA,MACH;AAEC,aAAO,MAAM,KAAK,OAAK,EAAE,OAAO,EAAE,KAAK;IACxC;AAGA,UAAM,eAAe,CAAC,WAAW;AAChC,UAAI,CAAC;AAAQ,eAAO;AACpB,aAAO,OAAO,eAAe,SAAS,EAAE,uBAAuB,EAAC,CAAE;AAAA,IACnE;;;;;;;;;;;;;;;;;;;;;;;ACxHA,GAAG,WAAW,eAAe;"}