/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-7c2ebfa5:root {
  --primary-color:#3B7EEB;
}
.mine-container.data-v-7c2ebfa5 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 15px 15px 70px;
}
.auth.data-v-7c2ebfa5 {
  background-color: #ffffff;
  border-radius: 10rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 20rpx;
}
.user-info-section.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  padding-bottom: 40rpx;
}
.avatar-container.data-v-7c2ebfa5 {
  margin-right: 30rpx;
  border-radius: 10rpx;
}
.avatar.data-v-7c2ebfa5 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
}
.user-details.data-v-7c2ebfa5 {
  flex: 1;
}
.username.data-v-7c2ebfa5 {
  font-size: 36rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

/* 认证状态样式 */
.auth-status.data-v-7c2ebfa5 {
  display: inline-block;
  padding: 2px 0;
  border-radius: 4px;
  font-size: 12px;
  margin-top: 5px;
}
.auth-status.authenticated.data-v-7c2ebfa5 {
  background-color: #E6F7FF;
  color: var(--primary-color);
}
.auth-status.authenticated .fa-check-circle.data-v-7c2ebfa5 {
  color: #3B7EEB;
}
.auth-status.need_auth.data-v-7c2ebfa5 {
  color: #979292;
}
.auth-status.need_auth .fa-exclamation-circle.data-v-7c2ebfa5 {
  color: #979292;
}
.auth-button.data-v-7c2ebfa5 {
  width: 640rpx;
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.auth-button.authenticated.data-v-7c2ebfa5 {
  background-color: #52c41a;
}
.auth-button.authenticated .fas.data-v-7c2ebfa5 {
  margin-right: 10rpx;
  font-size: 28rpx;
}
.auth-button.need-auth.data-v-7c2ebfa5 {
  background-color: #3B7EEB;
}
.auth-button.need-auth .fas.data-v-7c2ebfa5 {
  margin-right: 10rpx;
  font-size: 28rpx;
}
.auth-button[disabled].data-v-7c2ebfa5 {
  background-color: #cccccc;
}
.login-button.data-v-7c2ebfa5 {
  width: 640rpx;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 32rpx;
  border-radius: 10rpx;
}
.auth-tip.data-v-7c2ebfa5 {
  font-size: 24rpx;
  color: #ff6b35;
  margin-top: 10rpx;
}
.login-tip.data-v-7c2ebfa5 {
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}
.function-item.data-v-7c2ebfa5 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 30rpx;
  margin-bottom: 30rpx;
  transition: var(--transition-normal);
  border: 2rpx solid rgba(0, 0, 0, 0.03);
}
.function-item.disabled.data-v-7c2ebfa5 {
  opacity: 0.6;
  background-color: #f5f5f5;
}
.function-item.disabled .function-text.data-v-7c2ebfa5 {
  color: #999;
}
.function-item.disabled .fa-clipboard-list.data-v-7c2ebfa5 {
  color: #ccc;
}
.function-item.disabled .fa-lock.data-v-7c2ebfa5 {
  color: #fa8c16;
}
.function-item-left.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  position: relative;
}
.auth-required-tag.data-v-7c2ebfa5 {
  background-color: #fa8c16;
  color: white;
  font-size: 20rpx;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin-left: 15rpx;
}
.function-text.data-v-7c2ebfa5 {
  font-size: 30rpx;
  color: #333;
  margin-left: 20rpx;
}
.fa-clipboard-list.data-v-7c2ebfa5, .fa-shield-alt.data-v-7c2ebfa5 {
  font-size: 40rpx;
  color: #3b7eeb;
}
.fa-chevron-right.data-v-7c2ebfa5 {
  color: #999;
}
.btn-danger.data-v-7c2ebfa5 {
  background-color: #f5222d;
  color: white;
  margin-top: 20rpx;
  display: block;
  width: 100%;
}

/* 加载骨架屏样式 */
.loading-skeleton .skeleton-user-info.data-v-7c2ebfa5 {
  display: flex;
  align-items: center;
  padding-bottom: 40rpx;
}
.loading-skeleton .skeleton-avatar.data-v-7c2ebfa5 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading-7c2ebfa5 1.5s infinite;
  margin-right: 30rpx;
}
.loading-skeleton .skeleton-details.data-v-7c2ebfa5 {
  flex: 1;
}
.loading-skeleton .skeleton-name.data-v-7c2ebfa5 {
  width: 200rpx;
  height: 36rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading-7c2ebfa5 1.5s infinite;
  border-radius: 4rpx;
  margin-bottom: 20rpx;
}
.loading-skeleton .skeleton-tip.data-v-7c2ebfa5 {
  width: 300rpx;
  height: 24rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading-7c2ebfa5 1.5s infinite;
  border-radius: 4rpx;
}
.loading-skeleton .skeleton-button.data-v-7c2ebfa5 {
  width: 640rpx;
  height: 80rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading-7c2ebfa5 1.5s infinite;
  border-radius: 10rpx;
}
@keyframes skeleton-loading-7c2ebfa5 {
0% {
    background-position: -200% 0;
}
100% {
    background-position: 200% 0;
}
}