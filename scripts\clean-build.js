// 编译清理脚本
// 用于清理uni-popup相关的编译缓存，解决编译错误

const fs = require('fs');
const path = require('path');

/**
 * 递归删除目录
 * @param {string} dirPath 目录路径
 */
function removeDir(dirPath) {
  if (fs.existsSync(dirPath)) {
    try {
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(`✅ 已删除目录: ${dirPath}`);
    } catch (error) {
      console.warn(`⚠️ 删除目录失败: ${dirPath}`, error.message);
    }
  } else {
    console.log(`ℹ️ 目录不存在: ${dirPath}`);
  }
}

/**
 * 删除文件
 * @param {string} filePath 文件路径
 */
function removeFile(filePath) {
  if (fs.existsSync(filePath)) {
    try {
      fs.unlinkSync(filePath);
      console.log(`✅ 已删除文件: ${filePath}`);
    } catch (error) {
      console.warn(`⚠️ 删除文件失败: ${filePath}`, error.message);
    }
  } else {
    console.log(`ℹ️ 文件不存在: ${filePath}`);
  }
}

console.log('🧹 开始清理uni-popup相关的编译缓存...\n');

// 清理目录列表
const dirsToClean = [
  'unpackage/dist/dev/mp-weixin/node-modules/@dcloudio/uni-ui',
  'unpackage/dist/dev/mp-weixin/node-modules/@dcloudio',
  'unpackage/dist/build/mp-weixin/node-modules/@dcloudio/uni-ui',
  'unpackage/dist/build/mp-weixin/node-modules/@dcloudio'
];

// 清理文件列表
const filesToClean = [
  'unpackage/dist/dev/mp-weixin/app.json',
  'unpackage/dist/build/mp-weixin/app.json'
];

// 执行清理
console.log('📁 清理目录...');
dirsToClean.forEach(dir => {
  const fullPath = path.resolve(__dirname, '..', dir);
  removeDir(fullPath);
});

console.log('\n📄 清理文件...');
filesToClean.forEach(file => {
  const fullPath = path.resolve(__dirname, '..', file);
  removeFile(fullPath);
});

console.log('\n✨ 清理完成！');
console.log('\n📋 接下来请执行以下步骤：');
console.log('1. 在HBuilderX中点击"运行" -> "清理项目"');
console.log('2. 重新编译项目到微信小程序');
console.log('3. 如果仍有错误，请重启HBuilderX后再次编译');
