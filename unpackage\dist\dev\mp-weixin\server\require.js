"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
let isRefreshing = false;
let refreshPromise = null;
const waitingRequests = [];
let lastTokenCheck = 0;
const TOKEN_CHECK_INTERVAL = 5e3;
const getToken = () => {
  var _a;
  let token = common_vendor.index.getStorageSync("access_token");
  if (!token) {
    token = common_vendor.index.getStorageSync("token");
  }
  if (!token) {
    try {
      const pages = getCurrentPages();
      if (pages && pages.length > 0) {
        const currentPage = pages[pages.length - 1];
        token = ((_a = currentPage.options) == null ? void 0 : _a.token) || "";
        if (token) {
          common_vendor.index.setStorageSync("access_token", token);
          common_vendor.index.__f__("log", "at server/require.js:38", "从URL参数获取token并保存到本地存储");
        }
      }
    } catch (error) {
      common_vendor.index.__f__("log", "at server/require.js:42", "从URL获取token失败:", error);
    }
  }
  return token;
};
const getTokenType = () => {
  return common_vendor.index.getStorageSync("token_type") || "Bearer";
};
const NO_AUTH_APIS = [
  "/user/login",
  // 普通登录
  "/wechat/login/",
  // 微信登录
  "/user/phone-login",
  // 手机号登录
  "/user/send-code",
  // 发送验证码
  "/wechat/refresh/",
  // Token刷新（使用refresh_token）
  "/home/<USER>",
  // 首页数据（可能不需要认证）
  "/case_display/case_display/"
  // 案例展示（可能不需要认证）
];
const shouldSkipAuth = (url) => {
  return NO_AUTH_APIS.some((pattern) => {
    return url === pattern || url.startsWith(pattern);
  });
};
const requestInterceptor = (config) => {
  const skipAuth = config.skipAuth || shouldSkipAuth(config.url);
  if (skipAuth) {
    common_vendor.index.__f__("log", "at server/require.js:90", `接口 ${config.url} 跳过Authorization头部`);
    return config;
  }
  const token = getToken();
  const tokenType = getTokenType();
  if (token) {
    config.header = {
      ...config.header,
      "Authorization": `${tokenType} ${token}`
    };
    common_vendor.index.__f__("log", "at server/require.js:103", `已为请求添加Authorization头: ${tokenType} ${token.substring(0, 10)}...`);
  } else {
    common_vendor.index.__f__("warn", "at server/require.js:105", `接口 ${config.url} 未找到token，请求将不包含Authorization头`);
  }
  return config;
};
const responseInterceptor = (response) => {
  if (response.statusCode === 401) {
    common_vendor.index.showToast({
      title: "登录状态已过期，请重新登录",
      icon: "none"
    });
    const keysToRemove = [
      "token",
      "access_token",
      "token_type",
      "token_expire_time",
      "refresh_token",
      "userInfo",
      "openid",
      "unionid",
      "sessionKey",
      "wechat_userInfo"
    ];
    keysToRemove.forEach((key) => {
      common_vendor.index.removeStorageSync(key);
    });
    setTimeout(() => {
      common_vendor.index.navigateTo({
        url: "/pages/login/login"
      });
    }, 1500);
    return Promise.reject(new Error("登录状态已过期，请重新登录"));
  }
  if (response.statusCode !== 200) {
    common_vendor.index.showToast({
      title: response.data.msg || "请求失败",
      icon: "none"
    });
    return Promise.reject(new Error(response.data.msg || "请求失败"));
  }
  return response.data;
};
const createRequest = (defaultOptions = {}) => {
  return (options) => {
    return request({
      ...defaultOptions,
      ...options
    });
  };
};
const request = (options) => {
  return new Promise(async (resolve, reject) => {
    try {
      const isTokenValid = authUtils.checkTokenValidityWithCache();
      if (!isTokenValid && authUtils.getRefreshToken()) {
        if (isRefreshing) {
          authUtils.addRequestToQueue(options, resolve, reject);
          return;
        }
        try {
          await authUtils.refreshToken();
          authUtils.processWaitingRequests();
        } catch (refreshError) {
          authUtils.processWaitingRequests(refreshError);
          reject(refreshError);
          return;
        }
      }
      const config = requestInterceptor(options);
      common_vendor.index.request({
        ...config,
        url: `${config_env.getBaseURL()}${config.url}`,
        timeout: config_env.getTimeout(),
        success: (res) => {
          try {
            const response = responseInterceptor(res);
            resolve(response);
          } catch (error) {
            if (res.statusCode === 401 && !isRefreshing && authUtils.getRefreshToken()) {
              authUtils.addRequestToQueue(options, resolve, reject);
              authUtils.refreshToken().then(() => {
                authUtils.processWaitingRequests();
              }).catch((refreshError) => {
                authUtils.processWaitingRequests(refreshError);
              });
            } else {
              reject(error);
            }
          }
        },
        fail: (err) => {
          let errorMessage = "网络请求失败";
          if (err.errMsg) {
            if (err.errMsg.includes("timeout")) {
              errorMessage = "请求超时，请检查网络连接";
            } else if (err.errMsg.includes("fail")) {
              errorMessage = "网络连接失败，请检查网络设置";
            } else {
              errorMessage = `网络请求失败：${err.errMsg}`;
            }
          }
          common_vendor.index.showToast({
            title: errorMessage,
            icon: "none",
            duration: 2e3
          });
          reject(new Error(errorMessage));
        }
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at server/require.js:261", "请求处理异常:", error);
      reject(error);
    }
  });
};
const authUtils = {
  // 设置完整的token信息
  setTokenInfo: (tokenInfo) => {
    const { access_token, token_type = "Bearer", expires_in } = tokenInfo;
    if (access_token) {
      common_vendor.index.setStorageSync("access_token", access_token);
      common_vendor.index.setStorageSync("token_type", token_type);
      if (expires_in) {
        const expireTime = Date.now() + expires_in * 1e3;
        common_vendor.index.setStorageSync("token_expire_time", expireTime);
      }
      common_vendor.index.__f__("log", "at server/require.js:285", `Token信息已保存: ${token_type} ${access_token.substring(0, 10)}...`);
      return true;
    }
    return false;
  },
  // 手动设置token到本地存储（向后兼容）
  setToken: (token) => {
    if (token) {
      common_vendor.index.setStorageSync("access_token", token);
      common_vendor.index.setStorageSync("token_type", "Bearer");
      common_vendor.index.__f__("log", "at server/require.js:296", "Token已保存到本地存储");
      return true;
    }
    return false;
  },
  // 获取当前token
  getToken: () => {
    return getToken();
  },
  // 获取token类型
  getTokenType: () => {
    return getTokenType();
  },
  // 获取完整的Authorization头
  getAuthorizationHeader: () => {
    const token = getToken();
    const tokenType = getTokenType();
    return token ? `${tokenType} ${token}` : null;
  },
  // 检查token是否过期（增强版本，支持提前刷新和容错处理）
  isTokenExpired: (advanceMinutes = 5) => {
    const expireTime = common_vendor.index.getStorageSync("token_expire_time");
    if (!expireTime || expireTime <= 0)
      return true;
    const currentTime = Date.now();
    const advanceTime = advanceMinutes * 60 * 1e3;
    return currentTime >= expireTime - advanceTime;
  },
  // 清除token
  clearToken: () => {
    const keysToRemove = [
      "token",
      "access_token",
      "token_type",
      "token_expire_time",
      "refresh_token",
      "userInfo",
      "openid",
      "unionid",
      "sessionKey",
      "wechat_userInfo"
    ];
    keysToRemove.forEach((key) => {
      common_vendor.index.removeStorageSync(key);
    });
    common_vendor.index.__f__("log", "at server/require.js:340", "所有Token信息已清除");
  },
  // 检查token是否存在且未过期
  hasValidToken: () => {
    const token = getToken();
    return !!token && !authUtils.isTokenExpired();
  },
  // 检查token是否存在（不检查过期）
  hasToken: () => {
    const token = getToken();
    return !!token;
  },
  // 获取refresh_token
  getRefreshToken: () => {
    return common_vendor.index.getStorageSync("refresh_token") || null;
  },
  // 设置refresh_token
  setRefreshToken: (refreshToken) => {
    if (refreshToken) {
      common_vendor.index.setStorageSync("refresh_token", refreshToken);
      common_vendor.index.__f__("log", "at server/require.js:364", "Refresh Token已保存");
      return true;
    }
    return false;
  },
  // Token自动刷新核心函数 - 增强版本
  refreshToken: async () => {
    if (isRefreshing) {
      return refreshPromise;
    }
    const refreshToken = authUtils.getRefreshToken();
    if (!refreshToken) {
      throw new Error("Refresh Token不存在，请重新登录");
    }
    isRefreshing = true;
    refreshPromise = new Promise(async (resolve, reject) => {
      try {
        common_vendor.index.__f__("log", "at server/require.js:385", "开始刷新Token...");
        let retryCount = 0;
        const maxRetries = 2;
        while (retryCount <= maxRetries) {
          try {
            const response = await common_vendor.index.request({
              url: `${config_env.getBaseURL()}/wechat/refresh/`,
              method: "POST",
              header: {
                "Authorization": `Bearer ${refreshToken}`,
                "Content-Type": "application/json"
              },
              timeout: config_env.getTimeout(),
              data: {
                refresh_token: refreshToken
              }
            });
            if (response.statusCode === 200 && response.data) {
              const responseData = response.data.data || response.data;
              const { access_token, token_type, expires_in, refresh_token: newRefreshToken } = responseData;
              if (access_token) {
                authUtils.setTokenInfo({
                  access_token,
                  token_type: token_type || "Bearer",
                  expires_in
                });
                if (newRefreshToken) {
                  authUtils.setRefreshToken(newRefreshToken);
                }
                common_vendor.index.__f__("log", "at server/require.js:427", "Token刷新成功");
                resolve({
                  access_token,
                  token_type: token_type || "Bearer",
                  expires_in,
                  refresh_token: newRefreshToken || refreshToken
                });
                return;
              }
            }
            if (response.statusCode === 401 || response.statusCode === 403) {
              throw new Error("Refresh Token已过期，请重新登录");
            }
            throw new Error(`刷新失败，状态码: ${response.statusCode}, 响应: ${JSON.stringify(response.data)}`);
          } catch (error) {
            retryCount++;
            if (retryCount > maxRetries) {
              throw error;
            }
            await new Promise((resolve2) => setTimeout(resolve2, 1e3));
            common_vendor.index.__f__("log", "at server/require.js:455", `Token刷新重试 ${retryCount}/${maxRetries}`);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at server/require.js:460", "Token刷新失败:", error.msg);
        authUtils.clearToken();
        if (!authUtils._showingLogoutToast) {
          authUtils._showingLogoutToast = true;
          common_vendor.index.showToast({
            title: "登录已过期，请重新登录",
            icon: "none",
            duration: 2e3
          });
          setTimeout(() => {
            authUtils._showingLogoutToast = false;
          }, 3e3);
        }
        if (!authUtils._navigatingToLogin) {
          authUtils._navigatingToLogin = true;
          setTimeout(() => {
            common_vendor.index.navigateTo({
              url: "/pages/login/login",
              complete: () => {
                authUtils._navigatingToLogin = false;
              }
            });
          }, 2e3);
        }
        reject(error);
      } finally {
        isRefreshing = false;
        refreshPromise = null;
      }
    });
    return refreshPromise;
  },
  // 添加请求到等待队列
  addRequestToQueue: (config, resolve, reject) => {
    waitingRequests.push({ config, resolve, reject });
  },
  // 处理等待队列中的请求
  processWaitingRequests: (error = null) => {
    waitingRequests.forEach(({ config, resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        request(config).then(resolve).catch(reject);
      }
    });
    waitingRequests.length = 0;
  },
  // 优化的token有效性检查（带缓存）
  checkTokenValidityWithCache: () => {
    const now = Date.now();
    if (now - lastTokenCheck < TOKEN_CHECK_INTERVAL) {
      return true;
    }
    lastTokenCheck = now;
    const hasToken = authUtils.hasToken();
    const isExpired = authUtils.isTokenExpired();
    return hasToken && !isExpired;
  }
};
exports.authUtils = authUtils;
exports.createRequest = createRequest;
exports.request = request;
//# sourceMappingURL=../../.sourcemap/mp-weixin/server/require.js.map
