<template>
	<view class="real-case-container">
		<view class="header">
			<view class="header-title">调解成功案例</view>
			<text class="header-text">以下是部分真实调解成功的案例，供您参考</text>
		</view>
		<button @click="navigateTo()">测</button>
		<!-- 搜索和筛选区域 -->
		<!-- <view class="search-filter-section">
			<view class="search-box">
				<uni-easyinput
					v-model="searchKeyword"
					placeholder="搜索案例关键词"
					suffixIcon="search"
					:clearable="true"
					@confirm="handleSearch"
					@iconClick="handleSearch"
				></uni-easyinput>
			</view>
			
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{ active: activeFilter === item.value }"
					v-for="item in filterOptions" 
					:key="item.value"
					@click="handleFilterChange(item.value)"
				>
					{{ item.label }}
				</view>
			</view>
		</view> -->

		<!-- 案例列表 -->
		<view class="case-list">
			<view 
				class="case-card" 
				v-for="caseItem in filteredCases" 
				:key="caseItem.id"
				@click="navigateToDetail(caseItem.id)"
			>
				<!-- 案例头部 -->
				<view class="case-header">
					<view class="case-title">{{ caseItem.title }}</view>
					<view class="case-date">{{ caseItem.date }}</view>
				</view>

				<!-- 案例标签 -->
				<!-- <view class="case-tags">
					<text class="case-tag type-tag">{{ caseItem.type }}</text>
					<text class="case-tag status-tag" :class="getStatusClass(caseItem.status)">
						{{ caseItem.status }}
					</text>
				</view> -->

				<!-- 金额信息 -->
				<view class="amount-info">
					<view class="amount-row">
						<text class="amount-label">债务金额：</text>
						<text class="debt-amount">¥{{ formatAmount(caseItem.debtAmount) }}</text>
					</view>
					<view class="amount-row">
						<text class="amount-label">调解结果：</text>
						<!-- ¥{{ formatAmount(caseItem.resolvedAmount) }} -->
						<text class="resolved-amount">{{ caseItem.reductionRate }}</text>
					</view>
					<!-- <view class="reduction-info">
						<text class="reduction-rate">{{ caseItem.reductionRate }}</text>
					</view> -->
				</view>

				<!-- 案例简介 -->
				<view class="case-summary">
					<text class="summary-text">{{ caseItem.summary }}</text>
				</view>

				<!-- 查看详情按钮 -->
				<view class="detail-button">
					<text class="button-text">查看详情</text>
					<text class="arrow-icon">›</text>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="filteredCases.length === 0 && !loading">
			<text class="empty-text">暂无相关案例</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
// 导入API工具类
import { api } from '@/utils/api.js';

// 响应式数据
const searchKeyword = ref('');
const activeFilter = ref('all');
const loading = ref(false);
const caseList = ref([]);

// 筛选选项
const filterOptions = [
	{ label: '全部', value: 'all' },
	{ label: '信用卡', value: 'credit_card' },
	{ label: '车贷', value: 'car_loan' },
	{ label: '房贷', value: 'mortgage' },
	{ label: '其他', value: 'other' }
];

// 生命周期钩子
onMounted(() => {
	console.log('案例展示页面已加载，开始获取后台数据');
	// 获取案例列表数据
	// fetchCaseList();
});

// 获取案例列表
const fetchCaseList = async () => {
	loading.value = true;
	
	try {
		// 检查token是否存在（避免401未授权错误）
		const hasToken = api.auth.hasToken();
		if (!hasToken) {
			console.warn('未找到token，可能需要重新登录');
			// 可以选择跳转到登录页面或使用游客模式
		}
		
		// 调用后台API获取案例数据
		const response = await api.realCase.getList();
		
		// 处理API返回的数据格式，映射到前端需要的字段
		if (response && response.data) {
			caseList.value = response.data.map(item => ({
				id: item.id || item.case_id,
				title: item.title || item.case_title,
				type: item.type || item.case_type,
				debtAmount: item.debt_amount || item.debtAmount,
				resolvedAmount: item.resolved_amount || item.resolvedAmount,
				reductionRate: item.reduction_rate || item.reductionRate,
				date: item.date || item.create_time,
				status: item.status || item.case_status,
				summary: item.summary || item.description,
				category: item.category || mapTypeToCategory(item.type)
			}));
		} else {
			// 如果后台返回空数据，使用空数组
			caseList.value = [];
		}
		
		console.log('案例数据加载成功:', caseList.value.length, '条记录');
		
	} catch (error) {
		console.error('获取案例列表失败:', error);
		
		// 显示用户友好的错误提示
		uni.showToast({
			title: '加载失败，请重试',
			icon: 'none',
			duration: 2000
		});
		
		// 设置空数组避免页面出错
		caseList.value = [];
	} finally {
		loading.value = false;
	}
};



// 将案例类型映射到分类的辅助函数
const mapTypeToCategory = (type) => {
	if (!type) return 'other';
	const typeStr = type.toString().toLowerCase();
	if (typeStr.includes('信用卡') || typeStr.includes('credit')) return 'credit_card';
	if (typeStr.includes('车贷') || typeStr.includes('car')) return 'car_loan';
	if (typeStr.includes('房贷') || typeStr.includes('mortgage')) return 'mortgage';
	return 'other';
};

// 过滤后的案例列表
const filteredCases = computed(() => {
	let filtered = caseList.value;
	
	// 按类型筛选
	if (activeFilter.value !== 'all') {
		filtered = filtered.filter(item => item.category === activeFilter.value);
	}
	
	// 按搜索关键词筛选
	if (searchKeyword.value.trim()) {
		const keyword = searchKeyword.value.trim().toLowerCase();
		filtered = filtered.filter(item => 
			item.title.toLowerCase().includes(keyword) ||
			item.type.toLowerCase().includes(keyword) ||
			item.summary.toLowerCase().includes(keyword)
		);
	}
	
	return filtered;
});

// 处理搜索
const handleSearch = () => {
	// 触发计算属性重新计算
	console.log('搜索关键词：', searchKeyword.value);
};

// 处理筛选变化
const handleFilterChange = (filterValue) => {
	activeFilter.value = filterValue;
};
// 跳转到云开发静态页面
const navigateTo = () => {
	/* api.realCase.getDetails().then(res => {
		console.log('getDetails成功:', res);
	}).catch(err => {
		console.error('getDetails失败:', err);
	}); */
	uni.navigateTo({
		url: `/pages/webview/webview?url=${encodeURIComponent('https://www.eeclat.cn/jump_mp/#wechat_redirect')}`
	});
	/* wx.navigateToMiniProgram({
		appId: '',
		path: 'https://prod-9gwr0pqvc081f7f4-1370735801.tcloudbaseapp.com/',
		extraData: {
			foo: 'bar'
		},
		envVersion: 'develop',
		success(res) {
			// 打开成功
			console.log(res,'打开静态开发页面成功');
		}
	}); */
};
// 跳转到详情页面
const navigateToDetail = async (caseId) => {
	try {
		// 记录用户操作日志（根据系统要求记录按钮点击操作）
		await recordUserOperation('案例展示', '查看详情', caseId);
		
		// 跳转到详情页面
		uni.navigateTo({
			url: `/pages/case_detail/case_detail?id=${caseId}`
		});
	} catch (error) {
		console.error('记录操作日志失败:', error);
		// 即使日志记录失败，仍然允许页面跳转
		uni.navigateTo({
			url: `/pages/case_detail/case_detail?id=${caseId}`
		});
	}
};

// 记录用户操作日志的函数
const recordUserOperation = async (menuName, buttonName, extraData = '') => {
	try {
		const pages = getCurrentPages();
		const currentPage = pages[pages.length - 1];
		const browserPath = currentPage.route;
		
		// 构造操作日志数据
		const logData = {
			button_name: buttonName,       // 按钮名称
			button_type: buttonName,       // 按钮类型
			page_url: `/${browserPath}`, // 浏览器路径
			page_plate: menuName,           // 菜单名称
			// operation_time: new Date().toISOString(), // 操作时间
			// extra_data: extraData.toString() // 额外数据（如案例ID）
		};
		
		// 调用API记录操作日志
		await api.operationLog.recordOperation(logData);
		console.log('用户操作日志记录成功:', logData);
		
	} catch (error) {
		console.error('记录用户操作日志失败:', error);
		// 不阻断正常业务流程
	}
};

// 格式化金额显示
const formatAmount = (amount) => {
	if (!amount) return '0.00';
	return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });
};

// 获取状态样式类
const getStatusClass = (status) => {
	switch (status) {
		case '调解成功':
			return 'success';
		case '延期还款':
			return 'warning';
		case '调解中':
			return 'processing';
		default:
			return 'default';
	}
};
</script>

<style lang="scss" scoped>
.real-case-container {
	// min-height: 100vh;
	// background-color: #f5f5f5;
	// padding: 0 20rpx 30rpx 20rpx;
	height: calc(100% - 94px);
    overflow-y: auto;
    background-color: rgb(248, 250, 252);
    padding: 30rpx 30rpx 140rpx;
}

.header{
	background-color: white;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 2px 8px;
    margin-bottom: 30rpx;
    border-radius: 24rpx;
    padding: 46rpx;
    transition: all 0.3s ease;
    border-width: 2rpx;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.03);
    border-image: initial;
}
.header-title{
	font-size: 36rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 20rpx;
}
.header-text{
	font-size: 28rpx;
    color: #666;
    margin-bottom: 30rpx;
}
/* 
.search-filter-section {
	background-color: #ffffff;
	padding: 30rpx 20rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-box {
	margin-bottom: 30rpx;
}

.filter-tabs {
	display: flex;
	gap: 20rpx;
	overflow-x: auto;
}

.filter-tab {
	padding: 15rpx 30rpx;
	background-color: #f8f8f8;
	color: #666;
	border-radius: 30rpx;
	font-size: 26rpx;
	white-space: nowrap;
	transition: all 0.3s ease;
}

.filter-tab.active {
	background-color: #2979ff;
	color: #ffffff;
} */

.case-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: relative;
}

.case-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 20rpx;
}

.case-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	flex: 1;
	line-height: 1.4;
}

.case-date {
	font-size: 24rpx;
	color: #999;
	margin-left: 20rpx;
}

.case-tags {
	display: flex;
	gap: 15rpx;
	margin-bottom: 25rpx;
}

.case-tag {
	padding: 8rpx 16rpx;
	font-size: 22rpx;
	border-radius: 20rpx;
}

.type-tag {
	background-color: #e3f2fd;
	color: #1976d2;
}

.status-tag {
	&.success {
		background-color: #e8f5e8;
		color: #4caf50;
	}
	
	&.warning {
		background-color: #fff3e0;
		color: #ff9800;
	}
	
	&.processing {
		background-color: #f3e5f5;
		color: #9c27b0;
	}
	
	&.default {
		background-color: #f5f5f5;
		color: #666;
	}
}

.amount-info {
	margin-bottom: 25rpx;
}

.amount-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.amount-label {
	font-size: 26rpx;
	color: #666;
}

.debt-amount {
	font-size: 26rpx;
	color: #f44336;
	font-weight: 600;
}

.resolved-amount {
	font-size: 26rpx;
	color: #4caf50;
	font-weight: 600;
}

.reduction-info {
	text-align: right;
	margin-top: 5rpx;
}

.reduction-rate {
	font-size: 24rpx;
	color: #ff9800;
	font-weight: 500;
}

.case-summary {
	margin-bottom: 25rpx;
}

.summary-text {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.detail-button {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 15rpx 0;
	border-top: 2rpx solid #f0f0f0;
	margin-top: 20rpx;
}

.button-text {
	font-size: 28rpx;
	color: #2979ff;
	margin-right: 10rpx;
}

.arrow-icon {
	font-size: 32rpx;
	color: #2979ff;
	transform: rotate(90deg);
}

.empty-state, .loading-state {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 100rpx 0;
}

.empty-text, .loading-text {
	font-size: 28rpx;
	color: #999;
}

/* uni-easyinput 组件样式覆盖 */
:deep(.uni-easyinput__content) {
	height: 80rpx;
	background-color: #f8f8f8;
	border: none;
	border-radius: 40rpx;
	padding: 0 30rpx;
}

:deep(.uni-easyinput__content-input) {
	font-size: 28rpx;
}
</style>