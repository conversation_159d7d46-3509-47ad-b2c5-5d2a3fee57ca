"use strict";
const common_vendor = require("../common/vendor.js");
const utils_wechatAuth = require("./wechat-auth.js");
const config_env = require("../config/env.js");
const USER_STATUS = {
  GUEST: "guest",
  // 游客状态
  LOGGED_IN: "logged_in",
  // 已登录
  VERIFIED: "verified",
  // 已实名认证
  DISABLED: "disabled"
  // 账户被禁用
};
const defaultUserData = {
  // 基础信息
  userId: null,
  username: "",
  nickname: "",
  avatar: "",
  phone: "",
  email: "",
  // 微信信息
  openid: "",
  unionid: "",
  wechat_openid: "",
  wechat_nickname: "",
  wechat_avatar_url: "",
  wechatInfo: {
    nickName: "",
    avatarUrl: "",
    gender: 0,
    country: "",
    province: "",
    city: "",
    language: ""
  },
  // 认证信息
  isVerified: false,
  realName: "",
  idCard: "",
  verifyTime: null,
  // 状态信息
  status: USER_STATUS.GUEST,
  loginTime: null,
  lastActiveTime: null,
  // 权限信息
  permissions: [],
  roles: [],
  detect_auth_result: false
};
class UserStore {
  constructor() {
    this.state = common_vendor.reactive({
      ...defaultUserData,
      isLoading: false,
      isInitialized: false
    });
    this.init();
  }
  /**
   * 初始化用户状态
   */
  async init() {
    try {
      this.state.isLoading = true;
      const loginStatus = await utils_wechatAuth.checkLogin();
      if (loginStatus.isLogin) {
        await this.restoreUserInfo();
        await this.validateToken();
      } else {
        this.clearUserInfo();
      }
      this.state.isInitialized = true;
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/user-store.js:102", "用户状态初始化完成:", this.state);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:105", "用户状态初始化失败:", error);
      this.clearUserInfo();
    } finally {
      this.state.isLoading = false;
    }
  }
  /**
   * 从本地存储恢复用户信息
   */
  async restoreUserInfo() {
    try {
      const userInfo = common_vendor.index.getStorageSync("userInfo");
      const wechatUserInfo = common_vendor.index.getStorageSync("wechat_userInfo");
      const openid = common_vendor.index.getStorageSync("openid");
      const unionid = common_vendor.index.getStorageSync("unionid");
      const wechatOpenid = common_vendor.index.getStorageSync("wechat_openid");
      const userIsStaff = common_vendor.index.getStorageSync("detect_auth_result");
      if (userInfo) {
        Object.assign(this.state, userInfo);
        this.state.status = USER_STATUS.LOGGED_IN;
      }
      if (wechatUserInfo) {
        this.state.wechatInfo = wechatUserInfo;
        if (!this.state.avatar && wechatUserInfo.avatarUrl) {
          this.state.avatar = wechatUserInfo.avatarUrl;
        }
        if (!this.state.nickname && wechatUserInfo.nickName) {
          this.state.nickname = wechatUserInfo.nickName;
        }
      }
      if (openid) {
        this.state.openid = openid;
      }
      if (unionid) {
        this.state.unionid = unionid;
      }
      if (wechatOpenid) {
        this.state.wechat_openid = wechatOpenid;
      }
      if (userIsStaff !== null && userIsStaff !== void 0) {
        this.state.detect_auth_result = userIsStaff;
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:158", "恢复用户信息失败:", error);
    }
  }
  /**
   * 验证token有效性
   */
  async validateToken() {
    try {
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:171", "Token验证失败:", error);
      this.clearUserInfo();
      return false;
    }
  }
  /**
   * 设置用户信息
   */
  setUserInfo(userInfo) {
    try {
      Object.assign(this.state, userInfo);
      this.state.status = USER_STATUS.LOGGED_IN;
      this.state.loginTime = (/* @__PURE__ */ new Date()).toISOString();
      this.updateLastActiveTime();
      const userInfoToStore = {
        // 基础字段
        userId: this.state.userId,
        username: this.state.username || this.state.wechat_nickname,
        nickname: this.state.nickname,
        avatar: this.state.avatar,
        phone: this.state.phone,
        email: this.state.email,
        isVerified: this.state.isVerified,
        realName: this.state.realName,
        status: this.state.status,
        loginTime: this.state.loginTime,
        permissions: this.state.permissions,
        roles: this.state.roles,
        // 保存API返回的所有原始字段，确保数据完整性
        ...userInfo,
        // 特别处理关键字段
        detect_auth_result: userInfo.detect_auth_result || this.state.detect_auth_result,
        wechat_openid: userInfo.wechat_openid || this.state.wechat_openid,
        wechat_nickname: userInfo.wechat_nickname || this.state.wechat_nickname,
        wechat_avatar_url: userInfo.wechat_avatar_url || this.state.wechat_avatar_url
      };
      common_vendor.index.setStorageSync("userInfo", userInfoToStore);
      if (userInfo.detect_auth_result !== void 0) {
        common_vendor.index.setStorageSync("detect_auth_result", userInfo.detect_auth_result);
      }
      if (userInfo.wechat_openid) {
        common_vendor.index.setStorageSync("wechat_openid", userInfo.wechat_openid);
      }
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/user-store.js:225", "用户信息已更新:", this.state);
        common_vendor.index.__f__("log", "at utils/user-store.js:226", "存储的用户信息:", userInfoToStore);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:229", "设置用户信息失败:", error);
    }
  }
  /**
   * 更新用户信息
   */
  updateUserInfo(updates) {
    try {
      Object.assign(this.state, updates);
      this.updateLastActiveTime();
      const currentUserInfo = common_vendor.index.getStorageSync("userInfo") || {};
      const updatedUserInfo = { ...currentUserInfo, ...updates };
      common_vendor.index.setStorageSync("userInfo", updatedUserInfo);
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/user-store.js:247", "用户信息已更新:", updates);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:250", "更新用户信息失败:", error);
    }
  }
  /**
   * 设置微信用户信息
   */
  setWechatUserInfo(wechatInfo) {
    try {
      this.state.wechatInfo = wechatInfo;
      if (!this.state.avatar && wechatInfo.avatarUrl) {
        this.state.avatar = wechatInfo.avatarUrl;
      }
      if (!this.state.nickname && wechatInfo.nickName) {
        this.state.nickname = wechatInfo.nickName;
      }
      common_vendor.index.setStorageSync("wechat_userInfo", wechatInfo);
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/user-store.js:273", "微信用户信息已设置:", wechatInfo);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:276", "设置微信用户信息失败:", error);
    }
  }
  /**
   * 清除用户信息
   */
  clearUserInfo() {
    try {
      Object.assign(this.state, defaultUserData);
      this.state.isLoading = false;
      this.state.isInitialized = true;
      common_vendor.index.removeStorageSync("userInfo");
      common_vendor.index.removeStorageSync("wechat_userInfo");
      common_vendor.index.removeStorageSync("openid");
      common_vendor.index.removeStorageSync("unionid");
      common_vendor.index.removeStorageSync("wechat_openid");
      common_vendor.index.removeStorageSync("detect_auth_result");
      common_vendor.index.removeStorageSync("token");
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/user-store.js:300", "用户信息已清除");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/user-store.js:303", "清除用户信息失败:", error);
    }
  }
  /**
   * 更新最后活跃时间
   */
  updateLastActiveTime() {
    this.state.lastActiveTime = (/* @__PURE__ */ new Date()).toISOString();
  }
  /**
   * 用户登出
   */
  /* async logout() {
    try {
      await logout();
      this.clearUserInfo();
      
      // 跳转到首页或登录页
      uni.switchTab({
        url: '/pages/index/index'
      });
      
      uni.showToast({
        title: '已退出登录',
        icon: 'success'
      });
    } catch (error) {
      uni.__f__('error','at utils/user-store.js:332','退出登录失败:', error);
      uni.showToast({
        title: '退出登录失败',
        icon: 'none'
      });
    }
  } */
  /**
   * 检查用户权限
   */
  hasPermission(permission) {
    return this.state.permissions.includes(permission);
  }
  /**
   * 检查用户角色
   */
  hasRole(role) {
    return this.state.roles.includes(role);
  }
  /**
   * 获取用户状态
   */
  getUserState() {
    return this.state;
  }
}
const userStore = new UserStore();
const userComputed = {
  // 是否已登录
  isLoggedIn: common_vendor.computed(() => userStore.state.status !== USER_STATUS.GUEST),
  // 是否已实名认证
  isVerified: common_vendor.computed(() => userStore.state.isVerified),
  // 用户显示名称
  displayName: common_vendor.computed(() => {
    return userStore.state.realName || userStore.state.nickname || userStore.state.wechatNickname || userStore.state.wechatInfo.nickName || "用户";
  }),
  // 用户头像
  displayAvatar: common_vendor.computed(() => {
    return userStore.state.avatar || userStore.state.wechatInfo.avatarUrl || "/static/tabbar/mine.png";
  })
};
exports.userComputed = userComputed;
exports.userStore = userStore;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/user-store.js.map
