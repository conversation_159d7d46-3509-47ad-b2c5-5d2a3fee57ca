<template>
  <view class="mediation-query-container">
    <!-- 未认证状态提示 -->
    <view class="auth-required" v-if="!isAuthenticated && !searchKeyword">
      <view class="filter-tabs">
        <view
          class="type-card"
          :class="{ active: selectedType === 0 }"
          @click="selectType(0)"
          >案件号查询</view
        >
        <view
          class="type-card"
          :class="{ active: selectedType === 1 }"
          @click="selectType(1)"
          >身份信息查询</view
        >
      </view>

      <view class="form-container" v-if="selectedType === 0">
        <view class="form-card">
          <view class="card-header">
            <!-- <i class="fas fa-file-alt"></i> -->
            <text class="card-title">调解案件号</text>
          </view>
          <view class="card-content">
            <uni-easyinput
              v-model="formData.caseNumber"
              placeholder="请输入调解案件编号"
              class="input-field"
            >
            </uni-easyinput>
          </view>
        </view>
      </view>
      <view class="form-container" v-if="selectedType === 1">
        <view class="form-card">
          <view class="card-header">
            <!-- <i class="fas fa-file-alt"></i> -->
            <text class="card-title">姓名</text>
          </view>
          <view class="card-content">
            <uni-easyinput
              v-model="formData.name"
              placeholder="请输入真实姓名"
              class="input-field"
            >
            </uni-easyinput>
          </view>
        </view>
        <view class="form-card">
          <view class="card-header">
            <!-- <i class="fas fa-file-alt"></i> -->
            <text class="card-title">身份证号</text>
          </view>
          <view class="card-content">
            <uni-easyinput
              v-model="formData.card"
              placeholder="请输入身份证号"
              class="input-field"
            >
            </uni-easyinput>
          </view>
        </view>
      </view>
		<view class="help-text">
			<i class="fas fa-info-circle"></i>{{inquireTip}}
		</view>
	  	<button class="auth-button" @click="handleSearch">查询案件</button>
		<!-- 身份信息查询结果显示 -->
      <view v-if="selectedType === 1 && mediationCase !== null && (formData.name || formData.card)">
		<view class="query-result-card">
            <view class="query-result-icon">
                <i class="fas fa-search"></i>
            </view>
            <view class="query-result-text">已查询到【{{mediationCase}}】个相关调解案件</view>
        </view>
	  </view>
    </view>
    <!-- 工单列表 - 仅在认证状态或有搜索关键词时显示 -->
    <view
      class="order-list"
      v-if="(isAuthenticated || searchKeyword) && filteredOrderList.length > 0"
    >
      <!-- 搜索框 -->
      <view class="search-box">
        <uni-easyinput
          class="search-input-wrapper"
          v-model="searchKeyword"
          placeholder="请输入调解案件号进行查询"
          confirmType="search"
          suffixIcon="search"
          :clearable="true"
          @confirm="handleSearch"
          @iconClick="handleSearch"
        ></uni-easyinput>
      </view>
      <view
        class="order-item"
        v-for="order in filteredOrderList"
        :key="order.id"
        @click="navigateToDetail(order.id,order.status)"
        hover-class="order-item-hover"
        :class="{ 'order-item-animation': isLoadingAnimate }"
      >
        <view class="order-info">
          <view class="order-id"
            >调解案件号: <text class="id-text">{{ order.id }}</text></view
          >
		  <view
            class="status-label"
            :style="{
              backgroundColor:
                statusStyles[order.statusCode]?.bgColor || '#999',
              color: '#fff',
            }"
            >{{ order.status }}</view>
        </view>
        <view class="order-date-icon">
          <view class="order-date">发起日期: {{ order.createDate }}</view>
          <view class="arrow-icon">›</view>
        </view>
      </view>
    </view>

    <!-- 加载中状态 -->
    <view
      class="loading-state"
      v-if="isLoading && !isRefreshing && (isAuthenticated || searchKeyword)"
    >
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 空状态 - 仅在认证状态或有搜索关键词时显示 -->
    <view
      class="empty-state"
      v-if="
        (isAuthenticated || searchKeyword) &&
        filteredOrderList.length === 0 &&
        !isLoading
      "
    >
      <image
        class="empty-image"
        src="/static/icons/empty.png"
        mode="aspectFit"
      ></image>
      <text class="empty-text">{{
        searchKeyword ? "未找到相关调解案件号" : "暂无信息"
      }}</text>
      <button class="refresh-button" @click="handleRefresh">刷新</button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { onPullDownRefresh } from "@dcloudio/uni-app";
import { api } from "@/utils/api.js";

// 不需要手动引入uni-easyinput组件，已通过easycom自动引入

// 搜索关键词
const searchKeyword = ref("");

// 是否正在刷新
const isRefreshing = ref(false);

// 是否正在加载
const isLoading = ref(false);

// 加载动画效果
const isLoadingAnimate = ref(false);

// 用户认证状态
const isAuthenticated = ref(true);

// 选中的tab（0: 案件号查询, 1: 身份信息查询）
const selectedType = ref(0);

const selectType = (typeIndex) => {
  selectedType.value = typeIndex;
};
// 如果selectedType = 0 显示“未实名认证用户仅可查询单条调解案件”，反之“仅显示案件数量统计”
const inquireTip = computed(() => {
  return selectedType.value === 0 ? '未实名认证用户仅可查询单条调解案件' : '仅显示案件数量统计';
});
const mediationCase = ref(null); // 调解案件数量，初始为null表示未查询
// 表单数据
const formData = reactive({
	caseNumber: '',      // 调解案件号
	name: '',           // 姓名
	card: ''            // 身份证号
})

// 工单列表数据
const orderList = ref([]);

// 模拟数据 - 实际项目中可替换为API响应
const mockOrderList = [
  {
    id: "MED20230001",
    status: "待确认",
    statusCode: "pending",
    createDate: "2023-11-01",
  },
  {
    id: "MED20230001",
    status: "进行中",
    statusCode: "processing",
    createDate: "2023-10-15",
  },
  {
    id: "MED20230001",
    status: "已完成",
    statusCode: "completed",
    createDate: "2023-09-28",
  },
  {
    id: "MED20230001",
    status: "已关闭",
    statusCode: "closed",
    createDate: "2023-09-10",
  },
  {
    id: "MED20230002",
    status: "进行中",
    statusCode: "processing",
    createDate: "2023-10-15",
  },
  {
    id: "MED20230003",
    status: "已完成",
    statusCode: "completed",
    createDate: "2023-09-28",
  },
  {
    id: "MED20230004",
    status: "已关闭",
    statusCode: "closed",
    createDate: "2023-09-10",
  },
];

// 状态样式配置
const statusStyles = {
  pending: {
    bgColor: "#faad14",
  },
  processing: {
    bgColor: "#1890ff",
  },
  completed: {
    bgColor: "#52c41a",
  },
  closed: {
    bgColor: "#999",
  },
};

// 根据搜索关键词过滤的工单列表
const filteredOrderList = computed(() => {
  if (!searchKeyword.value) {
    return orderList.value;
  }

  const keyword = searchKeyword.value.toLowerCase();
  return orderList.value.filter((order) =>
    order.id.toLowerCase().includes(keyword)
  );
});

// 生命周期钩子
onMounted(() => {
  console.log("调解查询页面已加载");
  checkAuthStatus();
  // fetchOrderList();

  // 添加加载动画效果
  setTimeout(() => {
    isLoadingAnimate.value = true;
  }, 200);
});

// 检查用户认证状态
const checkAuthStatus = () => {
  // 检查是否登录
  const token = uni.getStorageSync("token");
  if (token) {
    // 这里可以添加额外的认证状态检查逻辑
    // 目前简单地通过token是否存在来判断认证状态
    // 实际应用中可能需要检查用户信息中的认证字段

    // 模拟认证状态 - 实际项目中应通过API获取
    // 此处为演示，设置为false以显示未认证状态
    isAuthenticated.value = false;
  } else {
    isAuthenticated.value = false;
  }

  console.log("用户认证状态:", isAuthenticated.value ? "已认证" : "未认证");
};

// 下拉刷新处理
onPullDownRefresh(() => {
  console.log("触发下拉刷新");
  isRefreshing.value = true;
  checkAuthStatus(); // 刷新时重新检查认证状态
  // fetchOrderList();
});

// 获取工单列表
const fetchOrderList = () => {
  // 设置加载状态
  isLoading.value = true;

  // 如果不是下拉刷新，则显示加载中
  if (!isRefreshing.value) {
    uni.showLoading({
      title: "加载中...",
    });
  }

  // 调用API获取数据
  api.mediationQuery.getList(params).then((res) => {
      // 隐藏加载中
      if (!isRefreshing.value) {
        uni.hideLoading();
      }

      // 设置数据
      if (res && res.code === 0 && res.data) {
        // API请求成功，使用API返回的数据
        orderList.value = res.data;
      } else {
        // API请求成功但数据异常，使用模拟数据
        console.log("API返回数据异常，使用模拟数据");
        orderList.value = mockOrderList;
      }

      // 结束加载状态
      isLoading.value = false;

      // 结束下拉刷新状态
      if (isRefreshing.value) {
        uni.stopPullDownRefresh();
        isRefreshing.value = false;

        uni.showToast({
          title: "刷新成功",
          icon: "success",
          duration: 1500,
        });
      }
    })
    .catch((err) => {
      // 隐藏加载中
      if (!isRefreshing.value) {
        uni.hideLoading();
      }

      console.error("获取工单列表失败", err);

      // API请求失败，使用模拟数据
      console.log("API请求失败，使用模拟数据");
      orderList.value = mockOrderList;

      // 结束加载状态
      isLoading.value = false;

      // 结束下拉刷新状态
      if (isRefreshing.value) {
        uni.stopPullDownRefresh();
        isRefreshing.value = false;

        uni.showToast({
          title: "刷新成功",
          icon: "success",
          duration: 1500,
        });
      } else {
        // 显示错误提示
        /* uni.showToast({
          title: "获取数据失败，已显示缓存数据",
          icon: "none",
          duration: 2000,
        }); */
      }
    });
};

// 处理搜索
const handleSearch = async () => {
  console.log("开始搜索，选择类型:", selectedType.value);

  // 根据选择的查询类型进行不同的处理
  if (selectedType.value === 0) {
    // 案件号查询
    await handleCaseNumberSearch();
  } else {
    // 身份信息查询
    await handleIdentitySearch();
  }
};

// 处理案件号查询
const handleCaseNumberSearch = async () => {
  const caseNumber = formData.caseNumber.trim();

  // 验证输入
  if (!caseNumber) {
    uni.showToast({
      title: "请输入调解案件号",
      icon: "none",
      duration: 1500,
    });
    return;
  }

  try {
    // 显示加载状态
    uni.showLoading({
      title: "查询中...",
    });

    console.log("查询调解案件号:", caseNumber);

    // 调用API查询调解案件
    // 接口：GET /mediation_management/mediation_case/
    // 参数：case_number (调解案件号)
    // 返回：包含 case_number, case_status_cn, initiate_date 等字段的案件信息
    const result = await api.mediationQuery.getCaseByNumber(caseNumber);

    uni.hideLoading();

    if (result && result.success && result.data) {
      // 查询成功，处理返回的数据
      const caseData = result.data;
      console.log("查询到调解案件数据:", caseData);

      // 将查询结果转换为列表格式显示
      const caseList = Array.isArray(caseData) ? caseData : [caseData];

      // 转换数据格式以匹配现有的显示逻辑
      // 将API返回的数据转换为页面显示所需的格式
      orderList.value = caseList.map(item => ({
        id: item.case_number || caseNumber,           // 调解案件号 (case_number)
        status: item.case_status_cn || '未知状态',     // 案件状态中文 (case_status_cn)
        statusCode: getStatusCode(item.case_status_cn), // 状态码（用于样式显示）
        createDate: formatDate(item.initiate_date),    // 发起日期格式化 (initiate_date)
        rawData: item  // 保存原始数据，用于详情页面传递
      }));

      // 设置搜索关键词以显示结果
      searchKeyword.value = caseNumber;

      uni.showToast({
        title: `查询成功，找到${orderList.value.length}条记录`,
        icon: "success",
        duration: 1500,
      });

    } else {
      // 查询失败或无数据
      console.log("未查询到相关案件");
      orderList.value = [];
      searchKeyword.value = caseNumber; // 仍然设置搜索关键词以显示空状态

      uni.showToast({
        title: "未查询到相关调解案件",
        icon: "none",
        duration: 2000,
      });
    }

  } catch (error) {
    uni.hideLoading();
    console.error("查询调解案件失败:", error);

    uni.showToast({
      title: "查询失败，请稍后重试",
      icon: "none",
      duration: 2000,
    });
  }
};

// 处理身份信息查询
const handleIdentitySearch = async () => {
  const name = formData.name.trim();
  const idCard = formData.card.trim();

  // 验证输入
  if (!name || !idCard) {
    uni.showToast({
      title: "请输入完整的姓名和身份证号",
      icon: "none",
      duration: 1500,
    });
    return;
  }

  try {
    // 显示加载状态
    uni.showLoading({
      title: "查询中...",
    });

    console.log("查询身份信息:", { name, idCard });

    // 调用API查询案件数量
    const result = await api.mediationQuery.getCaseCountByIdentity({
      name: name,
      id_card: idCard
    });

    uni.hideLoading();

    if (result && result.success) {
      // 查询成功，更新案件数量
      const count = result.data?.count || 0;
      mediationCase.value = count;

      console.log("查询到调解案件数量:", count);

      uni.showToast({
        title: `查询成功，共${count}个相关案件`,
        icon: "success",
        duration: 1500,
      });

    } else {
      // 查询失败
      mediationCase.value = 0;

      uni.showToast({
        title: "未查询到相关案件",
        icon: "none",
        duration: 2000,
      });
    }

  } catch (error) {
    uni.hideLoading();
    console.error("查询案件数量失败:", error);

    // 设置默认值
    mediationCase.value = 0;

    uni.showToast({
      title: "查询失败，请稍后重试",
      icon: "none",
      duration: 2000,
    });
  }
};

// 手动刷新
const handleRefresh = () => {
  fetchOrderList();
};

// 工具函数：根据案件状态中文获取状态码
const getStatusCode = (statusCn) => {
  // 根据中文状态映射到状态码，用于样式显示
  const statusMap = {
    '待确认': 'pending',
    '进行中': 'processing',
    '已完成': 'completed',
    '已关闭': 'closed',
    '待处理': 'pending',
    '处理中': 'processing',
    '已结案': 'completed',
    '已撤销': 'closed'
  };

  return statusMap[statusCn] || 'pending';
};

// 工具函数：格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  try {
    // 处理不同的日期格式
    const date = new Date(dateStr);

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return dateStr; // 如果无法解析，返回原始字符串
    }

    // 格式化为 YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error('日期格式化失败:', error);
    return dateStr;
  }
};

// 跳转到工单详情页
const navigateToDetail = (orderId,orderStatus) => {
	/* uni.navigateTo({
		url: `/pages/work_order_detail/work_order_detail?id=${orderId}`,
		success: () => {
		console.log("跳转到工单详情页");
		},
		fail: (err) => {
		console.error("跳转失败", err);
		uni.showToast({
			title: "跳转失败",
			icon: "none",
		});
		},
	}); */
	console.log(orderId,'=====orderStatus',orderStatus);
	// 根据状态跳转到不同的页面
	if (orderStatus === "待确认") {
		uni.navigateTo({
			url: `/pages/work_order_detail/work_order_detail?id=${orderId}&status=${orderStatus}`,
		});
	} else if (orderStatus === "进行中") {
		uni.navigateTo({
			url: `/pages/solution_confirm/solution_confirm?orderId=${orderId}&status=${orderStatus}`,
		});
	}
  /*  else if (orderStatus === "已完成") {
		uni.navigateTo({
			url: `/pages/case_completed/case_completed?id=${orderId}&status=${orderStatus}`,
		});
	} else if (orderStatus === "已关闭") {
		uni.navigateTo({
			url: `/pages/work_order_detail/work_order_detail?id=${orderId}&status=${orderStatus}`,
		});
	} */
};
</script>

<style lang="scss" scoped>
:root {
	--primary-color: #3b7eeb;      // 主题蓝色
	--primary-light: #e6f0ff;      // 浅蓝色背景
	--success-color: #52c41a;
	--text-color:#333;
}
.mediation-query-container {
  min-height: 100vh;
  // height: calc(100% - 94px);
  overflow-y: auto;
  background-color: rgb(248, 250, 252);
  padding: 30rpx 30rpx 140rpx;
}

.search-box {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.search-input-wrapper {
  flex: 1;
}

/* 调整uni-easyinput的样式 */
:deep(.uni-easyinput__content) {
  height: 80rpx;
  background-color: #fff;
  border: 2rpx solid #eee;
  border-radius: 40rpx;
  padding: 0 20rpx;
}

:deep(.uni-icons) {
  color: #999;
}

:deep(.uni-easyinput__placeholder-class) {
  font-size: 28rpx;
}

:deep(.uni-easyinput__content-input) {
  height: 80rpx;
  font-size: 28rpx;
}

/* 未认证状态提示样式 */
.auth-required {
  // flex-direction: column;
  // align-items: center;
  // justify-content: center;
  // padding: 100rpx 50rpx;
  .filter-tabs {
    display: flex;
    text-align: center;
    // gap: 20rpx;
    overflow-x: auto;

    .type-card {
      // padding: 15rpx 30rpx;
      // background-color: #f8f8f8;
      // color: #666;
      // border-radius: 30rpx;
      // font-size: 26rpx;
      // white-space: nowrap;
      // transition: all 0.3s ease;

      background-color: #fff;
      color: #666;
      position: relative;
      flex: 1 1 0%;
      border-width: 2rpx;
      border-style: solid;
      border-color: #fff;
      box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;
      border-image: initial;
      font-size: 28rpx;
      padding: 16rpx 32rpx;
      border-radius: 12rpx;
    }

    .type-card.active {
      background-color: #2979ff;
      color: #ffffff;
    }
  }
	--card-background: #ffffff;    // 卡片背景色

	.form-container {
		display: flex;
		flex-direction: column;
		.form-card {
			overflow: hidden;
			.card-header {
			padding: 30rpx 30rpx 20rpx 10rpx;
			// border-bottom: 1rpx solid #f0f0f0;
			display: flex;
			align-items: center;

			.fas {
				margin-right: 8px;
				color: var(--primary-color);
			}

			.card-title {
				font-size: 30rpx;
				color: var(--text-primary);
				font-weight: 800;
				// flex: 1;
			}
			}
		}
	}
	.query-result-card {
		background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
		border: 2rpx solid #b7eb8f;
		border-radius: 24rpx;
		padding: 40rpx;
		margin-top: 40rpx;
		text-align: center;
		box-shadow: 0 8rpx 24rpx rgba(82, 196, 26, 0.1);
		animation: slideInUp 0.3s ease;
	}
	.query-result-icon {
		width: 96rpx;
		height: 96rpx;
		border-radius: 50%;
		background: linear-gradient(135deg, var(--success-color) 0%, #45a049 100%);
		display: flex	;
		align-items: center;
		justify-content: center;
		margin: 0 auto 32rpx;
		color: white;
		font-size: 40rpx;
	}
	.query-result-text {
		font-size: 32rpx;
		font-weight: 600;
		color: var(--text-color);
		margin-bottom: 16rpx;
	}
	.help-text{
		background-color: var(--primary-light);
		font-size: 26rpx;
		color: var(--primary-color);
		display: flex;
		align-items: center;
		border-width: 2rpx;
		border-style: solid;
		border-color: rgba(59, 126, 235, 0.2);
		border-image: initial;
		border-radius: 16rpx;
		padding: 24rpx 32rpx;
		margin: 32rpx 0;
		gap: 16rpx;
		.fas {
			margin-right: 10rpx;
			padding-top: 4rpx;
		}
	}
	.auth-button {
		width: 690rpx;
		height: 80rpx;
		line-height: 80rpx;
		background-color: #2979ff;
		color: #fff;
		font-size: 32rpx;
		border-radius: 10rpx;
		margin-top: 40rpx;
	}
}
:deep(.uni-easyinput__content) {
	border-radius: 12rpx !important;
}
.order-list {
  margin-bottom: 20rpx;
}

.order-item {
  margin-bottom: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: block;
  unicode-bidi: isolate
}

.order-item-hover {
  transform: scale(0.98);
  box-shadow: 0 1rpx 5rpx rgba(0, 0, 0, 0.1);
}

.order-item-animation {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.order-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.order-id {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.order-date {
  font-size: 28rpx;
  color: #666;
}

.order-date-icon {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.status-label {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  white-space: nowrap;
}

.arrow-icon {
  margin-left: 10rpx;
  font-size: 50rpx;
  color: #666;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #2979ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.refresh-button {
  width: 200rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  color: #2979ff;
  background-color: #fff;
  border: 2rpx solid #2979ff;
  border-radius: 35rpx;
}
</style>