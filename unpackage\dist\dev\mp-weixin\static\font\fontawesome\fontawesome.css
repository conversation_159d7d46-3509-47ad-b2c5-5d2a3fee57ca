/*!
 * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2023 Fonticons, Inc.
 */

/* 实心图标字体 */
@font-face {
  font-family: 'FontAwesome';
  font-style: normal;
  font-weight: 900;
  /* 这里应该是转换后的base64字体文件，为简化演示使用CDN链接 */
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
}

/* 品牌图标字体 */
@font-face {
  font-family: 'FontAwesomeBrands';
  font-style: normal;
  font-weight: 400;
  /* 这里应该是转换后的base64字体文件，为简化演示使用CDN链接 */
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-brands-400.woff2') format('woff2');
}

.fa,
.fas {
  font-family: 'FontAwesome';
  font-weight: 900;
}

.fab {
  font-family: 'FontAwesomeBrands';
  font-weight: 400;
}

/* 客服图标 */
.fa-headset:before {
  content: "\f590";
}

/* 电话图标 */
.fa-phone:before {
  content: "\f095";
}

/* 微信/聊天图标 */
.fa-comment:before {
  content: "\f075";
}

/* 微信图标 - 品牌图标 */
.fa-weixin:before {
  content: "\f1d7";
}

/* 加号图标 */
.fa-plus:before {
  content: "\2b";
}

/* 关闭图标 */
.fa-xmark:before {
  content: "\f00d";
}

/* 调解记录 */
.fa-clipboard-list:before {
  content: "\f46d";
}
/* 隐私政策 */
/* .fa-shield-alt:before {
  content: "\f132";
} */
/* 右侧箭头 */
.fa-chevron-right:before {
  content: "\f054";
}
/* 对勾 */
.fa-check-circle:before, .fa-circle-check:before {
  content: "\f058";
}
/* 搜索、调解查询 */
.fa-magnifying-glass:before, .fa-search:before {
  content: "\f002";
}
/* 案例展示 */
.fa-file-alt:before, .fa-file-lines:before, .fa-file-text:before {
  content: "\f15c";
}
/* 投诉建议 */
.fa-comments:before {
  content: "\f086";
}
/* 意见建议 */
.fa-lightbulb:before {
  content: "\f0eb";
}
/* 服务投诉 */
.fa-exclamation-triangle:before, .fa-triangle-exclamation:before, .fa-warning:before {
  content: "\f071";
}
.fa-comments:before {
  content: "\f086";
}
/* 反馈类型 */
.fa-list-dots:before, .fa-list-ul:before {
  content: "\f0ca";
}
/* 关联案件信息 */
.fa-file-alt:before, .fa-file-lines:before, .fa-file-text:before {
  content: "\f15c";
}
/* 具体类别 */
.fa-tags:before {
  content: "\f02c";
}
/* 编辑 */
.fa-edit:before, .fa-pen-to-square:before {
  content: "\f044";
}
/* 提交反馈 */
.fa-paper-plane:before {
  content: "\f1d8";
}
.fa-cog:before, .fa-gear:before {
  content: "\f013";
}
.fa-mobile-alt:before, .fa-mobile-screen-button:before {
  content: "\f3cd";
}
.fa-heart:before {
  content: "\f004";
}
.fa-circle-plus:before, .fa-plus-circle:before {
  content: "\f055";
}
.fa-circle-info:before, .fa-info-circle:before {
  content: "\f05a";
}
.fa-shield-alt:before, .fa-shield-halved:before {
  content: "\f3ed";
}
.fa-user-times:before, .fa-user-xmark:before {
  content: "\f235";
}
.fa-clock-four:before, .fa-clock:before {
  content: "\f017";
}

.fa-ban:before, .fa-cancel:before {
  content: "\f05e";
}

.fa-bug:before {
  content: "\f188";
}
.fa-ellipsis-h:before, .fa-ellipsis:before {
  content: "\f141";
}
.fa-check-circle:before, .fa-circle-check:before {
  content: "\f058";
}
.fa-clock-four:before, .fa-clock:before {
  content: "\f017";
}
.fa-file-pdf:before {
  content: "\f1c1";
}
.fa-file-image:before {
  content: "\f1c5";
}
.fa-file-pdf:before {
  content: "\f1c1";
}
.fa-expand:before {
  content: "\f065";
}
.fa-pen-nib:before {
  content: "\f5ad";
}
.fa-file-signature:before {
  content: "\f573";
}
.fa-spinner:before {
  content: "\f110"
}
.fa-spin {
  -webkit-animation-delay: var(--fa-animation-delay,0s);
  animation-delay: var(--fa-animation-delay,0s);
  -webkit-animation-direction: var(--fa-animation-direction,normal);
  animation-direction: var(--fa-animation-direction,normal)
}

.fa-spin {
  -webkit-animation-name: fa-spin;
  animation-name: fa-spin;
  -webkit-animation-duration: var(--fa-animation-duration,2s);
  animation-duration: var(--fa-animation-duration,2s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count,infinite);
  animation-iteration-count: var(--fa-animation-iteration-count,infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing,linear);
  animation-timing-function: var(--fa-animation-timing,linear)
}

@-webkit-keyframes fa-spin {
  0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg)
  }

  to {
      -webkit-transform: rotate(1turn);
      transform: rotate(1turn)
  }
}

@keyframes fa-spin {
  0% {
      -webkit-transform: rotate(0deg);
      transform: rotate(0deg)
  }

  to {
      -webkit-transform: rotate(1turn);
      transform: rotate(1turn)
  }
}
.fa-envelope:before {
  content: "\f0e0";
}
.fa-check:before {
  content: "\f00c";
}
.fa-file-contract:before {
  content: "\f56c";
}
.fa-certificate:before {
  content: "\f0a3";
}
.fa-gavel:before, .fa-legal:before {
  content: "\f0e3";
}
.fa-exclamation-triangle:before, .fa-triangle-exclamation:before, .fa-warning:before {
  content: "\f071";
}
.fa-copy:before {
  content: "\f0c5";
}
.fa-bank:before, .fa-building-columns:before, .fa-institution:before, .fa-museum:before, .fa-university:before {
  content: "\f19c";
}
.fa-weixin:before {
  content: "\f1d7";
}
.fa-alipay:before {
  content: "\f642";
}
.fa-gift:before {
  content: "\f06b";
}
.fa-file-signature:before {
  content: "\f573";
}
.fa-circle-question:before, .fa-question-circle:before {
  content: "\f059";
}