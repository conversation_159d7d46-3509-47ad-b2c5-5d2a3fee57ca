@echo off
chcp 65001 >nul
echo ===============================================
echo 🧹 深度清理uni-popup编译缓存工具
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 检查项目目录: %CD%
echo.

echo 📁 第一步：清理所有编译输出目录...
if exist "unpackage" (
    echo 正在删除 unpackage 目录...
    rmdir /s /q "unpackage" 2>nul
    if exist "unpackage" (
        echo ⚠️ 部分文件可能被占用，尝试强制删除...
        timeout /t 2 >nul
        rmdir /s /q "unpackage" 2>nul
    )
    if not exist "unpackage" (
        echo ✅ unpackage 目录已删除
    ) else (
        echo ❌ unpackage 目录删除失败，请手动删除
    )
) else (
    echo ℹ️ unpackage 目录不存在
)

echo.
echo 📦 第二步：清理node_modules中的uni-ui...
if exist "node_modules\@dcloudio\uni-ui" (
    echo 正在删除 node_modules\@dcloudio\uni-ui...
    rmdir /s /q "node_modules\@dcloudio\uni-ui" 2>nul
    echo ✅ uni-ui 依赖已删除
) else (
    echo ℹ️ node_modules\@dcloudio\uni-ui 不存在
)

echo.
echo 🗂️ 第三步：清理HBuilderX缓存...
if exist ".hbuilderx" (
    rmdir /s /q ".hbuilderx" 2>nul
    echo ✅ HBuilderX 缓存已删除
) else (
    echo ℹ️ .hbuilderx 目录不存在
)

if exist ".hbuilder" (
    rmdir /s /q ".hbuilder" 2>nul
    echo ✅ HBuilder 缓存已删除
) else (
    echo ℹ️ .hbuilder 目录不存在
)

echo.
echo 📄 第四步：清理临时文件...
if exist "*.tmp" del /q "*.tmp" 2>nul
if exist "*.temp" del /q "*.temp" 2>nul
if exist ".DS_Store" del /q ".DS_Store" 2>nul
echo ✅ 临时文件已清理

echo.
echo 🔧 第五步：验证关键文件...
if exist "pages.json" (
    echo ✅ pages.json 存在
) else (
    echo ❌ pages.json 不存在，请检查项目完整性
)

if exist "manifest.json" (
    echo ✅ manifest.json 存在
) else (
    echo ❌ manifest.json 不存在，请检查项目完整性
)

if exist "package.json" (
    echo ✅ package.json 存在
) else (
    echo ❌ package.json 不存在，请检查项目完整性
)

echo.
echo ===============================================
echo ✨ 深度清理完成！
echo ===============================================
echo.
echo 📋 接下来请按以下步骤操作：
echo.
echo 1️⃣ 关闭 HBuilderX（如果正在运行）
echo.
echo 2️⃣ 重新打开 HBuilderX
echo.
echo 3️⃣ 在 HBuilderX 中执行：
echo    - 点击菜单 "运行" → "清理项目"
echo    - 等待清理完成
echo.
echo 4️⃣ 重新编译项目：
echo    - 点击 "运行" → "运行到小程序模拟器" → "微信开发者工具"
echo.
echo 5️⃣ 如果仍有错误：
echo    - 检查控制台的具体错误信息
echo    - 确认是否还有其他组件引用了uni-ui
echo.
echo ⚠️ 重要提示：
echo   如果问题仍然存在，可能需要：
echo   - 重新创建项目并迁移代码
echo   - 或者联系技术支持
echo.
echo ===============================================
pause
