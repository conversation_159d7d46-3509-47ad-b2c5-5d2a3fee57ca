<template>
  <view class="work-order-detail-container">
    <!-- 案件基本信息 -->
    <view class="work-order-card">
      <view class="work-order-header">
        <view class="work-order-title">
          <text>调解案件号: </text>
          <text class="work-order-id">{{
            workOrderData.id || "MED20230001"
          }}</text>
        </view>
        <view class="work-order-status">
          <text
            class="status-label"
            :class="{ 'status-pending': workOrderData.status === '已完成' }"
            >{{ workOrderData.status || "已完成" }}</text
          >
        </view>
      </view>
      <view class="work-order-date"
        >发起日期: {{ workOrderData.createDate || "2023-11-01" }}</view
      >
    </view>

    <!-- 进度条 -->
    <view class="progress-bar">
      <view class="progress-steps">
        <view class="progress-step active">
          <view class="step-circle">1</view>
          <view class="step-line active"></view>
          <view class="step-label">调解确认</view>
        </view>
        <view class="progress-step active">
          <view class="step-circle">2</view>
          <view class="step-line active"></view>
          <view class="step-label">方案确认</view>
        </view>
        <view class="progress-step active">
          <view class="step-circle">3</view>
          <view class="step-line active"></view>
          <view class="step-label">协议签署</view>
        </view>
        <view class="progress-step active">
          <view class="step-circle">4</view>
          <view class="step-label">完成</view>
        </view>
      </view>
    </view>
    <!-- 协议公证 -->
    <view class="card mt-20">
      <!-- 装饰性背景元素 -->
      <view
        style="
          position: absolute;
          top: -15px;
          right: -15px;
          width: 80px;
          height: 80px;
          background: rgba(255, 193, 7, 0.1);
          border-radius: 50%;
        "
      >
      </view>
      <view
        style="
          position: absolute;
          bottom: -10px;
          left: -10px;
          width: 60px;
          height: 60px;
          background: rgba(255, 193, 7, 0.08);
          border-radius: 50%;
        "
      >
      </view>

      <view style="text-align: center; position: relative; z-index: 2">
        <view
          style="
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #ff8f00 0%, #f57c00 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            box-shadow: 0 6px 16px rgba(255, 143, 0, 0.3);
          "
        >
          <i
            class="fas fa-certificate"
            style="font-size: 30px; color: white"
          ></i>
        </view>
        <h3
          style="
            margin: 0 0 12px 0;
            color: #e65100;
            font-size: 20px;
            font-weight: 600;
          "
        >
          协议公证服务
        </h3>
        <view
          style="
            margin: 0;
            font-size: 15px;
            color: #bf360c;
            line-height: 1.6;
            font-weight: 500;
          "
          >为您的调解协议提供法律保障</view
        >
      </view>
    </view>

    <view class="info-section">
      <view class="section-title"
        ><i
          class="fas fa-question-circle"
          style="
            color: var(--primary-color);
            margin-right: 10px;
            font-size: 20px;
          "
        ></i
        >什么是协议公证？</view
      >
      <div
        style="
          background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
          border-radius: 12px;
          padding: 20px;
          margin-bottom: 20px;
          border: 1px solid rgba(0, 0, 0, 0.05);
        "
      >
        <p
          style="
            font-size: 15px;
            color: var(--text-color);
            line-height: 1.7;
            margin: 0;
            text-align: justify;
          "
        >
          协议公证是指公证机构根据当事人的申请，依照法定程序对已签署的调解协议内容进行审查并予以证明的活动。经公证的调解协议具有<text
            style="color: var(--primary-color)"
            >强制执行效力</text
          >，双方当事人均需严格按照协议条款约定进行履约。
        </p>
      </div>
    </view>

    <!-- 免费公证服务 -->
    <view class="free-service-card">
      <view class="service-header">
        <view class="service-icon">
          <i class="fas fa-gift"></i>
        </view>
        <view class="service-title-container">
          <text class="service-title">免费公证服务</text>
          <text class="service-subtitle">专业法律保障，零费用申请</text>
        </view>
      </view>
      <view class="service-content">

        <view class="price-section">
          <view class="price-item">
            <text class="price-label">公证服务费用</text>
            <view class="price-content">
              <text class="original-price">¥200.00</text>
              <text class="free-price">免费</text>
            </view>
          </view>
          <view class="price-item">
            <text class="price-label">您需支付的费用</text>
            <text class="final-price">¥0.00</text>
          </view>
        </view>

        <view class="service-tips">
          <i class="fas fa-info-circle"></i>
          <text>本调解中心与某市公证处合作提供， 经公证支付的费用</text>
        </view>
      </view>
    </view>

    <!-- 确认公证申请 -->
    <view class="notarization-decision">
      <view class="decision-header">
        <i
          class="fas fa-clipboard-check"
          style="
            color: var(--primary-color);
            font-size: 20px;
            margin-right: 8px;
          "
        ></i>
        <text class="decision-title">确认公证申请</text>
      </view>
      <view class="decision-description">
        请选择是否对本调解协议进行公证。公证后，
        协议具有法律强制执行力，双方当事人均需 严格按照协议条款进行履约。
      </view>

      <!-- 申请协议公证 -->
      <view 
        class="decision-option" 
        :class="{ 'selected-apply': notarizationChoice === 'apply' }"
        @click="handleSelectChoice('apply')"
      >
        <view class="option-header">
          <view class="radio-container">
            <view 
              class="radio-button"
              :class="{ 'radio-checked-apply': notarizationChoice === 'apply' }"
            >
              <i 
                v-if="notarizationChoice === 'apply'" 
                class="fas fa-check"
                style="color: white; font-size: 12px;"
              ></i>
            </view>
          </view>
          <text class="option-title">申请协议公证</text>
          <text class="option-badge">推荐</text>
        </view>
        <view class="option-description">
          为协议进行免费公证确认协议法律效力。公证后，
          协议具有强制执行力，双方当事人均需严格履行。
        </view>
      </view>

      <!-- 暂不公证 -->
      <view 
        class="decision-option" 
        :class="{ 'selected-skip': notarizationChoice === 'skip' }"
        @click="handleSelectChoice('skip')"
      >
        <view class="option-header">
          <view class="radio-container">
            <view 
              class="radio-button"
              :class="{ 'radio-checked-skip': notarizationChoice === 'skip' }"
            >
              <i 
                v-if="notarizationChoice === 'skip'" 
                class="fas fa-check"
                style="color: white; font-size: 12px;"
              ></i>
            </view>
          </view>
          <text class="option-title">暂不公证</text>
        </view>
        <view class="option-description">
          协议仅具有合同效力，但不具备强制执行效力。
          如后续需要强制执行，需另行申请公证或诉讼。
        </view>
      </view>
    </view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <button class="confirm-button" @click="handleConfirmChoice">
        <i class="fas fa-file-contract"></i>
        {{ notarizationChoice === 'apply' ? '确认公证申请' : '确认暂不公证' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { api } from "@/utils/api.js";

// 接收参数
const orderId = ref("");

// 工单数据
const workOrderData = ref({
  id: "MED20230001",
  status: "已完成",
  createDate: "2023-11-01",
  creditor: "某银行信用卡中心",
  amount: "50,000.00",
  reduction_amount: "10,000.00",
  monthlyRepayment: "2,083.33",
  notarizationStatus: "未公证",
  paymentMemo: "MED20230003_张先生_123456_华泰民商事调解中心",
});

const isCopied = ref(false);

// 公证选择状态('apply' | 'skip')
const notarizationChoice = ref('apply');
// 选择公证选项
const handleSelectChoice = async (choice) => {
  notarizationChoice.value = choice;
  
  // 记录操作日志
  const buttonName = choice === 'apply' ? '选择申请协议公证' : '选择暂不公证';
  await recordOperationLog('协议公证', buttonName);
};

// 确认选择
const handleConfirmChoice = async () => {
  if (notarizationChoice.value === 'apply') {
    await handleApplyNotarization();
  } else {
    await handleSkipNotarization();
  }
};

// 申请协议公证 - 移除选择状态设置，因为已在handleSelectChoice中处理
const handleApplyNotarization = async () => {
  // 记录操作日志
  await recordOperationLog('协议公证', '确认公证申请');
  
  uni.showModal({
    title: '确认申请',
    content: '确认申请免费协议公证服务？公证完成后协议将具有法律强制执行效力。',
    confirmText: '确认申请',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '申请中...'
        });
        
        try {
          // 检查token
          const token = uni.getStorageSync('token') || getCurrentPages()[0]?.$page?.options?.token;
          if (!token) {
            uni.hideLoading();
            uni.showToast({
              title: '请先登录',
              icon: 'none'
            });
            return;
          }
          
          // 调用申请公证API
          const result = await api.notarization.apply({
            workOrderId: orderId.value || workOrderData.value.id
          });
          
          uni.hideLoading();
          
          if (result.code === 0) {
            uni.showToast({
              title: '申请成功',
              icon: 'success'
            });
            
            // 更新工单状态
            workOrderData.value.notarizationStatus = '申请中';
            
            // 可以跳转到申请结果页面或继续下一步
            setTimeout(() => {
              uni.navigateTo({
                url: `/pages/notarization_result/notarization_result?id=${orderId.value}&type=apply`
              });
            }, 1500);
          } else {
            uni.showToast({
              title: result.message || '申请失败',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('申请公证失败:', error);
          uni.showToast({
            title: '申请失败，请稍后重试',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 暂不公证 - 移除选择状态设置，因为已在handleSelectChoice中处理
const handleSkipNotarization = async () => {
  // 记录操作日志
  await recordOperationLog('协议公证', '确认暂不公证');
  
  uni.showModal({
    title: '确认选择',
    content: '确认暂不进行协议公证？您可以在协议签署完成后随时申请公证服务。',
    confirmText: '确认',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        uni.showLoading({
          title: '处理中...'
        });
        
        try {
          // 检查token
          const token = uni.getStorageSync('token') || getCurrentPages()[0]?.$page?.options?.token;
          if (!token) {
            uni.hideLoading();
            uni.showToast({
              title: '请先登录',
              icon: 'none'
            });
            return;
          }
          
          // 调用跳过公证API
          const result = await api.notarization.skip({
            workOrderId: orderId.value || workOrderData.value.id
          });
          
          uni.hideLoading();
          
          if (result.code === 0) {
            uni.showToast({
              title: '已确认',
              icon: 'success'
            });
            
            // 更新工单状态
            workOrderData.value.notarizationStatus = '暂不公证';
            
            // 跳转到下一步或完成页面
            setTimeout(() => {
              uni.navigateTo({
                url: `/pages/case_completed/case_completed?id=${orderId.value}`
              });
            }, 1500);
          } else {
            uni.showToast({
              title: result.message || '操作失败',
              icon: 'none'
            });
          }
        } catch (error) {
          uni.hideLoading();
          console.error('跳过公证失败:', error);
          uni.showToast({
            title: '操作失败，请稍后重试',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 生命周期钩子
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options;

  if (options && options.id) {
    orderId.value = options.id;
    console.log("接收到工单ID:", orderId.value);
    fetchWorkOrderDetail(orderId.value);
  } else {
    // 默认使用模拟数据
    fetchWorkOrderDetail();
  }
});

// 获取调解确认数据
const fetchWorkOrderDetail = (id) => {
  if (id) {
    // 使用API获取数据
    uni.showLoading({
      title: "加载中...",
    });

    api.workOrder
      .getDetail(id)
      .then((res) => {
        uni.hideLoading();
        if (res.code === 0) {
          workOrderData.value = res.data;
        } else {
          uni.showToast({
            title: res.message || "获取调解确认失败",
            icon: "none",
          });
        }
      })
      .catch((err) => {
        uni.hideLoading();
        console.error("获取调解确认失败", err);
        uni.showToast({
          title: "获取调解确认失败",
          icon: "none",
        });
      });
  } else {
    // 使用模拟数据
    setTimeout(() => {
      console.log("调解确认数据已加载（模拟）");
    }, 500);
  }
};
</script>


<style lang="scss" scoped>
:root {
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --primary-color: #3b7eeb;
  --primary-dark: #2c62c9;
  --border-color: #e0e0e0;
  --transition-normal: all 0.3s ease;
}
.work-order-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.work-order-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.work-order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.work-order-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.work-order-status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

.status-label {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #52c41a;
  color: #fff;
}

.status-pending {
  background-color: #52c41a;
}

.work-order-date {
  font-size: 28rpx;
  color: #666;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.step-line {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}

.progress-step:last-child .step-line {
  display: none;
}
.step-line.active {
  background-color: #2979ff;
}
.step-label {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.progress-step.active .step-circle {
  background-color: #2979ff;
}

.progress-step.active .step-label {
  color: #2979ff;
  font-weight: bold;
}

.card {
  background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%);
  border: 2rpx solid #ffd54f;
  border-radius: 32rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  display: inline-flex;
}

/* 免费公证服务卡片样式 */
.free-service-card {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2rpx solid #b3e5fc;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}

.free-service-card::before {
  content: '';
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 120rpx;
  height: 120rpx;
  background: #d9f0ff;
  border-radius: 50%;
}

.service-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  .service-icon {
    width: 100rpx;
    height: 100rpx;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
    .fas {
      color: #fff; 
      font-size: 40rpx;
    }
  }
}

.service-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #3b7eeb;
  margin-bottom: 10rpx;
}

.service-subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.price-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.price-item:last-child {
  margin-bottom: 0;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.price-label {
  font-size: 28rpx;
  color: #333;
}

.price-content {
  display: flex;
  align-items: center;
}

.original-price {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 15rpx;
}

.free-price {
  font-size: 32rpx;
  color: var(--success-color);
  font-weight: bold;
}

.final-price {
  font-size: 36rpx;
  color: var(--success-color);
  font-weight: bold;
}

.service-tips {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--success-color);
  line-height: 1.5;
  .fas {
    color: var(--success-color);
    font-size: 28rpx; 
    margin-right: 12rpx;
  }
}

/* 公证决策模块样式更新 */
.notarization-decision {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.decision-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.decision-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.decision-description {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
}

.decision-option {
  border: 3rpx solid #e0e0e0;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fafafa;
}

.decision-option:last-child {
  margin-bottom: 0;
}

.decision-option:active {
  transform: scale(0.98);
}

/* 选中申请协议公证的样式 */
.decision-option.selected-apply {
  border-color: #3b7eeb;
  background: #3b7eeb;
}

.decision-option.selected-apply .option-title {
  color: white;
}

.decision-option.selected-apply .option-description {
  color: rgba(255, 255, 255, 0.9);
}

.decision-option.selected-apply .option-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 选中暂不公证的样式 */
.decision-option.selected-skip {
  border-color: #faad14;
  background: rgb(255, 247, 230);
}

.decision-option.selected-skip .option-title {
  color: #faad14;
}

.decision-option.selected-skip .option-description {
  color: #b8860b;
}

.option-header {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  position: relative;
}

.radio-container {
  margin-right: 20rpx;
}

.radio-button {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: white;
}

/* 申请协议公证选中时的单选框样式 */
.radio-button.radio-checked-apply {
  border-color: #3b7eeb;
  background: var(--primary-color);
}

/* 暂不公证选中时的单选框样式 */
.radio-button.radio-checked-skip {
  border-color: #faad14;
  background: #faad14;
}

.option-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.option-badge {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}

.option-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-left: 60rpx;
}

// 底部按钮
.action-buttons {
  margin-top: 40rpx;
  padding: 20rpx 0;
}

.confirm-button {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  background-color: #2979ff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  .fas {
    margin-right: 10rpx;
  }
}
</style>