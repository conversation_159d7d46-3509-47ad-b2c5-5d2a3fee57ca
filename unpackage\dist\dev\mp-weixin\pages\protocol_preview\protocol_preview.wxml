<view class="protocol-preview-container data-v-3f6102ab"><view wx:if="{{a}}" class="long-image-preview data-v-3f6102ab"><view class="preview-header data-v-3f6102ab"><view class="header-left data-v-3f6102ab"><view wx:if="{{b}}" class="api-status data-v-3f6102ab"><text class="status-text data-v-3f6102ab">✓ 数据已加载</text></view><view wx:elif="{{c}}" class="api-status error data-v-3f6102ab"><text class="status-text data-v-3f6102ab">⚠ {{d}}</text></view></view><view class="header-right data-v-3f6102ab"><text class="progress-text data-v-3f6102ab">{{e}}%</text><button class="refresh-btn data-v-3f6102ab" bindtap="{{g}}" disabled="{{h}}"><text class="{{['refresh-icon', 'data-v-3f6102ab', f && 'spinning']}}">⟳</text></button><button class="debug-btn data-v-3f6102ab" bindtap="{{i}}"><text class="debug-text data-v-3f6102ab">调试</text></button></view></view><scroll-view class="long-scroll-container data-v-3f6102ab" scroll-y="true" scroll-top="{{r}}" bindscroll="{{s}}" enhanced show-scrollbar="{{false}}"><view class="content-container data-v-3f6102ab"><view wx:if="{{j}}" class="loading-container data-v-3f6102ab"><view class="loading-icon data-v-3f6102ab"><view class="fas fa-spinner fa-spin data-v-3f6102ab"></view></view><text class="loading-text data-v-3f6102ab">{{k}}</text><view wx:if="{{l}}" class="api-loading-tip data-v-3f6102ab"><text class="tip-text data-v-3f6102ab">正在调用 api.solution.getDetails() 接口</text></view></view><view wx:elif="{{m}}" class="error-container data-v-3f6102ab"><view class="error-icon data-v-3f6102ab">⚠</view><text class="error-title data-v-3f6102ab">数据获取失败</text><text class="error-message data-v-3f6102ab">{{n}}</text><button class="retry-btn data-v-3f6102ab" bindtap="{{o}}"><text class="retry-text data-v-3f6102ab">重新获取</text></button></view><view wx:else class="images-container data-v-3f6102ab"><view wx:for="{{p}}" wx:for-item="image" wx:key="e" class="svg-container data-v-3f6102ab"><image src="{{image.a}}" mode="widthFix" class="{{['protocol-page', 'svg-image', 'data-v-3f6102ab', image.b && 'first-page']}}" bindload="{{image.c}}" binderror="{{image.d}}" lazy-load/></view><image wx:for="{{q}}" wx:for-item="image" wx:key="a" src="{{image.b}}" mode="widthFix" class="{{['protocol-page', 'data-v-3f6102ab', image.c && 'first-page']}}" bindload="{{image.d}}" binderror="{{image.e}}" lazy-load/></view></view></scroll-view><view class="progress-indicator data-v-3f6102ab"><view class="progress-bar data-v-3f6102ab"><view class="progress-fill data-v-3f6102ab" style="{{'width:' + t}}"></view></view></view></view></view>