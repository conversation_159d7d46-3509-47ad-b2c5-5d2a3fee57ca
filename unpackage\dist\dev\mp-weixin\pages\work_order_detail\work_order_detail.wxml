<view class="work-order-detail-container"><view class="work-order-card"><view class="work-order-header"><view class="work-order-title"><text>调解案件号: </text><text class="work-order-id">{{a}}</text></view><view class="work-order-status"><text class="{{['status-label', c && 'status-pending']}}">{{b}}</text></view></view><view class="work-order-date">发起日期: {{d}}</view><view wx:if="{{e}}" class="work-order-date">关闭日期: {{f}}</view></view><view class="progress-bar"><view class="progress-steps"><view class="progress-step active"><view class="step-circle">1</view><view class="step-line"></view><view class="step-label">调解确认</view></view><view class="progress-step"><view class="step-circle">2</view><view class="step-line"></view><view class="step-label">方案确认</view></view><view class="progress-step"><view class="step-circle">3</view><view class="step-line"></view><view class="step-label">协议签署</view></view><view class="progress-step"><view class="step-circle">4</view><view class="step-label">完成</view></view></view></view><view class="info-section"><view class="section-title">调解信息</view><view wx:for="{{g}}" wx:for-item="item" wx:key="c" class="info-item"><text class="info-label">{{item.a}}</text><text class="info-value">{{item.b}}</text></view></view><view class="info-section"><view class="section-title">相关文件</view><view wx:for="{{h}}" wx:for-item="file" wx:key="d" class="file-item"><view class="{{['fas', file.a]}}" style="{{'color:' + file.b}}"></view><view class="file-content"><text class="file-name">{{file.c}}</text></view></view></view><view wx:if="{{i}}" class="info-section work-closing"><view class="section-title"><view class="fas fa-info-circle"></view>关闭原因 </view><view class="section-tip"><text>{{j}}</text></view></view><view wx:else class="action-buttons"><button class="accept-button" bindtap="{{k}}"><view class="fas fa-check-circle"></view>接受调解</button><button class="reject-button" bindtap="{{l}}"><view class="fas fa-clock"></view>考虑一下</button></view></view>