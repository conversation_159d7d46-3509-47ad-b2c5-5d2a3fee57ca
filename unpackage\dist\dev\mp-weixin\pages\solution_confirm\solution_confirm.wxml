<view class="solution-confirm-container data-v-5489ef86"><view class="work-order-card data-v-5489ef86"><view class="work-order-header data-v-5489ef86"><view class="work-order-title data-v-5489ef86"><text class="data-v-5489ef86">调解案件号: </text><text class="work-order-id data-v-5489ef86">{{a}}</text></view><view class="work-order-status data-v-5489ef86"><text class="{{['status-label', 'data-v-5489ef86', c && 'status-processing']}}">{{b}}</text></view></view><view class="work-order-date data-v-5489ef86">发起日期: {{d}}</view></view><view class="progress-bar data-v-5489ef86"><view class="progress-steps data-v-5489ef86"><view class="progress-step completed data-v-5489ef86"><view class="step-circle data-v-5489ef86">1</view><view class="step-line completed data-v-5489ef86"></view><view class="step-label data-v-5489ef86">调解确认</view></view><view class="progress-step active data-v-5489ef86"><view class="step-circle data-v-5489ef86">2</view><view class="step-line data-v-5489ef86"></view><view class="step-label data-v-5489ef86">方案确认</view></view><view class="progress-step data-v-5489ef86"><view class="step-circle data-v-5489ef86">3</view><view class="step-line data-v-5489ef86"></view><view class="step-label data-v-5489ef86">协议签署</view></view><view class="progress-step data-v-5489ef86"><view class="step-circle data-v-5489ef86">4</view><view class="step-label data-v-5489ef86">完成</view></view></view></view><view class="solutions-container data-v-5489ef86"><view wx:if="{{e}}" class="data-v-5489ef86"><view wx:for="{{f}}" wx:for-item="plan" wx:key="f" class="{{['solution-card', 'data-v-5489ef86', plan.g && 'selected']}}" bindtap="{{plan.h}}"><view class="solution-header data-v-5489ef86"><view class="solution-title-wrap data-v-5489ef86"><text class="solution-title data-v-5489ef86">{{plan.a}}</text></view><view class="solution-select data-v-5489ef86"><view class="{{['radio-button', 'data-v-5489ef86', plan.c && 'selected']}}"><view wx:if="{{plan.b}}" class="radio-inner data-v-5489ef86"></view></view></view></view><view class="solution-content data-v-5489ef86"><view wx:for="{{plan.d}}" wx:for-item="config" wx:key="c" class="config-detail data-v-5489ef86"><view class="solution-item data-v-5489ef86"><text class="solution-label data-v-5489ef86">{{config.a}}</text><text class="solution-value data-v-5489ef86">{{config.b}}</text></view></view></view><view class="solution-footer data-v-5489ef86"><text class="view-detail data-v-5489ef86" catchtap="{{plan.e}}">查看详情</text></view></view></view><view wx:if="{{g}}" class="empty-state data-v-5489ef86"><text class="empty-text data-v-5489ef86">暂无方案数据</text></view></view><view class="action-buttons data-v-5489ef86"><button class="confirm-button data-v-5489ef86" bindtap="{{l}}"><text wx:if="{{h}}" class="data-v-5489ef86"> 确认选择{{i}}</text><text wx:elif="{{j}}" class="data-v-5489ef86"> 确认选择{{k}}</text><text wx:else class="data-v-5489ef86"> 确认选择 </text></button></view></view>