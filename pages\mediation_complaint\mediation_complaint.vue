<template>
	<view class="mediation-complaint-container">
		<web-view src="https://prod-9gwr0pqvc081f7f4-1370735801.tcloudbaseapp.com/"></web-view>
		<!-- <web-view src="https://postpay-2g5hm2oxbbb721a4-1258211818.tcloudbaseapp.com/jump-mp.html"></web-view> -->
		<view class="gradient-bg">
			<view class="handle-box">
				<view class="handle-icon">
					<i class="fas fa-comments"></i>
				</view>
				<h2 class="handle-title">意见反馈与服务投诉</h2>
				<p class="handle-text">您的意见是我们改进服务的动力，请告诉我们您的想法</p>
			</view>
		</view>

		<!-- 反馈类型选择区域 -->
		<view class="feedback-types">
			<view class="form-card">
				<view class="card-header">
					<!-- <text class="card-icon">📋</text> -->
					<i class="fas fa-list-ul"></i>
					<text class="card-title">反馈类型</text>
				</view>
				<view class="type-card-content">
					<!-- 意见建议按钮 -->
					<view class="type-card" 
						:class="{ active: selectedType === 0 }" 
						@click="selectType(0)">
						<view class="feedback-type-icon suggestion-icon">
							<i class="fas fa-lightbulb"></i>
						</view>
						<text class="type-text">意见建议</text>
					</view>
					
					<!-- 服务投诉按钮 -->
					<view class="type-card" 
						:class="{ active: selectedType === 1 }" 
						@click="selectType(1)">
						<view class="feedback-type-icon complaint-icon">
							<i class="fas fa-exclamation-triangle"></i>
						</view>
						<text class="type-text">服务投诉</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 表单区域 - 每个区域都是独立卡片 -->
		<view class="form-container" v-if="selectedType !== null">
			
			<!-- 关联案件信息卡片 -->
			<view class="form-card">
				<view class="card-header">
					<!-- <text class="card-icon">📋</text> -->
					<i class="fas fa-file-alt"></i>
					<text class="card-title">关联案件信息</text>
					<text class="optional-tag">（可选）</text>
				</view>
				<view class="card-content">
					<uni-easyinput 
					    prefixIcon="search"
						v-model="formData.caseNumber"
						placeholder="请输入调解案件编号"
						class="input-field">
					</uni-easyinput>
					<view class="help-text">
						<i class="fas fa-info-circle"></i>填写案件编号有助于我们更快定位和处理您的反馈
					</view>
				</view>
			</view>

			<!-- 具体类别卡片 -->
			<view class="form-card">
				<view class="card-header">
					<!-- <text class="card-icon">🏷️</text> -->
					<i class="fas fa-tags"></i>
					<text class="card-title">具体类别</text>
				</view>
				<view class="card-content">
					<view class="category-grid">
						<view class="category-card" 
							v-for="(category, index) in currentCategories" 
							:key="category.value"
							:class="{ active: selectedCategory === category.value }"
							@click="selectCategory(category)">
							
							<!-- 左侧图标 -->
							<view class="category-icon">
								<i class="fas" :class="category.icon" :style="{ color: category.color }"></i>
							</view>
							
							<!-- 中间文字区域 -->
							<view class="category-info">
								<text class="category-title">{{ category.text }}</text>
								<text class="category-desc">{{ category.desc }}</text>
							</view>
							
							<!-- 右侧单选框 -->
							<view class="category-radio">
								<view class="radio-inner" :class="{ checked: selectedCategory === category.value }">
									<text class="radio-dot" v-if="selectedCategory === category.value">●</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 详细描述卡片 -->
			<view class="form-card">
				<view class="card-header">
					<!-- <text class="card-icon">✏️</text> -->
					<i class="fas fa-edit"></i>
					<text class="card-title">详细描述</text>
				</view>
				<view class="card-content">
					<view class="textarea-container">
						<uni-easyinput 
							type="textarea" 
							v-model="formData.description"
							:placeholder="currentPlaceholder"
							autoHeight
							class="textarea-field">
						</uni-easyinput>
						<text class="char-count">{{ formData.description.length }}/500</text>
					</view>
					<view class="help-text">
						<i class="fas fa-info-circle"></i>详细的描述有助于我们更好地理解和解决问题
					</view>
				</view>
			</view>

			<!-- 联系方式卡片 -->
			<view class="form-card">
				<view class="card-header">
					<!-- <text class="card-icon">📱</text> -->
					<i class="fas fa-phone"></i>
					<text class="card-title">联系方式</text>
					<text class="optional-tag">（可选）</text>
				</view>
				<view class="card-content">
					<uni-easyinput 
					    prefixIcon="phone"
						v-model="formData.contactPhone"
						placeholder="请输入您的手机号码"
						type="number"
						maxlength="11"
						class="input-field">
					</uni-easyinput>
					<view class="help-text">
						<i class="fas fa-shield-alt"></i>您的联系方式仅用于发送调解相关信息，我们承诺严格保护您的隐私
					</view>
				</view>
			</view>

			<!-- 提交按钮卡片 -->
			<button class="submit-btn" @click="handleSubmit">
				<!-- <text class="submit-icon">📤</text> -->
				<i class="fas fa-paper-plane"></i>
				<text class="submit-text">提交反馈</text>
			</button>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue';
// Tab状态管理（当前只有一个反馈类型tab）
const activeTab = ref(0)

// 选中的反馈类型（0: 意见建议, 1: 服务投诉）
const selectedType = ref(0)

// 选中的具体类别
const selectedCategory = ref('')

// 表单数据
const formData = reactive({
	caseNumber: '',      // 关联案件编号
	description: '',     // 详细描述
	contactPhone: ''     // 联系电话
})

// ==================== 配置数据 ====================

// 类别配置：不同反馈类型对应不同的具体类别选项，包含图标和颜色
const categoryConfig = {
	suggestion: [
		{ 
			value: 'process_optimization', 
			text: '流程优化建议', 
			desc: '对调解流程改进的建议', 
			icon: 'fa-cog', 
			color: '#52c41a' 
		},
		{ 
			value: 'improvement', 
			text: '功能改进建议', 
			desc: '对系统功能的优化建议', 
			icon: 'fa-mobile-alt', 
			color: '#1890ff' 
		},
		{ 
			value: 'service_praise', 
			text: '服务表扬', 
			desc: '对优质服务的表扬', 
			icon: 'fa-heart', 
			color: '#eb2f96'
		},
		{ 
			value: 'other_suggestion', 
			text: '其他建议', 
			desc: '其他类型的意见建议', 
			icon: 'fa-plus-circle', 
			color: '#999'
		}
	],
	complaint: [
		{ 
			value: 'service_attitude', 
			text: '服务态度问题', 
			desc: '工作人员态度不佳', 
			icon: 'fa-user-times', 
			color: '#f5222d',
		},
		{ 
			value: 'processing_time', 
			text: '处理时间过长', 
			desc: '调解处理效率低下', 
			icon: 'fa-clock',
			color: '#faad14',
		},
		{ 
			value: 'plan_unreasonable', 
			text: '方案不合理', 
			desc: '调解方案存在问题', 
			icon: 'fa-ban', 
			color: '#722ed1',
		},
		{ 
			value: 'system_technology', 
			text: '系统技术问题', 
			desc: '页面异常、功能故障等', 
			icon: 'fa-bug', 
			color: '#fa541c',
		},
		{ 
			value: 'other_complaint', 
			text: '其他投诉', 
			desc: '其他类型的服务投诉', 
			icon: 'fa-ellipsis-h', 
			color: '#999',
		}
	]
}

// ==================== 计算属性 ====================

// 根据当前选中的反馈类型返回对应的类别选项
const currentCategories = computed(() => {
	if (selectedType.value === 0) {
		return categoryConfig.suggestion
	} else if (selectedType.value === 1) {
		return categoryConfig.complaint
	}
	return []
})

// 根据当前反馈类型返回对应的描述占位符
const currentPlaceholder = computed(() => {
	if (selectedType.value === 0) {
		return '请详细描述您的意见或问题，我们会认真对待每一条反馈...'
	} else if (selectedType.value === 1) {
		return '请详细描述您的投诉或问题，我们承诺对待每一条反馈...'
	}
	return '请详细描述您的反馈内容...'
})

// ==================== 事件处理方法 ====================

// Tab切换处理（当前版本暂时保留，未来可扩展）
/* const switchTab = (tabIndex) => {
	console.log('切换Tab:', tabIndex)
	activeTab.value = tabIndex
} */

// 反馈类型选择处理
const selectType = (typeIndex) => {
	console.log('选择反馈类型:', typeIndex === 0 ? '意见建议' : '服务投诉')
	selectedType.value = typeIndex
	// 切换类型时重置类别选择
	selectedCategory.value = ''
}

// 具体类别选择处理
const selectCategory = (category) => {
	console.log('选择类别:', category.text)
	selectedCategory.value = category.value
}

// 返回按钮处理
const handleGoBack = () => {
	uni.navigateBack()
}

// ==================== 表单验证逻辑 ====================

// 表单验证方法
const validateForm = () => {
	// 验证反馈类型
	if (selectedType.value === null) {
		uni.showToast({
			title: '请选择反馈类型',
			icon: 'none'
		})
		return false
	}

	// 验证具体类别
	if (!selectedCategory.value) {
		uni.showToast({
			title: '请选择具体类别',
			icon: 'none'
		})
		return false
	}

	// 验证详细描述
	if (!formData.description.trim()) {
		uni.showToast({
			title: '请填写详细描述',
			icon: 'none'
		})
		return false
	}

	if (formData.description.trim().length < 10) {
		uni.showToast({
			title: '描述内容至少10个字符',
			icon: 'none'
		})
		return false
	}

	if (formData.description.length > 500) {
		uni.showToast({
			title: '描述内容不能超过500字符',
			icon: 'none'
		})
		return false
	}

	// 验证联系电话（可选，如果填写则验证格式）
	if (formData.contactPhone && formData.contactPhone.trim()) {
		const phoneReg = /^1[3-9]\d{9}$/
		if (!phoneReg.test(formData.contactPhone)) {
			uni.showToast({
				title: '请输入正确的手机号码',
				icon: 'none'
			})
			return false
		}
	}

	return true
}

// ==================== 表单提交处理 ====================

// 提交表单处理
const handleSubmit = () => {
	console.log('开始提交表单')
	
	// 表单验证
	if (!validateForm()) {
		return
	}

	// 构建提交数据
	const submitData = {
		type: selectedType.value === 0 ? 'suggestion' : 'complaint',
		category: selectedCategory.value,
		caseNumber: formData.caseNumber.trim() || null,
		description: formData.description.trim(),
		contactPhone: formData.contactPhone.trim() || null,
		submitTime: new Date().toISOString()
	}

	console.log('提交数据:', submitData)

	// 显示加载状态
	uni.showLoading({
		title: '提交中...',
		mask: true
	})

	// 模拟API提交（实际项目中替换为真实API调用）
	setTimeout(() => {
		uni.hideLoading()
		
		// 提交成功反馈
		const successTitle = selectedType.value === 0 ? '意见已提交' : '投诉已提交'
		uni.showToast({
			title: successTitle,
			icon: 'success',
			duration: 2000,
			success: () => {
				// 延迟返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			}
		})
	}, 1500)
}

// ==================== 生命周期钩子 ====================

onMounted(() => {
	console.log('投诉建议页面已加载')
})
</script>

<style lang="scss" scoped>
// ==================== CSS变量定义 ====================
:root {
	--primary-color: #3b7eeb;      // 主题蓝色
	--primary-light: #e6f0ff;      // 浅蓝色背景
	--text-primary: #333;          // 主要文字颜色
	--text-secondary: #666;        // 次要文字颜色
	--text-placeholder: #999;      // 占位符文字颜色
	--border-color: #e0e0e0;       // 边框颜色
	--background-color: #f5f5f5;   // 页面背景色
	--card-background: #ffffff;    // 卡片背景色
	--shadow-light: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);  // 轻阴影
	--shadow-card: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);   // 卡片阴影
	--border-radius: 16rpx;        // 卡片圆角
	--primary-dark: #2c62c9; // tab选中颜色
	--text-light:#999;
	--transition-normal:all 0.3s ease;
}

// ==================== 页面整体布局 ====================
.mediation-complaint-container {
	min-height: 100vh;
	background-color: var(--background-color);
	padding: 30rpx 20rpx;
}

// ==================== 页面头部样式 ====================
.gradient-bg {
	background: linear-gradient(135deg, #e6f0ff 0%, #e6f0ff 100%);
	border: none;
    margin-bottom: 40rpx;
	box-shadow: rgba(0, 0, 0, 0.04) 0 4rpx 16rpx;
    margin-bottom: 30rpx;
    border-radius: 24rpx;
    padding: 36rpx;
    transition: var(--transition-normal);
	.handle-box{
		text-align: center;
		padding: 20rpx 10rpx 40rpx;
	}
	.handle-icon{
		width: 100rpx;
		height: 100rpx;
		background-color: var(--primary-color);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 30rpx;
		.fas{
			font-size: 44rpx;
			color: white;
		}
	}
	.handle-title{
		margin: 0 0 16rpx 0;
		color: var(--primary-color);
		font-size: 36rpx;
		font-weight: 600;
	}
	.handle-text{
		margin: 0;
		font-size: 26rpx;
		color: var(--text-secondary);
		line-height: 1.5;
	}
}
// ==================== 反馈类型选择区域样式 ====================
.feedback-types {
	padding: 0 0 30rpx;
	display: flex;
	gap: 20rpx;
	.form-card{
		flex: 1;
	}
	.type-card-content{
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 28rpx; 
    	padding: 36rpx;
	}
	.type-card {
		flex: 1;
		background-color: var(--card-background);
		border-radius: var(--border-radius);
		border-radius: 16rpx;
		padding: 40rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 20rpx;
		border: 2rpx solid var(--border-color);
		box-shadow: var(--shadow-light);
		transition: var(--transition-normal);
		
		&.active {
			border-color: var(--primary-color);
			background-color: var(--primary-light);
    		border-top: 8rpx solid var(--primary-color);
			
			.feedback-type-icon {
				background-color: var(--primary-color);
				
				.fas {
					transform: scale(1.1);
					color: white;
				}
			}
			
			.type-text {
				color: var(--primary-color);
				font-weight: 600;
			}
		}
		.feedback-type-icon {
			width: 80rpx;
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: var(--primary-light);
			border-radius: 50%;
			margin: 0px auto 20rpx;
			transition: 0.3s;
		}
		.feedback-type-icon .fas {
			font-size: 36rpx;
			color: var(--primary-color);
			transition: 0.3s;
		}
		.type-text {
			font-size: 28rpx;
			color: var(--text-primary);
			font-weight: 500;
			transition: var(--transition-normal);
		}
	}
}
// ==================== 表单容器样式 ====================
.form-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}
// ==================== 表单卡片样式 ====================
.form-card {
	// margin: 20rpx 30rpx 0;
	background-color: var(--card-background);
	border-radius: var(--border-radius);
	box-shadow: var(--shadow-light);
	// border-radius: 16rpx;
	overflow: hidden;
	.card-header {
		padding: 30rpx 30rpx 20rpx 30rpx;
		// border-bottom: 1rpx solid #f0f0f0;
		display: flex;
		align-items: center;
		
		.fas {
			margin-right: 8px;
    		color: var(--primary-color);
		}
		
		.card-title {
			font-size: 30rpx;
			color: var(--text-primary);
			font-weight: 800;
			// flex: 1;
		}
		
		.optional-tag {
			font-size: 24rpx;
			color: var(--text-placeholder);
			// background-color: #f0f0f0;
			padding: 4rpx 12rpx;
			// border-radius: 12rpx;
		}
	}
	
	.card-content {
		padding: 30rpx;
		
		.help-text {
			font-size: 24rpx;
			color: var(--text-light);
			margin-top: 15rpx;
    		margin-bottom: 0;
			display: flex;
			
			.fas{
				margin-right: 10rpx;
				padding-top: 4rpx;
			}
		}
	}
}

// ==================== 输入框样式 ====================
.input-field {
	border-radius: 12rpx;
	width: 100%;
	height: 96rpx;
	line-height: 96rpx;
}

// ==================== 类别网格布局样式 ====================
.category-grid {
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.category-card {
	display: flex;
	align-items: center;
	padding: 32rpx;
	background-color: white;
	border-radius: 24rpx;
	// border: 2rpx solid transparent;
	border-width: 1px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.06);
	transition: 0.3s;
	position: relative;
	
	// 中间文字信息区域
	.category-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		margin-right: 20rpx;
		
		.category-title {
			font-size: 30rpx;
			color: var(--text-primary);
			margin-bottom: 6rpx;
			font-weight: 500;
			line-height: 1.2;
		}
		
		.category-desc {
			font-size: 24rpx;
			color: var(--text-secondary);
			line-height: 1.3;
		}
	}
	// 选中状态的左边框加粗效果
	&.active {
		background-color: var(--primary-light);
		border-left: 10rpx solid var(--primary-color);
		box-shadow: rgba(59, 126, 235, 0.15) 0px 4px 12px;
		border-color: var(--primary-color);
		padding-left: 21rpx; // 减少左内边距以保持对齐
		
		.category-title {
			color: var(--primary-color);
			font-weight: bold;
		}
		
		.category-desc {
			color: var(--primary-color);
			opacity: 0.8;
		}
		
		.radio-inner {
			border-color: var(--primary-color);
			background-color: var(--primary-color);
			
			.radio-dot {
				color: white;
			}
		}
	}
	
	// 左侧图标区域
	.category-icon {
		width: 60rpx;
		height: 60rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
		flex-shrink: 0;
		background: rgb(248, 249, 250);
		border-radius: 10rpx;
		transition: 0.3s;
		
		.fas {
			font-size: 36rpx;
    		transition: 0.3s;
		}
	}
	
	// 右侧单选框区域
	.category-radio {
		flex-shrink: 0;
		
		.radio-inner {
			width: 40rpx;
			height: 40rpx;
			border: 2rpx solid var(--border-color);
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;
			
			&.checked {
				border-color: var(--primary-color);
				background-color: var(--primary-color);
			}
			
			.radio-dot {
				font-size: 32rpx;
				color: white;
				transition: 0.3s;
			}
		}
	}
}

// ==================== 文本域样式 ====================
.textarea-container {
	position: relative;
	
	.textarea-field {
		min-height: 200rpx;
		border-radius: 12rpx;
		width: 100%;
	}
	
	.char-count {
		position: absolute;
		bottom: 20rpx;
		right: 20rpx;
		font-size: 24rpx;
		color: var(--text-placeholder);
		background-color: rgba(255, 255, 255, 0.8);
		padding: 4rpx 8rpx;
		border-radius: 8rpx;
	}
}

// ==================== 提交按钮样式 ====================
.submit-btn {
	margin-top: 60rpx;
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, var(--primary-color) 0%, #2979ff 100%);
	color: white;
	border-radius: 16rpx;
	border: none;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	font-size: 32rpx;
	font-weight: 500;
	box-shadow: 0 8rpx 20rpx rgba(59, 126, 235, 0.3);
	transition: all 0.3s ease;
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 12rpx rgba(59, 126, 235, 0.3);
	}
	
	.submit-icon {
		font-size: 28rpx;
	}
	
	.submit-text {
		font-size: 32rpx;
	}
}

// ==================== 全局组件样式覆盖 ====================
:deep(.uni-easyinput__content) {
	border-color: var(--border-color) !important;
	border-radius: 12rpx !important;
	background-color: #fafafa !important;
}

:deep(.uni-easyinput__content-input) {
	font-size: 28rpx !important;
	color: var(--text-primary) !important;
}

:deep(.uni-easyinput__placeholder-class) {
	color: var(--text-placeholder) !important;
}

:deep(.uni-easyinput__content-textarea) {
	font-size: 28rpx !important;
	color: var(--text-primary) !important;
}
</style>