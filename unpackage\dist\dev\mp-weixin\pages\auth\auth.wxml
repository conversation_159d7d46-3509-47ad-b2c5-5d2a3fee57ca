<view class="container data-v-98e148d1"><view class="header data-v-98e148d1"><text class="title data-v-98e148d1">人脸识别认证</text></view><view class="notification data-v-98e148d1"><text class="data-v-98e148d1">当前业务需要人脸识别验证</text></view><view class="card-icon data-v-98e148d1"><image src="{{a}}" class="icon data-v-98e148d1"/></view><view class="form-item data-v-98e148d1"><text class="label data-v-98e148d1">姓名</text><input class="{{['input-value', 'data-v-98e148d1', b && 'input-error', c && 'input-disabled']}}" placeholder="请输入姓名" disabled="{{d}}" bindinput="{{e}}" bindblur="{{f}}" value="{{g}}"/><text wx:if="{{h}}" class="error-text data-v-98e148d1">{{i}}</text></view><view class="form-item data-v-98e148d1"><text class="label data-v-98e148d1">身份证号码</text><input class="{{['input-value', 'data-v-98e148d1', j && 'input-error', k && 'input-disabled']}}" placeholder="请输入身份证号码" disabled="{{l}}" bindinput="{{m}}" bindblur="{{n}}" maxlength="18" value="{{o}}"/><text wx:if="{{p}}" class="error-text data-v-98e148d1">{{q}}</text></view><view class="footer data-v-98e148d1"><view class="agreement data-v-98e148d1"><checkbox-group class="data-v-98e148d1" bindchange="{{s}}"><checkbox class="data-v-98e148d1" checked="{{r}}" color="#2979FF"/></checkbox-group><text class="data-v-98e148d1">我已阅读并同意遵循</text><text class="link data-v-98e148d1" bindtap="{{t}}">《用户服务协议》</text><text class="data-v-98e148d1">和</text><text class="link data-v-98e148d1" bindtap="{{v}}">《个人信息保护政策》</text></view><button disabled="{{x}}" class="{{['action-btn', 'data-v-98e148d1', y && 'disabled']}}" bindtap="{{z}}">{{w}}</button></view></view>