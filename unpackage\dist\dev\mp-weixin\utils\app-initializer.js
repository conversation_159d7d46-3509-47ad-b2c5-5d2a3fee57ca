"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const utils_performanceOptimizer = require("./performance-optimizer.js");
const utils_wechatAuth = require("./wechat-auth.js");
class AppInitializer {
  constructor() {
    this.initialized = false;
    this.initPromise = null;
  }
  /**
   * 初始化应用
   */
  async init() {
    if (this.initialized) {
      return this.initPromise;
    }
    if (this.initPromise) {
      return this.initPromise;
    }
    this.initPromise = this._performInit();
    return this.initPromise;
  }
  /**
   * 执行初始化
   */
  async _performInit() {
    const monitor = utils_performanceOptimizer.performanceMonitor;
    monitor.start("app-init");
    try {
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:45", "开始应用初始化...");
      }
      await this._initPerformanceOptimization();
      await this._initWechatAuth();
      await this._initGlobalErrorHandler();
      await this._initPageLifecycle();
      this.initialized = true;
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:63", "应用初始化完成");
      }
      monitor.end("app-init");
      return { success: true };
    } catch (error) {
      monitor.end("app-init");
      common_vendor.index.__f__("error", "at utils/app-initializer.js:70", "应用初始化失败:", error);
      return { success: false, error };
    }
  }
  /**
   * 初始化性能优化
   */
  async _initPerformanceOptimization() {
    try {
      utils_performanceOptimizer.initPerformanceOptimization();
      utils_performanceOptimizer.memoryOptimizer.addCleanupTask(() => {
        try {
          const tokenExpireTime = common_vendor.index.getStorageSync("token_expire_time");
          if (tokenExpireTime && Date.now() > tokenExpireTime) {
            utils_wechatAuth.wechatAuth.clearLoginInfo();
          }
        } catch (error) {
          common_vendor.index.__f__("error", "at utils/app-initializer.js:91", "清理过期登录信息失败:", error);
        }
      });
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:96", "性能优化初始化完成");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/app-initializer.js:99", "性能优化初始化失败:", error);
    }
  }
  /**
   * 初始化微信认证
   */
  async _initWechatAuth() {
    try {
      await utils_wechatAuth.wechatAuth.checkLoginStatus();
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:112", "微信认证初始化完成");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/app-initializer.js:115", "微信认证初始化失败:", error);
    }
  }
  /**
   * 初始化全局错误处理
   */
  async _initGlobalErrorHandler() {
    try {
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.onUnhandledRejection) {
        common_vendor.index.onUnhandledRejection((event) => {
          common_vendor.index.__f__("error", "at utils/app-initializer.js:127", "未处理的Promise错误:", event.reason);
          if (config_env.isDebug()) {
            common_vendor.index.showToast({
              title: "发生未处理错误",
              icon: "none",
              duration: 2e3
            });
          }
        });
      }
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:157", "全局错误处理初始化完成");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/app-initializer.js:160", "全局错误处理初始化失败:", error);
    }
  }
  /**
   * 初始化页面生命周期监听
   */
  async _initPageLifecycle() {
    try {
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.onAppShow) {
        common_vendor.index.onAppShow(() => {
          if (config_env.isDebug()) {
            common_vendor.index.__f__("log", "at utils/app-initializer.js:173", "应用显示");
          }
          utils_wechatAuth.wechatAuth.checkLoginStatus().catch((error) => {
            common_vendor.index.__f__("error", "at utils/app-initializer.js:178", "检查登录状态失败:", error);
          });
        });
      }
      if (typeof common_vendor.index !== "undefined" && common_vendor.index.onAppHide) {
        common_vendor.index.onAppHide(() => {
          if (config_env.isDebug()) {
            common_vendor.index.__f__("log", "at utils/app-initializer.js:187", "应用隐藏");
          }
          utils_performanceOptimizer.memoryOptimizer.performCleanup();
        });
      }
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:196", "页面生命周期监听初始化完成");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/app-initializer.js:199", "页面生命周期监听初始化失败:", error);
    }
  }
  /**
   * 销毁应用
   */
  destroy() {
    try {
      utils_performanceOptimizer.destroyPerformanceOptimization();
      if (utils_wechatAuth.wechatAuth && typeof utils_wechatAuth.wechatAuth.destroy === "function") {
        utils_wechatAuth.wechatAuth.destroy();
      }
      this.initialized = false;
      this.initPromise = null;
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/app-initializer.js:220", "应用已销毁");
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/app-initializer.js:223", "应用销毁失败:", error);
    }
  }
  /**
   * 获取初始化状态
   */
  isInitialized() {
    return this.initialized;
  }
}
const appInitializer = new AppInitializer();
async function initApp() {
  return await appInitializer.init();
}
function destroyApp() {
  appInitializer.destroy();
}
exports.destroyApp = destroyApp;
exports.initApp = initApp;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/app-initializer.js.map
