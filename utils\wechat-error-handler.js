// 微信小程序专用错误处理器
import { isDebug } from '@/config/env.js';
import { ERROR_TYPES, showError } from '@/utils/error-handler.js';

/**
 * 微信错误代码映射
 */
export const WECHAT_ERROR_CODES = {
  // wx.login 错误码
  '40029': '无效的code',
  '45011': 'API调用太频繁',
  '40013': '无效的AppID',
  
  // wx.getUserProfile 错误码
  '40003': '无效的openid',
  '41001': '缺少access_token参数',
  '42001': 'access_token超时',
  
  // 网络相关
  'request:fail': '网络请求失败',
  'timeout': '请求超时',
  
  // 用户操作
  'getUserProfile:fail cancel': '用户取消授权',
  'getUserProfile:fail': '获取用户信息失败',
  'chooseAvatar:fail cancel': '用户取消选择头像',
  
  // 系统错误
  'system error': '系统错误',
  'busy': '系统繁忙'
};

/**
 * 微信错误类型枚举
 */
export const WECHAT_ERROR_TYPES = {
  LOGIN_FAILED: 'wechat_login_failed',           // 微信登录失败
  CODE_INVALID: 'wechat_code_invalid',           // code无效
  USER_CANCELLED: 'wechat_user_cancelled',       // 用户取消
  PROFILE_FAILED: 'wechat_profile_failed',       // 获取用户信息失败
  AVATAR_FAILED: 'wechat_avatar_failed',         // 头像选择失败
  API_LIMIT: 'wechat_api_limit',                 // API调用限制
  NETWORK_ERROR: 'wechat_network_error',         // 网络错误
  SYSTEM_ERROR: 'wechat_system_error'            // 系统错误
};

/**
 * 分析微信错误
 * @param {Error|string|Object} error 错误对象
 * @returns {Object} 错误分析结果
 */
export function analyzeWechatError(error) {
  let message = '';
  let errCode = '';
  
  if (error instanceof Error) {
    message = error.message || '';
  } else if (typeof error === 'string') {
    message = error;
  } else if (error && error.errMsg) {
    message = error.errMsg;
    errCode = error.errCode || '';
  } else {
    message = String(error || '');
  }
  
  const lowerMessage = message.toLowerCase();
  
  // 用户取消操作
  if (lowerMessage.includes('cancel') || 
      lowerMessage.includes('取消') ||
      message.includes('getUserProfile:fail cancel') ||
      message.includes('chooseAvatar:fail cancel')) {
    return {
      type: WECHAT_ERROR_TYPES.USER_CANCELLED,
      level: 'info',
      message: '用户取消操作',
      originalMessage: message,
      errCode
    };
  }
  
  // 网络相关错误
  if (lowerMessage.includes('network') ||
      lowerMessage.includes('网络') ||
      lowerMessage.includes('request:fail') ||
      lowerMessage.includes('timeout') ||
      lowerMessage.includes('超时')) {
    return {
      type: WECHAT_ERROR_TYPES.NETWORK_ERROR,
      level: 'warning',
      message: '网络连接失败，请检查网络后重试',
      originalMessage: message,
      errCode
    };
  }
  
  // API调用限制
  if (errCode === '45011' || 
      lowerMessage.includes('too many requests') ||
      lowerMessage.includes('api limit')) {
    return {
      type: WECHAT_ERROR_TYPES.API_LIMIT,
      level: 'warning',
      message: 'API调用过于频繁，请稍后重试',
      originalMessage: message,
      errCode
    };
  }
  
  // code相关错误
  if (errCode === '40029' || 
      lowerMessage.includes('invalid code') ||
      lowerMessage.includes('code无效')) {
    return {
      type: WECHAT_ERROR_TYPES.CODE_INVALID,
      level: 'error',
      message: '登录凭证无效，请重新登录',
      originalMessage: message,
      errCode
    };
  }
  
  // 获取用户信息失败
  if (lowerMessage.includes('getuserprofile') ||
      lowerMessage.includes('获取用户信息')) {
    return {
      type: WECHAT_ERROR_TYPES.PROFILE_FAILED,
      level: 'warning',
      message: '获取用户信息失败，可稍后在个人中心完善',
      originalMessage: message,
      errCode
    };
  }
  
  // 头像选择失败
  if (lowerMessage.includes('chooseavatar') ||
      lowerMessage.includes('头像')) {
    return {
      type: WECHAT_ERROR_TYPES.AVATAR_FAILED,
      level: 'warning',
      message: '头像选择失败，请重试',
      originalMessage: message,
      errCode
    };
  }
  
  // 系统错误
  if (lowerMessage.includes('system') ||
      lowerMessage.includes('系统') ||
      errCode === 'busy' ||
      lowerMessage.includes('busy')) {
    return {
      type: WECHAT_ERROR_TYPES.SYSTEM_ERROR,
      level: 'error',
      message: '系统繁忙，请稍后重试',
      originalMessage: message,
      errCode
    };
  }
  
  // 默认登录失败
  return {
    type: WECHAT_ERROR_TYPES.LOGIN_FAILED,
    level: 'error',
    message: '登录失败，请重试',
    originalMessage: message,
    errCode
  };
}

/**
 * 显示微信错误提示
 * @param {Error|string|Object} error 错误对象
 * @param {Object} options 选项
 */
export function showWechatError(error, options = {}) {
  const errorAnalysis = analyzeWechatError(error);
  
  // 在调试模式下记录详细信息
  if (isDebug()) {
    console.error('微信错误详情:', {
      type: errorAnalysis.type,
      level: errorAnalysis.level,
      message: errorAnalysis.message,
      original: errorAnalysis.originalMessage,
      errCode: errorAnalysis.errCode
    });
  }
  
  // 用户取消操作通常不需要显示错误提示
  if (errorAnalysis.type === WECHAT_ERROR_TYPES.USER_CANCELLED) {
    return;
  }
  
  const defaultOptions = {
    duration: 2000,
    icon: 'none',
    ...options
  };
  
  uni.showToast({
    title: errorAnalysis.message,
    icon: defaultOptions.icon,
    duration: defaultOptions.duration
  });
}

/**
 * 检查是否为可重试的微信错误
 * @param {Error|string|Object} error 错误对象
 * @returns {boolean} 是否可重试
 */
export function isRetryableWechatError(error) {
  const errorAnalysis = analyzeWechatError(error);
  
  const retryableTypes = [
    WECHAT_ERROR_TYPES.NETWORK_ERROR,
    WECHAT_ERROR_TYPES.SYSTEM_ERROR,
    WECHAT_ERROR_TYPES.API_LIMIT
  ];
  
  return retryableTypes.includes(errorAnalysis.type);
}

/**
 * 获取微信错误的重试延迟时间
 * @param {Error|string|Object} error 错误对象
 * @param {number} attempt 重试次数
 * @returns {number} 延迟时间（毫秒）
 */
export function getWechatRetryDelay(error, attempt = 1) {
  const errorAnalysis = analyzeWechatError(error);
  
  switch (errorAnalysis.type) {
    case WECHAT_ERROR_TYPES.API_LIMIT:
      // API限制错误需要更长的延迟
      return Math.min(5000 * attempt, 30000);
    
    case WECHAT_ERROR_TYPES.NETWORK_ERROR:
      // 网络错误使用递增延迟
      return Math.min(1000 * Math.pow(1.5, attempt - 1), 10000);
    
    case WECHAT_ERROR_TYPES.SYSTEM_ERROR:
      // 系统错误使用固定延迟
      return 3000;
    
    default:
      return 1000;
  }
}

/**
 * 微信登录错误处理包装器
 * @param {Function} operation 登录操作
 * @param {Object} options 选项
 * @returns {Promise} 操作结果
 */
export function withWechatErrorHandling(operation, options = {}) {
  const defaultOptions = {
    maxRetries: 3,
    showError: true,
    onRetry: null,
    ...options
  };
  
  return new Promise(async (resolve, reject) => {
    let lastError = null;
    
    for (let attempt = 1; attempt <= defaultOptions.maxRetries + 1; attempt++) {
      try {
        const result = await operation();
        resolve(result);
        return;
      } catch (error) {
        lastError = error;
        
        // 检查是否可重试
        if (attempt <= defaultOptions.maxRetries && isRetryableWechatError(error)) {
          const delay = getWechatRetryDelay(error, attempt);
          
          if (isDebug()) {
            console.log(`微信操作失败，${delay}ms后进行第${attempt}次重试:`, error);
          }
          
          // 调用重试回调
          if (typeof defaultOptions.onRetry === 'function') {
            defaultOptions.onRetry(attempt, error, delay);
          }
          
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // 不可重试或达到最大重试次数
          break;
        }
      }
    }
    
    // 显示错误提示
    if (defaultOptions.showError) {
      showWechatError(lastError);
    }
    
    reject(lastError);
  });
}
