<template>
	<view class="case-detail-container">
		<view class="detail-card">
			<!-- 案例标题区域 -->
			<view class="case-header">
				<view class="case-title">{{ caseDetail.title }}</view>
				<view class="case-tags">
					<text class="case-tag" v-for="tag in caseDetail.tags" :key="tag">{{ tag }}</text>
				</view>
			</view>

			<!-- 案例基本信息 -->
			<view class="case-info">
				<view class="info-row">
					<text class="info-label">案例类型</text>
					<text class="info-value">{{ caseDetail.type }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">债务金额</text>
					<text class="info-value debt-amount">¥{{ formatAmount(caseDetail.debtAmount) }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">调解结果</text>
					<text class="info-value resolved-amount">¥{{ formatAmount(caseDetail.resolvedAmount) }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">减免比例</text>
					<text class="info-value reduction-rate">{{ caseDetail.reductionRate }}</text>
				</view>
				<view class="info-row">
					<text class="info-label">调解日期</text>
					<text class="info-value">{{ caseDetail.date }}</text>
				</view>
			</view>

			<!-- 案例详细内容 -->
			<view class="case-content">
				<view class="content-section">
					<view class="section-title">案例背景</view>
					<view class="section-content">{{ caseDetail.background }}</view>
				</view>

				<view class="content-section">
					<view class="section-title">调解过程</view>
					<view class="section-content">{{ caseDetail.process }}</view>
				</view>

				<view class="content-section">
					<view class="section-title">调解结果</view>
					<view class="section-content">{{ caseDetail.result }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';

// 案例详情数据
const caseDetail = ref({});

// 页面加载时获取参数
onLoad((options) => {
	const caseId = options.id;
	if (caseId) {
		loadCaseDetail(caseId);
	}
});

// 生命周期钩子
onMounted(() => {
	console.log('案例详情页面已加载');
});

// 加载案例详情
const loadCaseDetail = (caseId) => {
	// 模拟API调用，实际项目中应该调用真实API
	const mockData = getCaseById(caseId);
	caseDetail.value = mockData;
};

// 根据ID获取案例数据（模拟数据）
const getCaseById = (id) => {
	const cases = [
		{
			id: '1',
			title: '信用卡欠款减免案例',
			type: '信用卡欠款',
			debtAmount: 80000,
			resolvedAmount: 28000,
			reductionRate: '减免65%',
			date: '2023-10-15',
			tags: ['信用卡', '减免成功'],
			background: '张先生因疫情影响，无法按时偿还卡款，通过调解平台申请调解，最终与银行达成和解',
			process: '1. 提交调解申请；2. 银行方面同意调解；3. 双方协商还款方案；4. 达成一致意见',
			result: '最终协商确定还款金额为28,000元，分36期还款，张先生表示满意'
		},
		{
			id: '2',
			title: '车贷逾期调解案例',
			type: '车贷逾期',
			debtAmount: 120000,
			resolvedAmount: 120000,
			reductionRate: '减免全部罚息',
			date: '2023-09-28',
			tags: ['车贷', '延期还款'],
			background: '李女士因生意周转困难，车贷逾期3个月，通过调解平台寻求帮助',
			process: '调解员协调双方，了解李女士实际困难，与金融机构协商延期还款方案',
			result: '金融机构同意免除全部罚息，延期6个月还款，李女士非常感谢'
		}
	];
	
	return cases.find(c => c.id === id) || {};
};

// 格式化金额显示
const formatAmount = (amount) => {
	if (!amount) return '0.00';
	return amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 });
};
</script>

<style lang="scss" scoped>
.case-detail-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 20rpx;
}

.detail-card {
	background-color: #ffffff;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.case-header {
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.case-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	line-height: 1.4;
}

.case-tags {
	display: flex;
	flex-wrap: wrap;
	gap: 15rpx;
}

.case-tag {
	padding: 8rpx 16rpx;
	background-color: #e3f2fd;
	color: #1976d2;
	font-size: 24rpx;
	border-radius: 20rpx;
}

.case-info {
	padding: 30rpx;
	border-bottom: 2rpx solid #f0f0f0;
}

.info-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25rpx;
	padding: 15rpx 0;
}

.info-row:last-child {
	margin-bottom: 0;
}

.info-label {
	font-size: 28rpx;
	color: #666;
	font-weight: 500;
}

.info-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
}

.debt-amount {
	color: #f44336;
}

.resolved-amount {
	color: #4caf50;
}

.reduction-rate {
	color: #ff9800;
}

.case-content {
	padding: 30rpx;
}

.content-section {
	margin-bottom: 40rpx;
}

.content-section:last-child {
	margin-bottom: 0;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 20rpx;
	position: relative;
	padding-left: 20rpx;
}

.section-title::before {
	content: '';
	position: absolute;
	left: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 8rpx;
	height: 30rpx;
	background-color: #2979ff;
	border-radius: 4rpx;
}

.section-content {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	text-align: justify;
}
</style> 