<script setup>
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { initApp as initializeApp, destroyApp } from '@/utils/app-initializer.js';
import { isDebug } from '@/config/env.js';

// 全局数据
const globalData = {
	userInfo: null,
	systemInfo: null,
	appInitialized: false
};

// 将globalData挂载到getApp()上
uni.getApp = () => ({
	globalData
});

// 初始化应用 - 增强版本
async function initApp() {
	try {
		if (isDebug()) {
			console.log('开始应用初始化...');
		}

		// 获取系统信息
		try {
			const systemInfo = uni.getSystemInfoSync();
			globalData.systemInfo = systemInfo;

			if (isDebug()) {
				console.log('系统信息:', systemInfo);
			}
		} catch (e) {
			console.error('获取系统信息失败:', e);
		}

		// 使用新的应用初始化器
		const result = await initializeApp();

		if (result.success) {
			globalData.appInitialized = true;

			if (isDebug()) {
				console.log('应用初始化成功');
			}
		} else {
			console.error('应用初始化失败:', result.error);
		}
	} catch (error) {
		console.error('应用初始化异常:', error);
	}
}

// 生命周期钩子
onLaunch(() => {
	if (isDebug()) {
		console.log('App Launch - 不良资产系统');
	}

	// 初始化应用
	initApp();
});

onShow(() => {
	if (isDebug()) {
		console.log('App Show');
	}

	// 应用显示时的处理已在app-initializer中处理
});

onHide(() => {
	if (isDebug()) {
		console.log('App Hide');
	}

	// 应用隐藏时的处理已在app-initializer中处理
});

// 应用销毁时清理资源
if (typeof uni !== 'undefined' && uni.onAppDestroy) {
	uni.onAppDestroy(() => {
		if (isDebug()) {
			console.log('App Destroy');
		}

		destroyApp();
	});
}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import './uni.scss';
	@import './static/font/iconfont.wxss';
	@import './static/font/icon-fallback.wxss';
	@import './static/font/fontawesome/fontawesome.css';
	
	/* 全局样式 */
	page {
		font-size: 28rpx;
		color: #333;
		background-color: #f5f5f5;
		font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
	}
	
	/* 通用样式 */
	.container {
		width: 100%;
		min-height: 100vh;
	}
	
	/* 卡片样式 */
	.card {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}
	
	/* 按钮样式 */
	.btn {
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		border-radius: 40rpx;
		font-size: 30rpx;
		color: #ffffff;
	}
	
	.btn-primary {
		background-color: $uni-color-primary;
	}
	
	.btn-success {
		background-color: $uni-color-success;
	}
	
	/* 文本溢出省略号 */
	.text-ellipsis {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	
	/* flex布局工具类 */
	.flex-row {
		display: flex;
		flex-direction: row;
	}
	
	.flex-column {
		display: flex;
		flex-direction: column;
	}
	
	.flex-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.space-between {
		justify-content: space-between;
	}
	
	.align-center {
		align-items: center;
	}
	
	/* 边距工具类 */
	.mt-10 {
		margin-top: 10rpx;
	}
	
	.mb-10 {
		margin-bottom: 10rpx;
	}
	
	.ml-10 {
		margin-left: 10rpx;
	}
	
	.mr-10 {
		margin-right: 10rpx;
	}
	
	.pt-10 {
		padding-top: 10rpx;
	}
	
	.pb-10 {
		padding-bottom: 10rpx;
	}
	
	.pl-10 {
		padding-left: 10rpx;
	}
	
	.pr-10 {
		padding-right: 10rpx;
	}
</style>
