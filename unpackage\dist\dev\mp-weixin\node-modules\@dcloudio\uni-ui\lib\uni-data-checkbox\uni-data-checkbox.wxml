<view class="uni-data-checklist" style="{{'margin-top:' + v}}"><block wx:if="{{a}}"><view class="uni-data-loading"><uni-load-more wx:if="{{b}}" u-i="241a78e5-0" bind:__l="__l" u-p="{{c}}"></uni-load-more><text wx:else>{{d}}</text></view></block><block wx:else><checkbox-group wx:if="{{e}}" class="{{['checklist-group', k && 'is-list']}}" bindchange="{{l}}"><label wx:for="{{f}}" wx:for-item="item" wx:key="l" class="{{['checklist-box', j, item.h, item.i, item.j]}}" style="{{item.k}}"><checkbox class="hidden" hidden disabled="{{item.a}}" value="{{item.b}}" checked="{{item.c}}"/><view wx:if="{{g}}" class="checkbox__inner" style="{{item.d}}"><view class="checkbox__inner-icon"></view></view><view class="{{['checklist-content', i && 'list-content']}}"><text class="checklist-text" style="{{item.f}}">{{item.e}}</text><view wx:if="{{h}}" class="checkobx__list" style="{{item.g}}"></view></view></label></checkbox-group><radio-group wx:else class="{{['checklist-group', r && 'is-list', s && 'is-wrap']}}" bindchange="{{t}}"><label wx:for="{{m}}" wx:for-item="item" wx:key="m" class="{{['checklist-box', q, item.i, item.j, item.k]}}" style="{{item.l}}"><radio class="hidden" hidden disabled="{{item.a}}" value="{{item.b}}" checked="{{item.c}}"/><view wx:if="{{n}}" class="radio__inner" style="{{item.e}}"><view class="radio__inner-icon" style="{{item.d}}"></view></view><view class="{{['checklist-content', p && 'list-content']}}"><text class="checklist-text" style="{{item.g}}">{{item.f}}</text><view wx:if="{{o}}" style="{{item.h}}" class="checkobx__list"></view></view></label></radio-group></block></view>