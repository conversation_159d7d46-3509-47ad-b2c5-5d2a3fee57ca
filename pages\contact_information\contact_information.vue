<template>
  <view class="content">
    <view class="gradient-bg card">
      <view class="icon-box">
        <i class="fas fa-envelope"></i>
      </view>
      <h2 class="text">确认联系方式</h2>
      <view class="text-gray">为了及时向您发送调解结果短信，便于日后快速查找相关信息，请确认您的手机号码</view>
    </view>

    <view class="card form-container">
      <h3 class="text-lg">确认联系方式</h3>
      <view class="form-card">
        <view class="card-header">
          <text class="card-title">手机号码</text>
        </view>
        <view class="card-content">
          <uni-easyinput
            v-model="formData.phoneNumber"
            placeholder="请输入手机号"
            class="input-field"
          >
          </uni-easyinput>
        </view>
        <view class="text-xs"><i class="fas fa-info-circle"></i>此手机号仅用于发送调解结果短信，不会用于其他用途</view>
      </view>
      <view class="form-card">
        <view class="card-header">
          <text class="card-title">短信验证码</text>
        </view>
        <view class="card-content code-input">
          <uni-easyinput
            v-model="formData.verificationCode"
            placeholder="请输入验证码"
            class="input-field"
          >
          </uni-easyinput>
          <button
            @click="getVerificationCode"
            :disabled="countdown > 0"
            class="code-btn"
            :class="
              countdown > 0
                ? 'bg-gray-200 text-gray-500'
                : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
            "
          >
            {{ countdown > 0 ? `${countdown}s后重新获取` : "获取验证码" }}
          </button>
        </view>
      </view>
      <!-- <view class="mb-4">
        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
        <input 
        type="tel" 
        id="phone" 
        v-model="phoneNumber" 
        placeholder="请输入您的手机号码" 
        class="w-full"
        >
        <view class="text-xs"><i class="fas fa-info-circle"></i>此手机号仅用于发送调解结果短信，不会用于其他用途</view>
      </view>
      
      <view class="mb-6">
        <label for="code" class="block text-sm font-medium text-gray-700 mb-1">短信验证码</label>
        <view class="flex gap-2">
        <input 
          type="text" 
          id="code" 
          v-model="verificationCode" 
          placeholder="请输入验证码" 
          class="flex-1"
        >
        <button 
          @click="getVerificationCode" 
          :disabled="countdown > 0"
          class="px-4 py-3 rounded-lg text-sm font-medium whitespace-nowrap"
          :class="countdown > 0 ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'"
        >
          {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
        </button>
			</view> 
		</view>-->
    </view>
    <view class="action-buttons">
      <button @click="confirmContact" class="confirm-btn">
        <i class="fas fa-check"></i>确认联系方式
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, createApp, reactive, computed, onMounted } from "vue";
import { onPullDownRefresh } from "@dcloudio/uni-app";
import { api } from "@/utils/api.js";
// 表单数据
const formData = reactive({
  phoneNumber: "18698990903", // 手机号码
  verificationCode: "557722", // 验证码
});
const countdown = ref(0); // 验证码倒计时
const isSubmitting = ref(false); // 提交状态
// 模拟获取验证码
const getVerificationCode = () => {
  console.log(formData.phoneNumber.value,'=====',formData.phoneNumber);
  if (!formData.phoneNumber) {
    uni.showToast({
      title: '请输入手机号码',
      icon: 'none'
    });
    return;
  }

  // 简单的手机号验证
  if (!/^1[3-9]\d{9}$/.test(formData.phoneNumber)) {
    uni.showToast({
      title: '请输入正确的手机号码',
      icon: 'none'
    });
    return;
  }

  // 模拟API调用 - 实际开发中替换为真实的API调用
  console.log("调用发送验证码API，手机号:", formData.phoneNumber);

  // 开始倒计时
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);

  // 模拟API返回 - 实际开发中处理真实的API响应
  setTimeout(() => {
    console.log("验证码已发送");
    // 这里可以添加提示，如Toast通知
  }, 1000);
};

// 模拟确认联系方式
const confirmContact = () => {
  if (!formData.phoneNumber) {
    uni.showToast({
      title: '请输入手机号码',
      icon: 'none'
    });
    return;
  }

  if (!formData.verificationCode) {
    uni.showToast({
      title: '请输入验证码',
      icon: 'none'
    });
    return;
  }

  // 简单的验证码验证
  if (formData.verificationCode.length !== 6) {
    uni.showToast({
      title: '请输入6位验证码',
      icon: 'none'
    });
    return;
  }

  isSubmitting.value = true;

  // 模拟API调用 - 实际开发中替换为真实的API调用
  console.log("调用确认联系方式API", {
    phone: formData.phoneNumber,
    code: formData.verificationCode,
  });

  // 模拟API响应 - 实际开发中处理真实的API响应
  // setTimeout(() => {
    isSubmitting.value = false;
    uni.showToast({
      title: '联系方式确认成功！',
      icon: 'none'
    });
  // }, 1500);
  
    // 这里可以添加跳转逻辑，如跳转到下一个页面
    uni.navigateTo({
      url: '/pages/case_completed/case_completed',
      success: () => {
        console.log('页面跳转成功');
      },
      fail: (error) => {
        console.error('页面跳转失败：', error);
        uni.showToast({
          title: '页面跳转失败，请重试',
          icon: 'none'
        });
      }
   });
};
// 生命周期钩子
onMounted(() => {
  console.log("调解查询页面已加载");
});
</script>

<style lang="scss" scoped>
:root {
  --primary-color: #3b7eeb;
  --primary-light: #e6f0ff;
  --transition-normal: all 0.3s ease;
  --text-secondary: #666;
  --text-color: #333;
  --text-light: #999;
}
.content {
  height: calc(100% - 188rpx);
  overflow-y: auto;
  padding: 30rpx;
  padding-bottom: 140rpx;
  background-color: #f8fafc;
}
.gradient-bg {
  background: linear-gradient(135deg, var(--primary-light) 0%, #e6f0ff 100%);
  border: none;
  text-align: center;
  padding: 60rpx 40rpx;
}
.icon-box {
  width: 120rpx;
  height: 120rpx;
  background-color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx;
  .fas {
    color: white;
    font-size: 48rpx;
  }
}
.text {
  margin: 0 0 20rpx 0;
  color: var(--primary-color);
  font-size: 36rpx;
  font-weight: bold;
}
.text-gray {
  color: var(--text-secondary);
  font-size: 28rpx;
  line-height: 1.6;
  margin: 0;
}

.card {
  background-color: white;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  padding: 36rpx;
  margin-bottom: 30rpx;
  transition: var(--transition-normal);
  border: 1px solid rgba(0, 0, 0, 0.03);
}
.form-container {
  display: flex;
  flex-direction: column;
  .form-card {
    overflow: hidden;
    margin-bottom: 20rpx;
    .card-header {
      padding: 30rpx 30rpx 20rpx 10rpx;
      // border-bottom: 1rpx solid #f0f0f0;
      display: flex;
      align-items: center;

      .fas {
        margin-right: 8px;
        color: var(--primary-color);
      }

      .card-title {
        font-size: 30rpx;
        color: var(--text-primary);
        font-weight: 800;
        // flex: 1;
      }
    }
  }
}
.text-lg {
  text-align: center;
  color: var(--text-color);
  font-size: 36rpx;
  font-weight: bold;
}
.action-buttons {
  padding: 20rpx 0;
}

.confirm-btn {
  background-color: #3b7eeb;
  color: #fff;
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  .fas {
    margin-right: 20rpx;
    font-size: 36rpx;
    color: var(--bg-primary);
  }
  &:hover {
    background-color: #2a6ed8;
  }
}
.text-xs {
  display: flex;
  align-items: center;
  margin-top: 25rpx;
  font-size: 24rpx;
  color: var(--text-light);
  .fas {
    color: var(--text-light);
    font-size: 24rpx;
    margin-right: 10rpx;
  }
}
.code-input{
  display: flex;
  gap: 24rpx;
  .input-field{
    position: relative;
    flex: 1;
  }
  .code-btn{
    white-space: nowrap;
    min-width: 100px;
    background-color: var(--primary-color);
    color: white;
  }
}
:deep(.uni-easyinput__content-input) {
  height: 80rpx;
  font-size: 28rpx;
}
</style>