// 默认头像的base64编码数据
// 这些是简单的SVG头像，转换为base64格式以确保兼容性

/**
 * 默认头像 - 通用
 */
export const DEFAULT_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNFNUU3RUIiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iIzlDQTNBRiIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiM5Q0EzQUYiLz48L2c+PC9zdmc+';

/**
 * 男性默认头像
 */
export const MALE_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNEREY0RkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iIzM5OEVGNyIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiMzOThFRjciLz48L2c+PC9zdmc+';

/**
 * 女性默认头像
 */
export const FEMALE_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGRUY3RkYiLz48ZyB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCwgNDApIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjQ1IiByPSIyNSIgZmlsbD0iI0VDNEE5OSIvPjxwYXRoIGQ9Ik0yMCAxMjAgUTIwIDkwIDUwIDkwIFE4MCA5MCA4MCAxMjAgTDgwIDE0MCBMMjAgMTQwIFoiIGZpbGw9IiNFQzRBOTkiLz48L2c+PC9zdmc+';

/**
 * 占位符头像 - 加载中
 */
export const PLACEHOLDER_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGM0Y0RjYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjMwIiBmaWxsPSIjRDFENURCIiBvcGFjaXR5PSIwLjUiLz48L3N2Zz4=';

/**
 * 错误头像 - 加载失败时显示
 */
export const ERROR_AVATAR_BASE64 = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGRUY3RkYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjQwIiBmaWxsPSIjRkVGMkYyIiBzdHJva2U9IiNGODcxNzEiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiNGODcxNzEiPj88L3RleHQ+PC9zdmc+';

/**
 * 获取base64格式的默认头像
 * @param {string} type 头像类型
 * @param {string|number} gender 性别
 * @returns {string} base64头像数据
 */
export function getBase64Avatar(type = 'default', gender = null) {
  // 根据性别返回对应头像
  if (type === 'default' && gender !== null) {
    switch (parseInt(gender)) {
      case 0: // 男性
        return MALE_AVATAR_BASE64;
      case 1: // 女性
        return FEMALE_AVATAR_BASE64;
      default: // 保密或其他
        return DEFAULT_AVATAR_BASE64;
    }
  }

  // 根据类型返回头像
  switch (type) {
    case 'male':
      return MALE_AVATAR_BASE64;
    case 'female':
      return FEMALE_AVATAR_BASE64;
    case 'placeholder':
    case 'loading':
      return PLACEHOLDER_AVATAR_BASE64;
    case 'error':
      return ERROR_AVATAR_BASE64;
    default:
      return DEFAULT_AVATAR_BASE64;
  }
}

/**
 * 检查是否为base64头像
 * @param {string} url 头像URL
 * @returns {boolean} 是否为base64格式
 */
export function isBase64Avatar(url) {
  return url && url.startsWith('data:image/');
}

/**
 * 获取头像的显示名称
 * @param {string} type 头像类型
 * @returns {string} 显示名称
 */
export function getAvatarDisplayName(type) {
  const names = {
    default: '默认头像',
    male: '男性头像',
    female: '女性头像',
    placeholder: '占位符头像',
    loading: '加载中头像',
    error: '错误头像'
  };

  return names[type] || '未知头像';
}