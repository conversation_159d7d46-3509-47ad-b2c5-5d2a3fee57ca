@echo off
chcp 65001 >nul
echo ===============================================
echo 🔄 重新安装uni-ui组件库
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 当前项目目录: %CD%
echo.

echo 📋 此脚本将：
echo   1. 清理现有的uni-ui安装
echo   2. 重新安装uni-ui组件库
echo   3. 清理编译缓存
echo   4. 验证安装结果
echo.

set /p confirm=是否继续？(Y/N): 
if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 🚀 开始重新安装uni-ui...
echo.

echo 📦 第一步：清理现有uni-ui安装...
if exist "node_modules\@dcloudio\uni-ui" (
    echo 正在删除现有uni-ui...
    rmdir /s /q "node_modules\@dcloudio\uni-ui" 2>nul
    echo ✅ 现有uni-ui已删除
) else (
    echo ℹ️ 现有uni-ui不存在
)

echo.
echo 📁 第二步：清理编译缓存...
if exist "unpackage\dist\dev\mp-weixin\node-modules\@dcloudio\uni-ui" (
    rmdir /s /q "unpackage\dist\dev\mp-weixin\node-modules\@dcloudio\uni-ui" 2>nul
    echo ✅ 开发环境编译缓存已清理
)

if exist "unpackage\dist\build\mp-weixin\node-modules\@dcloudio\uni-ui" (
    rmdir /s /q "unpackage\dist\build\mp-weixin\node-modules\@dcloudio\uni-ui" 2>nul
    echo ✅ 生产环境编译缓存已清理
)

echo.
echo 🌐 第三步：重新安装uni-ui...
echo 正在执行: npm install @dcloudio/uni-ui@1.5.7
npm install @dcloudio/uni-ui@1.5.7

if %errorlevel%==0 (
    echo ✅ uni-ui安装成功
) else (
    echo ❌ uni-ui安装失败
    echo.
    echo 可能的解决方案：
    echo 1. 检查网络连接
    echo 2. 尝试使用淘宝镜像：npm install @dcloudio/uni-ui@1.5.7 --registry=https://registry.npm.taobao.org
    echo 3. 清理npm缓存：npm cache clean --force
    echo.
    pause
    exit /b
)

echo.
echo 🔍 第四步：验证安装结果...
if exist "node_modules\@dcloudio\uni-ui\lib\uni-easyinput" (
    echo ✅ uni-easyinput组件已安装
) else (
    echo ❌ uni-easyinput组件未找到
)

if exist "node_modules\@dcloudio\uni-ui\lib\uni-data-select" (
    echo ✅ uni-data-select组件已安装
) else (
    echo ❌ uni-data-select组件未找到
)

if exist "node_modules\@dcloudio\uni-ui\lib\uni-popup" (
    echo ⚠️ uni-popup组件已安装（但项目中不使用）
) else (
    echo ℹ️ uni-popup组件未安装
)

echo.
echo 🗂️ 第五步：清理HBuilderX缓存...
if exist ".hbuilderx" (
    rmdir /s /q ".hbuilderx" 2>nul
    echo ✅ HBuilderX缓存已清理
)

echo.
echo ===============================================
echo ✨ uni-ui重新安装完成！
echo ===============================================
echo.
echo 📋 接下来请按顺序执行：
echo.
echo 1️⃣ 关闭HBuilderX
echo.
echo 2️⃣ 重新打开HBuilderX
echo.
echo 3️⃣ 在HBuilderX中执行"运行" → "清理项目"
echo.
echo 4️⃣ 重新编译到微信小程序
echo.
echo 5️⃣ 验证结果：
echo    ✅ uni-easyinput组件应该正常显示
echo    ✅ 调解投诉页面的输入框正常工作
echo    ✅ 不应该再有组件文件缺失错误
echo.
echo ⚠️ 如果仍有问题：
echo   - 检查网络连接是否正常
echo   - 尝试使用npm镜像源
echo   - 检查HBuilderX版本兼容性
echo.
echo ===============================================
pause
