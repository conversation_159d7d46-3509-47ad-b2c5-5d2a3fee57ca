@echo off
chcp 65001 >nul
echo ===============================================
echo 🚑 WebView备用方案 - 临时解决方案
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 当前项目目录: %CD%
echo.

echo 📋 说明：
echo   此脚本将修改auth.vue，使用备用方案跳转到人脸识别
echo   这是一个临时解决方案，用于在webview问题修复前正常使用功能
echo.

set /p confirm=是否应用备用方案？(Y/N): 
if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 🔧 正在应用备用方案...
echo.

echo 📄 备份原始文件...
if exist "pages\auth\auth.vue.backup" (
    echo ℹ️ 备份文件已存在
) else (
    copy "pages\auth\auth.vue" "pages\auth\auth.vue.backup" >nul
    echo ✅ 已创建备份: auth.vue.backup
)

echo.
echo 📝 方案说明：
echo.
echo 🔄 备用方案1：直接跳转外部链接
echo   使用uni.navigateTo打开系统浏览器
echo.
echo 🔄 备用方案2：显示链接供用户复制
echo   在页面上显示链接，用户手动复制到浏览器
echo.
echo 🔄 备用方案3：使用小程序内置web-view
echo   创建简化版webview页面
echo.

echo ⚠️ 注意：
echo   备用方案可能在某些环境下有限制
echo   建议优先解决原始webview问题
echo.

echo 📋 手动修改指导：
echo.
echo 在 pages/auth/auth.vue 的 startFaceRecognition 函数中：
echo.
echo 将这行：
echo   uni.navigateTo({
echo     url: `/pages/webview/webview?url=${encodeURIComponent(responseData.data)}`
echo   });
echo.
echo 替换为：
echo   // 备用方案：显示链接给用户
echo   uni.showModal({
echo     title: '人脸识别',
echo     content: '请复制以下链接到浏览器中完成验证：' + responseData.data,
echo     showCancel: false,
echo     confirmText: '复制链接',
echo     success: () => {
echo       uni.setClipboardData({
echo         data: responseData.data,
echo         success: () => {
echo           uni.showToast({ title: '链接已复制' });
echo         }
echo       });
echo     }
echo   });
echo.
echo ===============================================
echo 备用方案准备完成！
echo ===============================================
echo.
pause 