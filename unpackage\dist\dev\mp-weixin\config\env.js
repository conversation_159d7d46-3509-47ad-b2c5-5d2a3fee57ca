"use strict";
const common_vendor = require("../common/vendor.js");
const ENV_TYPE = {
  DEVELOPMENT: "development",
  TESTING: "testing",
  PRODUCTION: "production"
};
const envConfig = {
  // 开发环境
  [ENV_TYPE.DEVELOPMENT]: {
    name: "开发环境",
    // baseURL: 'https://www.eeclat.cn',
    baseURL: "http://*************:14010",
    wechatAppId: "",
    // 微信小程序AppID - 开发环境
    debug: true,
    timeout: 1e4
  },
  // 测试环境  
  [ENV_TYPE.TESTING]: {
    name: "测试环境",
    baseURL: "http://*************:14010",
    // baseURL: 'https://www.eeclat.cn',
    wechatAppId: "",
    // 微信小程序AppID - 测试环境
    debug: true,
    timeout: 15e3
  },
  // 生产环境
  [ENV_TYPE.PRODUCTION]: {
    name: "生产环境",
    baseURL: "http://*************:14010",
    // baseURL: 'https://www.eeclat.cn',
    wechatAppId: "",
    // 微信小程序AppID - 生产环境
    debug: false,
    timeout: 2e4
  }
};
let currentEnv = ENV_TYPE.DEVELOPMENT;
function detectEnvironment() {
  try {
    const accountInfo = common_vendor.wx$1.getAccountInfoSync();
    const envVersion = accountInfo.miniProgram.envVersion;
    switch (envVersion) {
      case "develop":
        return ENV_TYPE.DEVELOPMENT;
      case "trial":
        return ENV_TYPE.TESTING;
      case "release":
        return ENV_TYPE.PRODUCTION;
      default:
        return ENV_TYPE.DEVELOPMENT;
    }
  } catch (error) {
    common_vendor.index.__f__("warn", "at config/env.js:66", "环境检测失败，使用默认开发环境:", error);
    return ENV_TYPE.DEVELOPMENT;
  }
}
function initEnvironment() {
  currentEnv = detectEnvironment();
  common_vendor.index.__f__("log", "at config/env.js:74", `当前环境: ${getCurrentConfig().name}`);
}
function getCurrentConfig() {
  return envConfig[currentEnv];
}
function getBaseURL() {
  return getCurrentConfig().baseURL;
}
function isDebug() {
  return getCurrentConfig().debug;
}
function getTimeout() {
  return getCurrentConfig().timeout;
}
initEnvironment();
exports.getBaseURL = getBaseURL;
exports.getTimeout = getTimeout;
exports.isDebug = isDebug;
//# sourceMappingURL=../../.sourcemap/mp-weixin/config/env.js.map
