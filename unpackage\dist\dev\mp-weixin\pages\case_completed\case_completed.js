"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "case_completed",
  setup(__props) {
    const caseNumber = common_vendor.ref("");
    const basicInfo = common_vendor.ref({
      // 基本信息
      case_number: "",
      initiate_date: "",
      case_status_cn: "",
      mediation_progress: ""
    });
    const workOrderData = common_vendor.ref({});
    const isCopied = common_vendor.ref(false);
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/case_completed/case_completed.vue:246", "方案确认页面加载，参数:", options);
      if (options && options.case_number) {
        handleUrlParams(options);
      }
    });
    const handleUrlParams = async (options) => {
      common_vendor.index.__f__("log", "at pages/case_completed/case_completed.vue:257", "处理URL参数:", options);
      const { case_number, initiate_date, case_status_cn, mediation_progress } = options;
      const hasCompleteParams = case_number && initiate_date && case_status_cn && mediation_progress;
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        if (hasCompleteParams) {
          common_vendor.index.__f__("log", "at pages/case_completed/case_completed.vue:269", "检测到完整参数，直接显示基本信息");
          basicInfo.value.case_number = decodeURIComponent(case_number);
          basicInfo.value.initiate_date = decodeURIComponent(initiate_date);
          basicInfo.value.case_status_cn = decodeURIComponent(case_status_cn);
          basicInfo.value.mediation_progress = decodeURIComponent(mediation_progress);
          caseNumber.value = basicInfo.value.case_number;
          await fetchWorkOrderDetail(basicInfo.value.case_number);
        } else if (case_number) {
          common_vendor.index.__f__("log", "at pages/case_completed/case_completed.vue:281", "仅有案件编号，调用接口获取详细信息");
          const decodedCaseNumber = decodeURIComponent(case_number);
          caseNumber.value = decodedCaseNumber;
          await fetchMediationSingleDetailWithoutLoading(decodedCaseNumber);
          await fetchWorkOrderDetail(decodedCaseNumber);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/case_completed/case_completed.vue:292", "处理URL参数失败:", error);
        common_vendor.index.showToast({
          title: "页面加载失败",
          icon: "none",
          duration: 2e3
        });
      } finally {
        common_vendor.index.hideLoading();
      }
    };
    const fetchMediationSingleDetailWithoutLoading = async (caseNumber2) => {
      try {
        const result = await utils_api.api.mediationQuery.getSingleDetail(caseNumber2);
        if (result.state === "success" && result.data) {
          const data = result.data;
          basicInfo.value.case_number = data.case_number || caseNumber2;
          basicInfo.value.initiate_date = data.initiate_date || "";
          basicInfo.value.case_status_cn = data.case_status_cn || "";
          basicInfo.value.mediation_progress = data.mediation_progress || "";
        } else {
          throw new Error(result.msg || "获取案件信息失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/case_completed/case_completed.vue:319", "获取案件详情失败:", error);
        throw error;
      }
    };
    common_vendor.onMounted(() => {
    });
    const fetchWorkOrderDetail = (id) => {
      common_vendor.index.showLoading({
        title: "加载中..."
      });
      const result = utils_api.api.mediationQuery.getCompletedDetail(id);
      common_vendor.index.hideLoading();
      if (result.state == "success") {
        workOrderData.value = result.data;
      } else {
        common_vendor.index.showToast({
          title: result.msg || "获取数据失败",
          icon: "none"
        });
      }
    };
    const handleViewProtocol = () => {
      const fileParams = {
        fileUrl: encodeURIComponent("http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx"),
        fileType: encodeURIComponent("pdf"),
        fileName: encodeURIComponent("调解协议.pdf"),
        caseNumber: encodeURIComponent(caseNumber.value || "485")
      };
      const paramsString = Object.entries(fileParams).map(([key, value]) => `${key}=${value}`).join("&");
      common_vendor.index.navigateTo({
        url: `/pages/protocol_preview/protocol_preview?${paramsString}`
      });
    };
    const handleViewSvgFile = () => {
      const fileParams = {
        fileUrl: encodeURIComponent("http://192.168.1.101:10010/file_download/svg/sample.svg"),
        fileType: encodeURIComponent("svg"),
        fileName: encodeURIComponent("示例图表.svg"),
        caseNumber: encodeURIComponent(caseNumber.value || "485")
      };
      const paramsString = Object.entries(fileParams).map(([key, value]) => `${key}=${value}`).join("&");
      common_vendor.index.navigateTo({
        url: `/pages/protocol_preview/protocol_preview?${paramsString}`
      });
    };
    const handleNotarization = () => {
      common_vendor.index.navigateTo({
        url: "/pages/agreement_notarization/agreement_notarization"
      });
    };
    const copyMemo = () => {
      common_vendor.index.setClipboardData({
        data: workOrderData.value.paymentMemo
      });
      common_vendor.index.showToast({
        title: "还款备注信息已复制",
        icon: "success"
      });
      isCopied.value = true;
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(basicInfo.value.case_number),
        b: common_vendor.t(basicInfo.value.case_status_cn),
        c: basicInfo.value.case_status_cn === "已完成" ? 1 : "",
        d: common_vendor.t(basicInfo.value.initiate_date),
        e: workOrderData.value.notarization_status_cn === "未公证"
      }, workOrderData.value.notarization_status_cn === "未公证" ? {
        f: common_vendor.t(workOrderData.value.notarization_status_cn),
        g: common_vendor.o(handleNotarization)
      } : {}, {
        h: workOrderData.value.notarization_status_cn === "已公证"
      }, workOrderData.value.notarization_status_cn === "已公证" ? {} : {}, {
        i: common_vendor.f(workOrderData.value.mediation_info, (info, k0, i0) => {
          return {
            a: common_vendor.t(info.title),
            b: common_vendor.t(info.value),
            c: info.id
          };
        }),
        j: common_vendor.f(workOrderData.value.mediation_plan, (plan, k0, i0) => {
          return {
            a: common_vendor.t(plan.title),
            b: common_vendor.t(plan.value),
            c: common_vendor.n(plan.title == "减免金额" ? "reduction_amount" : ""),
            d: plan.id
          };
        }),
        k: common_vendor.t(workOrderData.value.repayment_note),
        l: !isCopied.value
      }, !isCopied.value ? {
        m: common_vendor.o(copyMemo)
      } : {}, {
        n: common_vendor.o(handleViewProtocol),
        o: common_vendor.o(handleViewSvgFile)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1cf6b09e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/case_completed/case_completed.js.map
