// 环境配置管理
// 支持开发、测试、生产环境的API地址配置

// 环境类型枚举
export const ENV_TYPE = {
  DEVELOPMENT: 'development',
  TESTING: 'testing', 
  PRODUCTION: 'production'
};

// 环境配置
const envConfig = {
  // 开发环境
  [ENV_TYPE.DEVELOPMENT]: {
    name: '开发环境',
    // baseURL: 'https://www.eeclat.cn',
    baseURL: 'http://192.168.1.101:14010',
    wechatAppId: '', // 微信小程序AppID - 开发环境
    debug: true,
    timeout: 10000
  },
  
  // 测试环境  
  [ENV_TYPE.TESTING]: {
    name: '测试环境',
    baseURL: 'http://192.168.1.101:14010',
    baseURL: 'https://www.eeclat.cn',
    wechatAppId: '', // 微信小程序AppID - 测试环境
    debug: true,
    timeout: 15000
  },
  
  // 生产环境
  [ENV_TYPE.PRODUCTION]: {
    name: '生产环境', 
    baseURL: 'http://192.168.1.101:14010',
    // baseURL: 'https://www.eeclat.cn',
    wechatAppId: '', // 微信小程序AppID - 生产环境
    debug: false,
    timeout: 20000
  }
};

// 当前环境 - 可通过构建工具或手动切换
let currentEnv = ENV_TYPE.DEVELOPMENT;

// 自动检测环境（基于域名或其他标识）
function detectEnvironment() {
  // 在微信小程序中可以通过不同的方式检测环境
  // 这里提供一个基础的检测逻辑
  try {
    const accountInfo = wx.getAccountInfoSync();
    const envVersion = accountInfo.miniProgram.envVersion;
    
    switch (envVersion) {
      case 'develop':
        return ENV_TYPE.DEVELOPMENT;
      case 'trial':
        return ENV_TYPE.TESTING;
      case 'release':
        return ENV_TYPE.PRODUCTION;
      default:
        return ENV_TYPE.DEVELOPMENT;
    }
  } catch (error) {
    console.warn('环境检测失败，使用默认开发环境:', error);
    return ENV_TYPE.DEVELOPMENT;
  }
}

// 初始化环境
function initEnvironment() {
  currentEnv = detectEnvironment();
  console.log(`当前环境: ${getCurrentConfig().name}`);
}

// 获取当前环境配置
export function getCurrentConfig() {
  return envConfig[currentEnv];
}

// 获取当前环境类型
export function getCurrentEnv() {
  return currentEnv;
}

// 手动设置环境（用于调试）
export function setEnvironment(env) {
  if (envConfig[env]) {
    currentEnv = env;
    console.log(`环境已切换到: ${getCurrentConfig().name}`);
    return true;
  }
  console.error('无效的环境类型:', env);
  return false;
}

// 获取API基础地址
export function getBaseURL() {
  return getCurrentConfig().baseURL;
}

// 获取微信AppID
export function getWechatAppId() {
  return getCurrentConfig().wechatAppId;
}

// 是否为调试模式
export function isDebug() {
  return getCurrentConfig().debug;
}

// 获取请求超时时间
export function getTimeout() {
  return getCurrentConfig().timeout;
}

// 获取所有环境配置（用于环境切换界面）
export function getAllEnvConfigs() {
  return Object.keys(envConfig).map(key => ({
    type: key,
    ...envConfig[key]
  }));
}

// 环境配置验证
export function validateConfig() {
  const config = getCurrentConfig();
  const errors = [];
  
  if (!config.baseURL) {
    errors.push('API基础地址未配置');
  }
  
  if (!config.wechatAppId) {
    errors.push('微信AppID未配置');
  }
  
  if (errors.length > 0) {
    console.warn('环境配置验证失败:', errors);
    return { valid: false, errors };
  }
  
  return { valid: true, errors: [] };
}

// 初始化环境配置
initEnvironment();

// 导出默认配置
export default {
  getCurrentConfig,
  getCurrentEnv,
  setEnvironment,
  getBaseURL,
  getWechatAppId,
  isDebug,
  getTimeout,
  getAllEnvConfigs,
  validateConfig,
  ENV_TYPE
};
