"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
if (!Array) {
  const _easycom_uni_data_checkbox2 = common_vendor.resolveComponent("uni-data-checkbox");
  _easycom_uni_data_checkbox2();
}
const _easycom_uni_data_checkbox = () => "../../node-modules/@dcloudio/uni-ui/lib/uni-data-checkbox/uni-data-checkbox.js";
if (!Math) {
  (_easycom_uni_data_checkbox + CanvasAutograph)();
}
const CanvasAutograph = () => "../../components/canvas-autograph/canvas-autograph.js";
const _sfc_main = {
  __name: "agreement_signing",
  setup(__props) {
    const caseNumber = common_vendor.ref("");
    const initiateDate = common_vendor.ref("");
    const closeDate = common_vendor.ref("");
    const caseStatus = common_vendor.ref("");
    const workOrderData = common_vendor.ref({});
    const isSigned = common_vendor.ref(false);
    const isLoadingPreview = common_vendor.ref(false);
    const preview = common_vendor.computed(() => {
      return {
        icon: "fas fa-expand",
        text: "全屏查看协议内容",
        class: "preview-btn"
      };
    });
    const agreementStatus = common_vendor.ref([]);
    const signatureData = common_vendor.ref("");
    const isLoading = common_vendor.ref(false);
    const hobbys = common_vendor.ref([
      {
        text: "我已阅读并同意《调解协议》的全部条款",
        value: "1",
        disabled: false
      }
    ]);
    const checkboxData = common_vendor.computed(() => {
      if (isSigned.value) {
        return [
          {
            text: "协议签署完成",
            value: 0,
            disabled: true
          }
        ];
      }
      return hobbys.value;
    });
    const buttonConfig = common_vendor.computed(() => {
      if (isSigned.value) {
        return {
          icon: "fas fa-check-circle",
          text: "协议签署完成",
          class: "success-btn"
        };
      }
      return {
        icon: "fas fa-file-signature",
        text: "确认协议签署",
        class: !agreementStatus.value.includes("1") ? "disabled-btn" : "sign-btn"
      };
    });
    const saveSignatureState = () => {
      try {
        const stateData = {
          isSigned: isSigned.value,
          agreementStatus: agreementStatus.value,
          signatureData: signatureData.value,
          timestamp: Date.now()
        };
        common_vendor.index.setStorageSync("agreement_signature_state", JSON.stringify(stateData));
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:227", "保存签名状态失败:", error);
      }
    };
    const loadSignatureState = () => {
      try {
        const savedState = common_vendor.index.getStorageSync("agreement_signature_state");
        if (savedState) {
          const stateData = JSON.parse(savedState);
          if (Date.now() - stateData.timestamp < 24 * 60 * 60 * 1e3) {
            isSigned.value = stateData.isSigned || false;
            agreementStatus.value = stateData.agreementStatus || [];
            signatureData.value = stateData.signatureData || "";
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:247", "加载签名状态失败:", error);
      }
    };
    const handlePreview = async () => {
      try {
        isLoadingPreview.value = true;
        common_vendor.index.navigateTo({
          url: `/pages/protocol_preview/protocol_preview`
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:283", "跳转失败:", error);
        common_vendor.index.showToast({
          title: "跳转失败，请重试",
          icon: "error"
        });
      } finally {
        isLoadingPreview.value = false;
      }
    };
    const handleConfirm = async () => {
      try {
        if (isSigned.value) {
          isLoading.value = true;
          const isAgreementCompleted = buttonConfig.value.text === "协议签署完成";
          if (isAgreementCompleted) {
            await new Promise((resolve) => setTimeout(resolve, 1500));
            common_vendor.index.showToast({
              title: "协议确认完成",
              icon: "success",
              duration: 1500
            });
            setTimeout(() => {
              common_vendor.index.navigateTo({
                url: "/pages/contact_information/contact_information"
              });
            }, 1500);
          } else {
            common_vendor.index.showToast({
              title: "操作完成",
              icon: "success"
            });
          }
          return;
        }
        if (agreementStatus.value.includes("1")) {
          common_vendor.index.showModal({
            title: "提示",
            content: "您已同意协议条款，请进行电子签名以完成签署流程",
            confirmText: "去签名",
            cancelText: "稍后",
            success: (res) => {
              if (res.confirm) {
                openCanvas();
              }
            }
          });
        } else {
          common_vendor.index.showToast({
            title: "请先勾选同意协议条款",
            icon: "none"
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:353", "处理确认操作失败:", error);
        common_vendor.index.showToast({
          title: "操作失败，请重试",
          icon: "error"
        });
      } finally {
        isLoading.value = false;
      }
    };
    const isCanvas = common_vendor.ref(false);
    const complete = async (signatureBase64) => {
      try {
        isLoading.value = true;
        common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:377", "签名数据:", signatureBase64);
        signatureData.value = signatureBase64;
        await saveElectronicSignature(signatureBase64);
        isSigned.value = true;
        agreementStatus.value = ["1"];
        saveSignatureState();
        common_vendor.index.showToast({
          title: "签名保存成功",
          icon: "success"
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:397", "处理签名失败:", error);
        common_vendor.index.showToast({
          title: "签名保存失败，请重试",
          icon: "error"
        });
        isSigned.value = false;
        agreementStatus.value = [];
        signatureData.value = "";
      } finally {
        isLoading.value = false;
      }
    };
    const saveElectronicSignature = async (signatureBase64) => {
      if (!caseNumber.value) {
        throw new Error("案件号不能为空");
      }
      try {
        const filePath = await base64ToTempFile(signatureBase64);
        const token = common_vendor.index.getStorageSync("access_token") || common_vendor.index.getStorageSync("token");
        const tokenType = common_vendor.index.getStorageSync("token_type") || "Bearer";
        const apiUrl = utils_api.api.electronicSignature.getUpdateSignatureUrl(caseNumber.value);
        common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:431", "开始上传电子签名:", {
          url: apiUrl,
          filePath,
          caseNumber: caseNumber.value
        });
        return new Promise((resolve, reject) => {
          common_vendor.index.uploadFile({
            url: apiUrl,
            filePath,
            name: "electronic_signature",
            // 确保参数名为 electronic_signature
            method: "PUT",
            // 使用PUT方法
            formData: {
              case_number: caseNumber.value
            },
            header: {
              "Authorization": token ? `${tokenType} ${token}` : ""
            },
            success: (res) => {
              common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:451", "电子签名上传响应:", res);
              if (res.statusCode === 200 || res.statusCode === 201) {
                try {
                  const responseData = JSON.parse(res.data);
                  if (responseData.success !== false) {
                    common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:458", "电子签名保存成功:", responseData);
                    resolve(responseData);
                  } else {
                    reject(new Error(responseData.message || "保存电子签名失败"));
                  }
                } catch (parseError) {
                  common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:465", "响应解析失败，但状态码正常，认为上传成功");
                  resolve({ success: true, data: res.data });
                }
              } else {
                reject(new Error(`服务器错误: HTTP ${res.statusCode}`));
              }
            },
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:473", "电子签名上传失败:", error);
              reject(new Error(error.errMsg || "网络请求失败"));
            }
          });
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:479", "保存电子签名失败:", error);
        throw error;
      }
    };
    const base64ToTempFile = (base64Data) => {
      return new Promise((resolve, reject) => {
        try {
          const base64 = base64Data.replace(/^data:image\/\w+;base64,/, "");
          const fileName = `electronic_signature_${caseNumber.value}_${Date.now()}.png`;
          const filePath = `${common_vendor.wx$1.env.USER_DATA_PATH}/${fileName}`;
          common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:499", "开始创建临时文件:", {
            fileName,
            filePath,
            base64Length: base64.length
          });
          const fs = common_vendor.wx$1.getFileSystemManager();
          fs.writeFile({
            filePath,
            data: base64,
            encoding: "base64",
            // 指定编码为base64
            success: () => {
              common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:512", "临时文件创建成功:", {
                filePath,
                fileName
              });
              resolve(filePath);
            },
            fail: (error) => {
              common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:519", "创建临时文件失败:", error);
              reject(new Error(`创建临时文件失败: ${error.errMsg || error.message}`));
            }
          });
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:524", "base64转文件处理失败:", error);
          reject(new Error(`base64转文件失败: ${error.message}`));
        }
      });
    };
    const openCanvas = () => {
      try {
        if (isSigned.value) {
          common_vendor.index.showToast({
            title: "协议已签署，无需重复操作",
            icon: "none"
          });
          return;
        }
        if (isLoading.value) {
          common_vendor.index.showToast({
            title: "正在处理中，请稍候",
            icon: "loading"
          });
          return;
        }
        isCanvas.value = true;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:554", "打开签名界面失败:", error);
        common_vendor.index.showToast({
          title: "打开签名界面失败",
          icon: "error"
        });
      }
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:564", "协议签署页面参数:", options);
      if (options.case_number) {
        try {
          caseNumber.value = decodeURIComponent(options.case_number);
          common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:570", "接收到调解ID:", caseNumber.value);
          workOrderData.value.id = caseNumber.value;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:574", "case_number参数解码失败:", error);
          caseNumber.value = options.case_number;
          workOrderData.value.id = options.case_number;
        }
      }
      if (options.initiate_date) {
        try {
          initiateDate.value = decodeURIComponent(options.initiate_date);
          common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:584", "接收到日期:", initiateDate.value);
          workOrderData.value.createDate = initiateDate.value;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:588", "initiate_date参数解码失败:", error);
          initiateDate.value = options.initiate_date;
          workOrderData.value.createDate = options.initiate_date;
        }
      }
      if (options.close_date) {
        try {
          closeDate.value = decodeURIComponent(options.close_date);
          common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:598", "关闭日期:", closeDate.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:600", "close_date参数解码失败:", error);
          closeDate.value = options.close_date;
        }
      }
      if (options.case_status_cn) {
        try {
          caseStatus.value = decodeURIComponent(options.case_status_cn);
          common_vendor.index.__f__("log", "at pages/agreement_signing/agreement_signing.vue:609", "接收到调解状态:", caseStatus.value);
          workOrderData.value.status = caseStatus.value;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/agreement_signing/agreement_signing.vue:613", "case_status_cn参数解码失败:", error);
          caseStatus.value = options.case_status_cn;
          workOrderData.value.status = options.case_status_cn;
        }
      }
    });
    common_vendor.onMounted(() => {
      loadSignatureState();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(workOrderData.value.id || "MED20230001"),
        b: common_vendor.t(workOrderData.value.status || "进行中"),
        c: workOrderData.value.status === "进行中" ? 1 : "",
        d: common_vendor.t(workOrderData.value.createDate || "2023-11-01"),
        e: isLoadingPreview.value
      }, isLoadingPreview.value ? {} : {
        f: common_vendor.n(preview.value.icon)
      }, {
        g: common_vendor.t(isLoadingPreview.value ? "正在加载PDF" : preview.value.text),
        h: common_vendor.o(handlePreview),
        i: common_vendor.t(isSigned.value ? "协议签署已完成" : "请勾选下方确认项进行电子签署"),
        j: common_vendor.o(($event) => agreementStatus.value = $event),
        k: common_vendor.p({
          multiple: true,
          localdata: checkboxData.value,
          disabled: isSigned.value,
          modelValue: agreementStatus.value
        }),
        l: common_vendor.t(isSigned.value ? "电子签名已生效，协议具有法律效力" : "点击此处进入电子签名界面"),
        m: isSigned.value ? 1 : "",
        n: isSigned.value ? 1 : "",
        o: common_vendor.o(openCanvas),
        p: isLoading.value
      }, isLoading.value ? {} : {
        q: common_vendor.n(buttonConfig.value.icon)
      }, {
        r: common_vendor.t(isLoading.value ? "正在确认..." : buttonConfig.value.text),
        s: common_vendor.n(buttonConfig.value.class),
        t: common_vendor.o(handleConfirm),
        v: isLoading.value || !isSigned.value && !agreementStatus.value.includes("1"),
        w: common_vendor.o(complete),
        x: common_vendor.o(($event) => isCanvas.value = $event),
        y: common_vendor.p({
          showSignatureLine: true,
          signatureLabel: "签名：",
          signatureLineY: 60,
          signatureLineColor: "#2979ff",
          modelValue: isCanvas.value
        })
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dd2a7f81"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/agreement_signing/agreement_signing.js.map
