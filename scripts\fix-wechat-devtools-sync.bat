@echo off
chcp 65001 >nul
echo ===============================================
echo 🔧 修复微信开发者工具真机调试同步问题
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 当前项目目录: %CD%
echo.

echo 📋 问题分析：
echo   webview.js文件编译成功，但真机调试时找不到文件
echo   这通常是微信开发者工具的缓存或同步问题
echo.

echo 🚀 开始修复流程...
echo.

echo 📁 第一步：清理本地编译缓存...
if exist "unpackage\dist\dev\mp-weixin" (
    echo 正在删除微信小程序编译缓存...
    rmdir /s /q "unpackage\dist\dev\mp-weixin" 2>nul
    echo ✅ 本地编译缓存已清理
) else (
    echo ℹ️ 本地编译缓存不存在
)

if exist ".hbuilderx" (
    rmdir /s /q ".hbuilderx" 2>nul
    echo ✅ HBuilderX缓存已清理
)

echo.
echo 📱 第二步：微信开发者工具设置检查清单
echo.
echo ⚠️ 请在微信开发者工具中依次检查以下设置：
echo.
echo 1️⃣ 【构建设置】
echo    - 菜单栏 → 工具 → 构建npm
echo    - 点击"构建npm"按钮
echo    - 确认构建成功
echo.
echo 2️⃣ 【缓存清理】  
echo    - 菜单栏 → 工具 → 清缓存
echo    - 选择"清除所有缓存"
echo    - 等待清理完成
echo.
echo 3️⃣ 【编译设置】
echo    - 菜单栏 → 设置 → 项目设置
echo    - 关闭"增量编译"（如果开启）
echo    - 开启"使用工具安装的npm"
echo    - 开启"启用热重载"
echo.
echo 4️⃣ 【真机调试设置】
echo    - 菜单栏 → 工具 → 真机调试
echo    - 重新扫码连接手机
echo    - 确认"自动推送"已开启
echo.
echo 5️⃣ 【重新编译】
echo    - 在HBuilderX中重新运行项目
echo    - 等待完全编译完成
echo    - 在开发者工具中点击"编译"
echo.
echo 6️⃣ 【最终验证】
echo    - 在真机上测试webview页面导航
echo    - 查看控制台是否有"webview页面接收参数"日志
echo.
echo ===============================================
echo ✨ 设置检查完成！
echo ===============================================
echo.
echo 📋 如果问题仍然存在：
echo.
echo 🔄 备用方案1：重装微信开发者工具
echo   - 完全卸载微信开发者工具
echo   - 重新下载安装最新版本
echo.
echo 🔄 备用方案2：使用临时跳转方案
echo   - 运行 fix-webview-fallback.bat
echo   - 临时使用外部浏览器打开链接
echo.
echo ⚠️ 重要提示：
echo   真机调试问题有时需要多次尝试
echo   建议先在模拟器中测试功能是否正常
echo.
echo ===============================================
pause 