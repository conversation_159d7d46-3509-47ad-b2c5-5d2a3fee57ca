<template>
  <view class="container">
    <view class="header">
      <text class="title">人脸识别认证</text>
    </view>

    <view class="notification">
      <text>当前业务需要人脸识别验证</text>
    </view>

    <view class="card-icon">
      <image src="/static/face-id-icon.png" class="icon" />
    </view>

    <view class="form-item">
      <text class="label">姓名</text>
      <input
        class="input-value"
        :class="{ 'input-error': nameError, 'input-disabled': nameDisabled }"
        placeholder="请输入姓名"
        v-model="formData.name"
        :disabled="nameDisabled"
        @input="onNameInput"
        @blur="validateName"
      />
      <text v-if="nameError" class="error-text">{{ nameError }}</text>
    </view>

    <view class="form-item">
      <text class="label">身份证号码</text>
      <input
        class="input-value"
        :class="{ 'input-error': cardError, 'input-disabled': cardDisabled }"
        placeholder="请输入身份证号码"
        v-model="formData.card"
        :disabled="cardDisabled"
        @input="onCardInput"
        @blur="validateCard"
        maxlength="18"
      />
      <text v-if="cardError" class="error-text">{{ cardError }}</text>
    </view>

    <view class="footer">
      <view class="agreement">
        <checkbox-group @change="toggleAgreement">
          <checkbox :checked="isAgreed" color="#2979FF" />
        </checkbox-group>
        <text>我已阅读并同意遵循</text>
        <text class="link" @tap="navigateTo('user_agreement')">《用户服务协议》</text>
        <text>和</text>
        <text class="link" @tap="navigateTo('privacy_policy')">《个人信息保护政策》</text>
      </view>
      <button 
        class="action-btn" 
        :disabled="!canSubmit" 
        :class="{ 'disabled': !canSubmit }"
        @tap="startFaceRecognition"
      >
        {{ isLoading ? '验证中...' : '开始人脸识别验证' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { api } from '@/utils/api.js';
import wechatAuth from '@/utils/wechat-auth.js';
// HTTP请求拦截器模块
import { getBaseURL, getTimeout, isDebug } from '@/config/env.js';

// 表单数据 - 修复phoneForm未定义问题
const formData = ref({
  name: '',
  card: ''
});

// 验证状态
const nameError = ref('');
const cardError = ref('');
const isAgreed = ref(false);
const isLoading = ref(false);

// 输入框禁用状态 - 用于URL参数预填充时禁用编辑
const nameDisabled = ref(false);
const cardDisabled = ref(false);

// 计算是否可以提交 - 实现按钮状态控制
const canSubmit = computed(() => {
  return isAgreed.value && 
         formData.value.name.trim() && 
         formData.value.card.trim() && 
         !nameError.value && 
         !cardError.value &&
         !isLoading.value;
});

// 姓名输入处理
const onNameInput = () => {
  // 清除之前的错误提示
  if (nameError.value) {
    nameError.value = '';
  }
};

// 姓名验证
const validateName = () => {
  const name = formData.value.name.trim();
  if (!name) {
    nameError.value = '请输入姓名';
    return false;
  }
  if (name.length < 2) {
    nameError.value = '姓名至少2个字符';
    return false;
  }
  if (name.length > 10) {
    nameError.value = '姓名不能超过10个字符';
    return false;
  }
  // 检查是否包含特殊字符
  if (!/^[\u4e00-\u9fa5a-zA-Z]+$/.test(name)) {
    nameError.value = '姓名只能包含中文或英文';
    return false;
  }
  nameError.value = '';
  return true;
};

// 身份证号输入处理
const onCardInput = () => {
  // 清除之前的错误提示
  if (cardError.value) {
    cardError.value = '';
  }
  // 转换为大写
  formData.value.card = formData.value.card.toUpperCase();
};

// 身份证号验证 - 实现格式校验
const validateCard = () => {
  const card = formData.value.card.trim();
  if (!card) {
    cardError.value = '请输入身份证号码';
    return false;
  }
  
  // 身份证号格式验证（18位，最后一位可为X）
  const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  
  if (!idCardRegex.test(card)) {
    cardError.value = '身份证号码格式不正确';
    return false;
  }
  
  // 身份证校验码验证
  if (!validateIdCardChecksum(card)) {
    cardError.value = '身份证号码校验失败';
    return false;
  }
  
  cardError.value = '';
  return true;
};

// 身份证校验码验证
const validateIdCardChecksum = (idCard) => {
  const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
  
  let sum = 0;
  for (let i = 0; i < 17; i++) {
    sum += parseInt(idCard[i]) * weights[i];
  }
  
  const checkCode = checkCodes[sum % 11];
  return checkCode === idCard[17];
};

// 协议同意状态切换
const toggleAgreement = () => {
  isAgreed.value = !isAgreed.value;
};

// 页面导航
const navigateTo = (page) => {
  uni.navigateTo({
    url: `/pages/${page}/${page}`
  });
};

// 记录操作日志
/* const recordOperation = async (buttonName, details = {}) => {
  try {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    
    await api.operationLog.recordOperation({
      menu_name: '人脸识别认证',
      button_name: buttonName,
      browser_path: `/${currentPage.route}`,
      operation_details: JSON.stringify({
        ...details,
        timestamp: new Date().toISOString()
      })
    });
  } catch (error) {
    console.warn('记录操作日志失败:', error);
  }
}; */

// 获取用户openid
const getUserOpenid = () => {
  try {
    // 优先从wechatAuth获取
    if (wechatAuth.openid) {
      return wechatAuth.openid;
    }
    
    // 从存储中获取
    const openid = uni.getStorageSync('openid');
    if (openid) {
      return openid;
    }
    
    throw new Error('未获取到用户openid');
  } catch (error) {
    console.error('获取openid失败:', error);
    throw error;
  }
};

// 开始人脸识别验证 - 实现完整的接口调用逻辑
const startFaceRecognition = async () => {
  // 记录按钮点击操作
  /* await recordOperation('开始人脸识别验证', {
    name: formData.value.name,
    hasIdCard: !!formData.value.card
  }); */

  // 最终表单验证
  if (!validateName() || !validateCard()) {
    uni.showToast({
      title: '请填写正确的信息',
      icon: 'none',
      duration: 2000
    });
    return;
  }

  isLoading.value = true;

  try {
    // 获取用户openid
    const openid = getUserOpenid();

    // 调用人脸核身接口 - 使用统一的API管理
    const response = await api.wechat.faceIdAuth({
      openid: openid,
      name: formData.value.name.trim(),
      id_card: formData.value.card.trim()
    });

    // API统一管理已处理HTTP状态码，这里直接处理业务数据
    console.log('人脸核身接口响应:', response);
    // 检查响应是否包含验证URL
    if (response.data) {
        // 记录成功操作
        /* await recordOperation('人脸识别验证_跳转成功', {
          hasUrl: true
        }); */
        
        // 跳转到腾讯云人脸核身页面responseData = https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx1f7125112b74db52&redirect_uri=https%3A%2F%2Fopen.faceid.qq.com%2Fv1%2Fapi%2FgetCode%3FbizRedirect%3Dhttps%253A%252F%252Ffaceid.qq.com%252Fapi%252Fauth%252FgetOpenidAndSaveToken%253Ftoken%253DF0F626E2-285F-470A-A6E6-4780FBE8D098&response_type=code&scope=snsapi_base&state=&component_appid=wx9802ee81e68d6dee#wechat_redirect
        /* uni.redirectTo({
          url: responseData.data
        }); */
        uni.navigateTo({
          url: `/pages/webview/webview?url=${encodeURIComponent(response.data)}`
        });
      } else {
        throw new Error(response.msg || '获取验证地址失败');
      }
  } catch (error) {
    console.error('人脸识别验证失败:', error);
    
    // 记录失败操作
    /* await recordOperation('人脸识别验证_失败', {
      error: error.msg
    }); */
    
    // 显示用户友好的错误提示
    let errorMessage = '验证失败，请重试';
    
    /* if (error.message.includes('openid')) {
      errorMessage = '用户身份验证失败，请重新登录';
    } else if (error.message.includes('网络')) {
      errorMessage = '网络连接失败，请检查网络设置';
    } else if (error.message.includes('超时')) {
      errorMessage = '请求超时，请重试';
    } */
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 3000
    });
  } finally {
    isLoading.value = false;
  }
};

/* // 获取环境配置中的baseURL
const getBaseURL = () => {
  try {
    const { getBaseURL } = require('@/config/env.js');
    return getBaseURL();
  } catch (error) {
    console.warn('获取baseURL失败，使用默认值');
    return 'https://your-backend-api.com';
  }
}; */

// 模拟Vue Router的$route.query对象
const $route = ref({
  query: {}
});

// 解析URL参数并填充表单
const parseUrlParams = () => {
  try {
    console.log('开始解析URL查询参数...');
    console.log('当前$route.query:', $route.value.query);

    // 处理name参数 - 需要URL解码
    if ($route.value.query.name) {
      try {
        // URL解码处理
        const decodedName = decodeURIComponent($route.value.query.name);
        console.log('解析到name参数:', decodedName);

        // 填充到表单
        formData.value.name = decodedName;
        // 设置为禁用状态
        nameDisabled.value = true;

        // 自动验证姓名
        validateName();
      } catch (decodeError) {
        console.error('name参数URL解码失败:', decodeError);
        // 解码失败时使用原始值
        formData.value.name = $route.value.query.name;
        nameDisabled.value = true;
        validateName();
      }
    }

    // 处理idCard参数 - 直接使用参数值
    if ($route.value.query.idCard) {
      console.log('解析到idCard参数:', $route.value.query.idCard);

      // 填充到表单
      formData.value.card = $route.value.query.idCard;
      // 设置为禁用状态
      cardDisabled.value = true;

      // 自动验证身份证号
      validateCard();
    }

  } catch (error) {
    console.error('解析URL参数失败:', error);
  }
};

// uni-app页面加载生命周期 - 接收页面参数
onLoad((options) => {
  console.log('页面加载，接收参数:', options);

  // 构建模拟的$route.query对象
  $route.value.query = { ...options } || {};
  console.log('构建$route.query:', $route.value.query);

  // 解析URL查询参数并填充表单
  parseUrlParams();
});

// 页面挂载时的初始化
onMounted(async () => {
  /* try {
    // 记录页面访问
    await recordOperation('页面访问', {
      pageName: '人脸识别认证'
    });
  } catch (error) {
    console.warn('记录页面访问失败:', error);
  } */
});
</script>

<style scoped>
.container {
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  min-height: 100vh;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
}

.notification {
  text-align: center;
  margin-top: 20rpx;
  color: #666666;
  font-size: 14px;
}

.card-icon {
  display: flex;
  justify-content: center;
  margin: 40rpx 0;
}

.icon {
  width: 120rpx;
  height: 120rpx;
  background-color: #2979FF;
  border-radius: 16rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  font-size: 16px;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.input-value {
  font-size: 16px;
  color: #333333;
  border-bottom: 1px solid #eeeeee;
  padding-bottom: 15rpx;
  width: 100%;
}

.input-error {
  border-bottom-color: #ff4757 !important;
}

.input-disabled {
  background-color: #f5f5f5 !important;
  color: #999999 !important;
  cursor: not-allowed;
}

.error-text {
  color: #ff4757;
  font-size: 12px;
  margin-top: 5rpx;
  display: block;
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  background-color: #ffffff;
  border-top: 1px solid #f0f0f0;
}

.agreement {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 30rpx;
  font-size: 14px;
  color: #666666;
}

.link {
  color: #2979FF;
  margin: 0 5rpx;
}

.action-btn {
  background-color: #2979FF;
  color: #ffffff;
  border-radius: 50rpx;
  font-size: 16px;
  transition: all 0.3s ease;
}

.disabled {
  background-color: #CCCCCC !important;
  opacity: 0.7;
}
</style>