{"version": 3, "file": "uni-section.js", "sources": ["node_modules/@dcloudio/uni-ui/lib/uni-section/uni-section.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RDovd29yay_kuI3oia_otYTkuqfns7vnu58vbm9uLXBlcmZvcm1pbmctYXNzZXRzL25vZGVfbW9kdWxlcy9AZGNsb3VkaW8vdW5pLXVpL2xpYi91bmktc2VjdGlvbi91bmktc2VjdGlvbi52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"uni-section\">\r\n\t\t<view class=\"uni-section-header\" @click=\"onClick\">\r\n\t\t\t\t<view class=\"uni-section-header__decoration\" v-if=\"type\" :class=\"type\" />\r\n        <slot v-else name=\"decoration\"></slot>\r\n\r\n        <view class=\"uni-section-header__content\">\r\n          <text :style=\"{'font-size':titleFontSize,'color':titleColor}\" class=\"uni-section__content-title\" :class=\"{'distraction':!subTitle}\">{{ title }}</text>\r\n          <text v-if=\"subTitle\" :style=\"{'font-size':subTitleFontSize,'color':subTitleColor}\" class=\"uni-section-header__content-sub\">{{ subTitle }}</text>\r\n        </view>\r\n\r\n        <view class=\"uni-section-header__slot-right\">\r\n          <slot name=\"right\"></slot>\r\n        </view>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"uni-section-content\" :style=\"{padding: _padding}\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\r\n\t/**\r\n\t * Section 标题栏\r\n\t * @description 标题栏\r\n\t * @property {String} type = [line|circle|square] 标题装饰类型\r\n\t * \t@value line 竖线\r\n\t * \t@value circle 圆形\r\n\t * \t@value square 正方形\r\n\t * @property {String} title 主标题\r\n\t * @property {String} titleFontSize 主标题字体大小\r\n\t * @property {String} titleColor 主标题字体颜色\r\n\t * @property {String} subTitle 副标题\r\n\t * @property {String} subTitleFontSize 副标题字体大小\r\n\t * @property {String} subTitleColor 副标题字体颜色\r\n\t * @property {String} padding 默认插槽 padding\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: 'UniSection',\r\n    emits:['click'],\r\n\t\tprops: {\r\n\t\t\ttype: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\trequired: true,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n      titleFontSize: {\r\n        type: String,\r\n        default: '14px'\r\n      },\r\n\t\t\ttitleColor:{\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333'\r\n\t\t\t},\r\n\t\t\tsubTitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n      subTitleFontSize: {\r\n        type: String,\r\n        default: '12px'\r\n      },\r\n      subTitleColor: {\r\n        type: String,\r\n        default: '#999'\r\n      },\r\n\t\t\tpadding: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n    computed:{\r\n      _padding(){\r\n        if(typeof this.padding === 'string'){\r\n          return this.padding\r\n        }\r\n\r\n        return this.padding?'10px':''\r\n      }\r\n    },\r\n\t\twatch: {\r\n\t\t\ttitle(newVal) {\r\n\t\t\t\tif (uni.report && newVal !== '') {\r\n\t\t\t\t\tuni.report('title', newVal)\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n    methods: {\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" >\r\n\t$uni-primary: #2979ff !default;\r\n\r\n\t.uni-section {\r\n\t\tbackground-color: #fff;\r\n    .uni-section-header {\r\n      position: relative;\r\n      /* #ifndef APP-NVUE */\r\n      display: flex;\r\n      /* #endif */\r\n      flex-direction: row;\r\n      align-items: center;\r\n      padding: 12px 10px;\r\n      font-weight: normal;\r\n\r\n      &__decoration{\r\n        margin-right: 6px;\r\n        background-color: $uni-primary;\r\n        &.line {\r\n          width: 4px;\r\n          height: 12px;\r\n          border-radius: 10px;\r\n        }\r\n\r\n        &.circle {\r\n          width: 8px;\r\n          height: 8px;\r\n          border-top-right-radius: 50px;\r\n          border-top-left-radius: 50px;\r\n          border-bottom-left-radius: 50px;\r\n          border-bottom-right-radius: 50px;\r\n        }\r\n\r\n        &.square {\r\n          width: 8px;\r\n          height: 8px;\r\n        }\r\n      }\r\n\r\n      &__content {\r\n        /* #ifndef APP-NVUE */\r\n        display: flex;\r\n        /* #endif */\r\n        flex-direction: column;\r\n        flex: 1;\r\n        color: #333;\r\n\r\n        .distraction {\r\n          flex-direction: row;\r\n          align-items: center;\r\n        }\r\n        &-sub {\r\n          margin-top: 2px;\r\n        }\r\n      }\r\n\r\n      &__slot-right{\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    .uni-section-content{\r\n      font-size: 14px;\r\n    }\r\n\t}\r\n</style>\r\n", "import Component from 'D:/work/不良资产系统/non-performing-assets/node_modules/@dcloudio/uni-ui/lib/uni-section/uni-section.vue'\nwx.createComponent(Component)"], "names": ["uni"], "mappings": ";;AAwCC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACJ,OAAM,CAAC,OAAO;AAAA,EAChB,OAAO;AAAA,IACN,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS;AAAA,IACT;AAAA,IACE,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACJ,YAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACD,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,IACE,kBAAkB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACD,eAAe;AAAA,MACb,MAAM;AAAA,MACN,SAAS;AAAA,IACV;AAAA,IACJ,SAAS;AAAA,MACR,MAAM,CAAC,SAAS,MAAM;AAAA,MACtB,SAAS;AAAA,IACV;AAAA,EACA;AAAA,EACC,UAAS;AAAA,IACP,WAAU;AACR,UAAG,OAAO,KAAK,YAAY,UAAS;AAClC,eAAO,KAAK;AAAA,MACd;AAEA,aAAO,KAAK,UAAQ,SAAO;AAAA,IAC7B;AAAA,EACD;AAAA,EACH,OAAO;AAAA,IACN,MAAM,QAAQ;AACb,UAAIA,oBAAI,UAAU,WAAW,IAAI;AAChCA,4BAAI,OAAO,SAAS,MAAM;AAAA,MAC3B;AAAA,IACD;AAAA,EACA;AAAA,EACC,SAAS;AAAA,IACV,UAAU;AACT,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;AClGD,GAAG,gBAAgB,SAAS;", "x_google_ignoreList": [0]}