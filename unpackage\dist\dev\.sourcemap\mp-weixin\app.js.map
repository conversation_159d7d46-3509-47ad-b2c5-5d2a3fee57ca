{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup>\r\nimport { onLaunch, onShow, onHide } from '@dcloudio/uni-app';\r\nimport { initApp as initializeApp, destroyApp } from '@/utils/app-initializer.js';\r\nimport { isDebug } from '@/config/env.js';\r\n\r\n// 全局数据\r\nconst globalData = {\r\n\tuserInfo: null,\r\n\tsystemInfo: null,\r\n\tappInitialized: false\r\n};\r\n\r\n// 将globalData挂载到getApp()上\r\nuni.getApp = () => ({\r\n\tglobalData\r\n});\r\n\r\n// 初始化应用 - 增强版本\r\nasync function initApp() {\r\n\ttry {\r\n\t\tif (isDebug()) {\r\n\t\t\tconsole.log('开始应用初始化...');\r\n\t\t}\r\n\r\n\t\t// 获取系统信息\r\n\t\ttry {\r\n\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\tglobalData.systemInfo = systemInfo;\r\n\r\n\t\t\tif (isDebug()) {\r\n\t\t\t\tconsole.log('系统信息:', systemInfo);\r\n\t\t\t}\r\n\t\t} catch (e) {\r\n\t\t\tconsole.error('获取系统信息失败:', e);\r\n\t\t}\r\n\r\n\t\t// 使用新的应用初始化器\r\n\t\tconst result = await initializeApp();\r\n\r\n\t\tif (result.success) {\r\n\t\t\tglobalData.appInitialized = true;\r\n\r\n\t\t\tif (isDebug()) {\r\n\t\t\t\tconsole.log('应用初始化成功');\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tconsole.error('应用初始化失败:', result.error);\r\n\t\t}\r\n\t} catch (error) {\r\n\t\tconsole.error('应用初始化异常:', error);\r\n\t}\r\n}\r\n\r\n// 生命周期钩子\r\nonLaunch(() => {\r\n\tif (isDebug()) {\r\n\t\tconsole.log('App Launch - 不良资产系统');\r\n\t}\r\n\r\n\t// 初始化应用\r\n\tinitApp();\r\n});\r\n\r\nonShow(() => {\r\n\tif (isDebug()) {\r\n\t\tconsole.log('App Show');\r\n\t}\r\n\r\n\t// 应用显示时的处理已在app-initializer中处理\r\n});\r\n\r\nonHide(() => {\r\n\tif (isDebug()) {\r\n\t\tconsole.log('App Hide');\r\n\t}\r\n\r\n\t// 应用隐藏时的处理已在app-initializer中处理\r\n});\r\n\r\n// 应用销毁时清理资源\r\nif (typeof uni !== 'undefined' && uni.onAppDestroy) {\r\n\tuni.onAppDestroy(() => {\r\n\t\tif (isDebug()) {\r\n\t\t\tconsole.log('App Destroy');\r\n\t\t}\r\n\r\n\t\tdestroyApp();\r\n\t});\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t/*每个页面公共css */\r\n\t@import './uni.scss';\r\n\t@import './static/font/iconfont.wxss';\r\n\t@import './static/font/icon-fallback.wxss';\r\n\t@import './static/font/fontawesome/fontawesome.css';\r\n\t\r\n\t/* 全局样式 */\r\n\tpage {\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #333;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tfont-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;\r\n\t}\r\n\t\r\n\t/* 通用样式 */\r\n\t.container {\r\n\t\twidth: 100%;\r\n\t\tmin-height: 100vh;\r\n\t}\r\n\t\r\n\t/* 卡片样式 */\r\n\t.card {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);\r\n\t}\r\n\t\r\n\t/* 按钮样式 */\r\n\t.btn {\r\n\t\theight: 80rpx;\r\n\t\tline-height: 80rpx;\r\n\t\ttext-align: center;\r\n\t\tborder-radius: 40rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #ffffff;\r\n\t}\r\n\t\r\n\t.btn-primary {\r\n\t\tbackground-color: $uni-color-primary;\r\n\t}\r\n\t\r\n\t.btn-success {\r\n\t\tbackground-color: $uni-color-success;\r\n\t}\r\n\t\r\n\t/* 文本溢出省略号 */\r\n\t.text-ellipsis {\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n\t\r\n\t/* flex布局工具类 */\r\n\t.flex-row {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t}\r\n\t\r\n\t.flex-column {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\t\r\n\t.flex-center {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t.space-between {\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\t\r\n\t.align-center {\r\n\t\talign-items: center;\r\n\t}\r\n\t\r\n\t/* 边距工具类 */\r\n\t.mt-10 {\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\t\r\n\t.mb-10 {\r\n\t\tmargin-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.ml-10 {\r\n\t\tmargin-left: 10rpx;\r\n\t}\r\n\t\r\n\t.mr-10 {\r\n\t\tmargin-right: 10rpx;\r\n\t}\r\n\t\r\n\t.pt-10 {\r\n\t\tpadding-top: 10rpx;\r\n\t}\r\n\t\r\n\t.pb-10 {\r\n\t\tpadding-bottom: 10rpx;\r\n\t}\r\n\t\r\n\t.pl-10 {\r\n\t\tpadding-left: 10rpx;\r\n\t}\r\n\t\r\n\t.pr-10 {\r\n\t\tpadding-right: 10rpx;\r\n\t}\r\n</style>\r\n", "import App from './App'\n\n// #ifndef VUE3\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()\n// #endif\n\n// #ifdef VUE3\nimport { createSSRApp } from 'vue'\nexport function createApp() {\n  const app = createSSRApp(App)\n  return {\n    app\n  }\n}\n// #endif"], "names": ["uni", "isDebug", "initializeApp", "onLaunch", "onShow", "onHide", "destroyApp", "createSSRApp", "App"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,UAAM,aAAa;AAAA,MAClB,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,gBAAgB;AAAA,IACjB;AAGAA,kBAAG,MAAC,SAAS,OAAO;AAAA,MACnB;AAAA,IACD;AAGA,mBAAe,UAAU;AACxB,UAAI;AACH,YAAIC,WAAO,QAAA,GAAI;AACdD,wBAAAA,MAAA,MAAA,OAAA,iBAAY,YAAY;AAAA,QACxB;AAGD,YAAI;AACH,gBAAM,aAAaA,oBAAI;AACvB,qBAAW,aAAa;AAExB,cAAIC,WAAO,QAAA,GAAI;AACdD,0BAAY,MAAA,MAAA,OAAA,iBAAA,SAAS,UAAU;AAAA,UAC/B;AAAA,QACD,SAAQ,GAAG;AACXA,wBAAA,MAAA,MAAA,SAAA,iBAAc,aAAa,CAAC;AAAA,QAC5B;AAGD,cAAM,SAAS,MAAME,qBAAAA;AAErB,YAAI,OAAO,SAAS;AACnB,qBAAW,iBAAiB;AAE5B,cAAID,WAAO,QAAA,GAAI;AACdD,0BAAAA,MAAA,MAAA,OAAA,iBAAY,SAAS;AAAA,UACrB;AAAA,QACJ,OAAS;AACNA,wBAAc,MAAA,MAAA,SAAA,iBAAA,YAAY,OAAO,KAAK;AAAA,QACtC;AAAA,MACD,SAAQ,OAAO;AACfA,4DAAc,YAAY,KAAK;AAAA,MAC/B;AAAA,IACF;AAGAG,kBAAAA,SAAS,MAAM;AACd,UAAIF,WAAO,QAAA,GAAI;AACdD,sBAAAA,MAAY,MAAA,OAAA,iBAAA,qBAAqB;AAAA,MACjC;AAGD;IACD,CAAC;AAEDI,kBAAAA,OAAO,MAAM;AACZ,UAAIH,WAAO,QAAA,GAAI;AACdD,sBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,MACtB;AAAA,IAGF,CAAC;AAEDK,kBAAAA,OAAO,MAAM;AACZ,UAAIJ,WAAO,QAAA,GAAI;AACdD,sBAAAA,MAAA,MAAA,OAAA,iBAAY,UAAU;AAAA,MACtB;AAAA,IAGF,CAAC;AAGD,QAAI,OAAOA,wBAAQ,eAAeA,cAAG,MAAC,cAAc;AACnDA,oBAAG,MAAC,aAAa,MAAM;AACtB,YAAIC,WAAO,QAAA,GAAI;AACdD,wBAAAA,MAAA,MAAA,OAAA,iBAAY,aAAa;AAAA,QACzB;AAEDM,6BAAAA;MACF,CAAE;AAAA,IACF;;;;;ACzEO,SAAS,YAAY;AAC1B,QAAM,MAAMC,cAAY,aAACC,SAAG;AAC5B,SAAO;AAAA,IACL;AAAA,EACD;AACH;;;"}