/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*每个页面公共css */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/*!
 * Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2023 Fonticons, Inc.
 */
/* 实心图标字体 */
@font-face {
  font-family: 'FontAwesome';
  font-style: normal;
  font-weight: 900;
  /* 这里应该是转换后的base64字体文件，为简化演示使用CDN链接 */
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-solid-900.woff2') format('woff2');
}
/* 品牌图标字体 */
@font-face {
  font-family: 'FontAwesomeBrands';
  font-style: normal;
  font-weight: 400;
  /* 这里应该是转换后的base64字体文件，为简化演示使用CDN链接 */
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/webfonts/fa-brands-400.woff2') format('woff2');
}
.fa,
.fas {
  font-family: 'FontAwesome';
  font-weight: 900;
}
.fab {
  font-family: 'FontAwesomeBrands';
  font-weight: 400;
}
/* 客服图标 */
.fa-headset:before {
  content: "\f590";
}
/* 电话图标 */
.fa-phone:before {
  content: "\f095";
}
/* 微信/聊天图标 */
.fa-comment:before {
  content: "\f075";
}
/* 微信图标 - 品牌图标 */
.fa-weixin:before {
  content: "\f1d7";
}
/* 加号图标 */
.fa-plus:before {
  content: "\2b";
}
/* 关闭图标 */
.fa-xmark:before {
  content: "\f00d";
}
/* 调解记录 */
.fa-clipboard-list:before {
  content: "\f46d";
}
/* 隐私政策 */
/* .fa-shield-alt:before {
  content: "\f132";
} */
/* 右侧箭头 */
.fa-chevron-right:before {
  content: "\f054";
}
/* 对勾 */
.fa-check-circle:before, .fa-circle-check:before {
  content: "\f058";
}
/* 搜索、调解查询 */
.fa-magnifying-glass:before, .fa-search:before {
  content: "\f002";
}
/* 案例展示 */
.fa-file-alt:before, .fa-file-lines:before, .fa-file-text:before {
  content: "\f15c";
}
/* 投诉建议 */
.fa-comments:before {
  content: "\f086";
}
/* 意见建议 */
.fa-lightbulb:before {
  content: "\f0eb";
}
/* 服务投诉 */
.fa-exclamation-triangle:before, .fa-triangle-exclamation:before, .fa-warning:before {
  content: "\f071";
}
.fa-comments:before {
  content: "\f086";
}
/* 反馈类型 */
.fa-list-dots:before, .fa-list-ul:before {
  content: "\f0ca";
}
/* 关联案件信息 */
.fa-file-alt:before, .fa-file-lines:before, .fa-file-text:before {
  content: "\f15c";
}
/* 具体类别 */
.fa-tags:before {
  content: "\f02c";
}
/* 编辑 */
.fa-edit:before, .fa-pen-to-square:before {
  content: "\f044";
}
/* 提交反馈 */
.fa-paper-plane:before {
  content: "\f1d8";
}
.fa-cog:before, .fa-gear:before {
  content: "\f013";
}
.fa-mobile-alt:before, .fa-mobile-screen-button:before {
  content: "\f3cd";
}
.fa-heart:before {
  content: "\f004";
}
.fa-circle-plus:before, .fa-plus-circle:before {
  content: "\f055";
}
.fa-circle-info:before, .fa-info-circle:before {
  content: "\f05a";
}
.fa-shield-alt:before, .fa-shield-halved:before {
  content: "\f3ed";
}
.fa-user-times:before, .fa-user-xmark:before {
  content: "\f235";
}
.fa-clock-four:before, .fa-clock:before {
  content: "\f017";
}
.fa-ban:before, .fa-cancel:before {
  content: "\f05e";
}
.fa-bug:before {
  content: "\f188";
}
.fa-ellipsis-h:before, .fa-ellipsis:before {
  content: "\f141";
}
.fa-check-circle:before, .fa-circle-check:before {
  content: "\f058";
}
.fa-clock-four:before, .fa-clock:before {
  content: "\f017";
}
.fa-file-pdf:before {
  content: "\f1c1";
}
.fa-file-image:before {
  content: "\f1c5";
}
.fa-file-pdf:before {
  content: "\f1c1";
}
.fa-expand:before {
  content: "\f065";
}
.fa-pen-nib:before {
  content: "\f5ad";
}
.fa-file-signature:before {
  content: "\f573";
}
.fa-spinner:before {
  content: "\f110"
}
.fa-spin {
  animation-delay: var(--fa-animation-delay,0s);
  animation-direction: var(--fa-animation-direction,normal)
}
.fa-spin {
  animation-name: fa-spin;
  animation-duration: var(--fa-animation-duration,2s);
  animation-iteration-count: var(--fa-animation-iteration-count,infinite);
  animation-timing-function: var(--fa-animation-timing,linear)
}
@keyframes fa-spin {
0% {
      transform: rotate(0deg)
}
to {
      transform: rotate(1turn)
}
}
.fa-envelope:before {
  content: "\f0e0";
}
.fa-check:before {
  content: "\f00c";
}
.fa-file-contract:before {
  content: "\f56c";
}
.fa-certificate:before {
  content: "\f0a3";
}
.fa-gavel:before, .fa-legal:before {
  content: "\f0e3";
}
.fa-exclamation-triangle:before, .fa-triangle-exclamation:before, .fa-warning:before {
  content: "\f071";
}
.fa-copy:before {
  content: "\f0c5";
}
.fa-bank:before, .fa-building-columns:before, .fa-institution:before, .fa-museum:before, .fa-university:before {
  content: "\f19c";
}
.fa-weixin:before {
  content: "\f1d7";
}
.fa-alipay:before {
  content: "\f642";
}
.fa-gift:before {
  content: "\f06b";
}
.fa-file-signature:before {
  content: "\f573";
}
.fa-circle-question:before, .fa-question-circle:before {
  content: "\f059";
}
@font-face {
  font-family: "iconfont";
  /* 以下是修改后的引用方式，使用了小程序支持的TTF格式 */
  src: url("data:font/truetype;charset=utf-8;base64,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");
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-search:before {
  content: "\e900";
}
.icon-confirm:before {
  content: "\e901";
}
.icon-complaint:before {
  content: "\e902";
}
.icon-case:before {
  content: "\e903";
}
.icon-home:before {
  content: "\e904";
}
.icon-mine:before {
  content: "\e905";
}
.icon-user:before {
  content: "\e906";
}
.icon-feedback:before {
  content: "\e907";
}
/* 添加客服、电话和微信图标 */
.icon-kefu:before {
  content: "\e680";
}
.icon-phone:before {
  content: "\e725";
}
.icon-wechat:before {
  content: "\e620";
}
/* 纯CSS备用图标样式 */
.icon-fallback {
  position: relative;
  display: inline-block;
  width: 64rpx;
  height: 64rpx;
  background-color: #2979ff;
  color: white;
  text-align: center;
  line-height: 64rpx;
  border-radius: 50%;
  font-size: 36rpx;
}
.icon-confirm-fallback:before {
  content: "C";
}
/* 全局样式 */
page {
  font-size: 28rpx;
  color: #333;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Segoe UI, Arial, Roboto, "PingFang SC", "miui", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
}
/* 通用样式 */
.container {
  width: 100%;
  min-height: 100vh;
}
/* 卡片样式 */
.card {
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
/* 按钮样式 */
.btn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  color: #ffffff;
}
.btn-primary {
  background-color: #007aff;
}
.btn-success {
  background-color: #4cd964;
}
/* 文本溢出省略号 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/* flex布局工具类 */
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-column {
  display: flex;
  flex-direction: column;
}
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.space-between {
  justify-content: space-between;
}
.align-center {
  align-items: center;
}
/* 边距工具类 */
.mt-10 {
  margin-top: 10rpx;
}
.mb-10 {
  margin-bottom: 10rpx;
}
.ml-10 {
  margin-left: 10rpx;
}
.mr-10 {
  margin-right: 10rpx;
}
.pt-10 {
  padding-top: 10rpx;
}
.pb-10 {
  padding-bottom: 10rpx;
}
.pl-10 {
  padding-left: 10rpx;
}
.pr-10 {
  padding-right: 10rpx;
}page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}