{"pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "不良资产系统"}}, {"path": "pages/mine/mine", "style": {"navigationBarTitleText": "我的"}}, {"path": "pages/mediation_query/mediation_query", "style": {"navigationBarTitleText": "调解查询", "enablePullDownRefresh": true, "backgroundTextStyle": "dark"}}, {"path": "pages/mediation_complaint/mediation_complaint", "style": {"navigationBarTitleText": "调解投诉"}}, {"path": "pages/real_case/real_case", "style": {"navigationBarTitleText": "案例展示"}}, {"path": "pages/case_detail/case_detail", "style": {"navigationBarTitleText": "案例查询"}}, {"path": "pages/work_order_detail/work_order_detail", "style": {"navigationBarTitleText": "调解确认"}}, {"path": "pages/solution_confirm/solution_confirm", "style": {"navigationBarTitleText": "方案确认"}}, {"path": "pages/case_completed/case_completed", "style": {"navigationBarTitleText": "调解案件详情"}}, {"path": "pages/protocol_preview/protocol_preview", "style": {"navigationBarTitleText": "协议预览"}}, {"path": "pages/contact_information/contact_information", "style": {"navigationBarTitleText": "确认联系方式"}}, {"path": "pages/agreement_signing/agreement_signing", "style": {"navigationBarTitleText": "协议签署"}}, {"path": "pages/agreement_notarization/agreement_notarization", "style": {"navigationBarTitleText": "协议公证"}}, {"path": "pages/login/login", "style": {"navigationBarTitleText": "登录", "navigationStyle": "custom"}}, {"path": "pages/auth/auth", "style": {"navigationBarTitleText": "实名认证"}}, {"path": "pages/user-info/user-info", "style": {"navigationBarTitleText": "完善个人信息", "navigationStyle": "custom"}}, {"path": "pages/user_agreement/user_agreement", "style": {"navigationBarTitleText": "服务协议"}}, {"path": "pages/privacy_policy/privacy_policy", "style": {"navigationBarTitleText": "隐私政策"}}, {"path": "pages/webview/webview", "style": {"navigationBarTitleText": "Web-View嵌入H5页面"}}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "不良资产系统", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#999999", "selectedColor": "#007aff", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png"}, {"pagePath": "pages/mine/mine", "text": "我的", "iconPath": "static/tabbar/mine.png", "selectedIconPath": "static/tabbar/mine-active.png"}]}, "easycom": {"autoscan": true, "custom": {"^miliu-(.*)": "@/components/miliu-$1/miliu-$1.vue", "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"}}, "uniIdRouter": {}}