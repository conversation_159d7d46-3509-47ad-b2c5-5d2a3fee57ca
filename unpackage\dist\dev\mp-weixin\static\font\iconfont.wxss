@font-face {
  font-family: 'iconfont';
  /* 以下是修改后的引用方式，使用了小程序支持的TTF格式 */
  src: url('data:font/truetype;charset=utf-8;base64,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');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-search:before {
  content: "\e900";
}

.icon-confirm:before {
  content: "\e901";
}

.icon-complaint:before {
  content: "\e902";
}

.icon-case:before {
  content: "\e903";
}

.icon-home:before {
  content: "\e904";
}

.icon-mine:before {
  content: "\e905";
}

.icon-user:before {
  content: "\e906";
}

.icon-feedback:before {
  content: "\e907";
}

/* 添加客服、电话和微信图标 */
.icon-kefu:before {
  content: "\e680";
}

.icon-phone:before {
  content: "\e725";
}

.icon-wechat:before {
  content: "\e620";
} 
