{"version": 3, "file": "user-info.js", "sources": ["pages/user-info/user-info.vue", "C:/software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvdXNlci1pbmZvL3VzZXItaW5mby52dWU"], "sourcesContent": ["<template>\n  <view class=\"user-info-page\">\n    <!-- 顶部标题区域 -->\n    <view class=\"header-section\">\n      <view class=\"title\">完善个人信息</view>\n      <view class=\"subtitle\">设置您的头像和昵称，让其他用户更好地认识您</view>\n    </view>\n\n    <!-- 用户信息表单 -->\n    <view class=\"form-section\">\n      <user-info-form\n        :allow-skip=\"true\"\n        submit-button-text=\"完成设置\"\n        :initial-user-info=\"initialUserInfo\"\n        @submit=\"handleSubmit\"\n        @skip=\"handleSkip\"\n        @avatar-change=\"handleAvatarChange\"\n        @nickname-change=\"handleNicknameChange\"\n      />\n    </view>\n\n    <!-- 说明文字 -->\n    <view class=\"info-section\">\n      <view class=\"info-item\">\n        <text class=\"info-icon\">🔒</text>\n        <text class=\"info-text\">您的个人信息将被安全保护</text>\n      </view>\n      <view class=\"info-item\">\n        <text class=\"info-icon\">✨</text>\n        <text class=\"info-text\">完善信息后可享受更好的服务体验</text>\n      </view>\n    </view>\n\n    <!-- 加载遮罩 -->\n    <view class=\"loading-mask\" v-if=\"isProcessing\">\n      <view class=\"loading-content\">\n        <uni-load-more status=\"loading\" :content-text=\"loadingText\"></uni-load-more>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\nimport wechatAuth from '@/utils/wechat-auth.js';\nimport userStore from '@/utils/user-store.js';\n\n// 响应式数据\nconst isProcessing = ref(false);\nconst initialUserInfo = ref({});\n\n// 加载文案\nconst loadingText = ref({\n  contentText: {\n    contentdown: '正在保存...',\n    contentrefresh: '正在保存...',\n    contentnomore: '保存完成'\n  }\n});\n\n// 页面加载\nonMounted(() => {\n  loadUserInfo();\n});\n\n// 加载用户信息\nfunction loadUserInfo() {\n  try {\n    const storedInfo = wechatAuth.getStoredUserInfo();\n    if (storedInfo.wechatUserInfo) {\n      initialUserInfo.value = storedInfo.wechatUserInfo;\n    }\n  } catch (error) {\n    console.error('加载用户信息失败:', error);\n  }\n}\n\n// 处理头像变化\nfunction handleAvatarChange(avatarUrl) {\n  console.log('头像已更新:', avatarUrl);\n}\n\n// 处理昵称变化\nfunction handleNicknameChange(nickname) {\n  console.log('昵称已更新:', nickname);\n}\n\n// 处理提交\nasync function handleSubmit(result) {\n  if (!result.success) {\n    uni.showToast({\n      title: result.error || '设置失败',\n      icon: 'none',\n      duration: 2000\n    });\n    return;\n  }\n\n  try {\n    isProcessing.value = true;\n\n    // 更新用户状态管理\n    const completeUserData = wechatAuth.getCompleteUserData();\n    userStore.setWechatUserInfo(result.userInfo);\n\n    // 显示成功提示\n    uni.showToast({\n      title: '设置完成',\n      icon: 'success',\n      duration: 1500\n    });\n\n    // 延迟跳转到首页\n    setTimeout(() => {\n      uni.switchTab({\n        url: '/pages/index/index'\n      });\n    }, 1500);\n\n  } catch (error) {\n    console.error('保存用户信息失败:', error);\n    uni.showToast({\n      title: '保存失败，请重试',\n      icon: 'none',\n      duration: 2000\n    });\n  } finally {\n    isProcessing.value = false;\n  }\n}\n\n// 处理跳过\nfunction handleSkip() {\n  uni.showModal({\n    title: '确认跳过',\n    content: '跳过设置后，您可以稍后在个人中心完善信息',\n    confirmText: '确认跳过',\n    cancelText: '继续设置',\n    success: (res) => {\n      if (res.confirm) {\n        // 用户确认跳过\n        uni.switchTab({\n          url: '/pages/index/index'\n        });\n      }\n    }\n  });\n}\n\n// 页面标题\nuni.setNavigationBarTitle({\n  title: '完善个人信息'\n});\n</script>\n\n<style lang=\"scss\" scoped>\n.user-info-page {\n  min-height: 100vh;\n  background-color: #f8f9fa;\n  padding: 40rpx 0;\n}\n\n.header-section {\n  text-align: center;\n  padding: 60rpx 40rpx 40rpx;\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n}\n\n.title {\n  font-size: 48rpx;\n  font-weight: bold;\n  color: #333333;\n  margin-bottom: 20rpx;\n}\n\n.subtitle {\n  font-size: 28rpx;\n  color: #666666;\n  line-height: 1.5;\n}\n\n.form-section {\n  background-color: #ffffff;\n  margin-bottom: 20rpx;\n}\n\n.info-section {\n  padding: 40rpx;\n  background-color: #ffffff;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24rpx;\n}\n\n.info-item:last-child {\n  margin-bottom: 0;\n}\n\n.info-icon {\n  font-size: 32rpx;\n  margin-right: 20rpx;\n}\n\n.info-text {\n  font-size: 28rpx;\n  color: #666666;\n  flex: 1;\n}\n\n.loading-mask {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n}\n\n.loading-content {\n  background-color: #ffffff;\n  border-radius: 16rpx;\n  padding: 40rpx;\n}\n</style>\n", "import MiniProgramPage from 'D:/work/不良资产系统/non-performing-assets/pages/user-info/user-info.vue'\nwx.createPage(MiniProgramPage)"], "names": ["ref", "onMounted", "wechatAuth", "uni", "userStore"], "mappings": ";;;;;;;;;;;;;;;;;AAgDA,UAAM,eAAeA,cAAAA,IAAI,KAAK;AAC9B,UAAM,kBAAkBA,cAAAA,IAAI,CAAA,CAAE;AAG9B,UAAM,cAAcA,cAAAA,IAAI;AAAA,MACtB,aAAa;AAAA,QACX,aAAa;AAAA,QACb,gBAAgB;AAAA,QAChB,eAAe;AAAA,MAChB;AAAA,IACH,CAAC;AAGDC,kBAAAA,UAAU,MAAM;AACd;IACF,CAAC;AAGD,aAAS,eAAe;AACtB,UAAI;AACF,cAAM,aAAaC,4BAAW;AAC9B,YAAI,WAAW,gBAAgB;AAC7B,0BAAgB,QAAQ,WAAW;AAAA,QACpC;AAAA,MACF,SAAQ,OAAO;AACdC,sBAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAAA,MACjC;AAAA,IACH;AAGA,aAAS,mBAAmB,WAAW;AACrCA,8EAAY,UAAU,SAAS;AAAA,IACjC;AAGA,aAAS,qBAAqB,UAAU;AACtCA,8EAAY,UAAU,QAAQ;AAAA,IAChC;AAGA,mBAAe,aAAa,QAAQ;AAClC,UAAI,CAAC,OAAO,SAAS;AACnBA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO,OAAO,SAAS;AAAA,UACvB,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AACD;AAAA,MACD;AAED,UAAI;AACF,qBAAa,QAAQ;AAGrB,cAAM,mBAAmBD,4BAAW;AACpCE,wBAAAA,UAAU,kBAAkB,OAAO,QAAQ;AAG3CD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,UAAU;AAAA,YACZ,KAAK;AAAA,UACb,CAAO;AAAA,QACF,GAAE,IAAI;AAAA,MAER,SAAQ,OAAO;AACdA,sBAAA,MAAA,MAAA,SAAA,wCAAc,aAAa,KAAK;AAChCA,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAChB,CAAK;AAAA,MACL,UAAY;AACR,qBAAa,QAAQ;AAAA,MACtB;AAAA,IACH;AAGA,aAAS,aAAa;AACpBA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS,CAAC,QAAQ;AAChB,cAAI,IAAI,SAAS;AAEfA,0BAAAA,MAAI,UAAU;AAAA,cACZ,KAAK;AAAA,YACf,CAAS;AAAA,UACF;AAAA,QACF;AAAA,MACL,CAAG;AAAA,IACH;AAGAA,kBAAG,MAAC,sBAAsB;AAAA,MACxB,OAAO;AAAA,IACT,CAAC;;;;;;;;;;;;;;;;;;;;;;;ACvJD,GAAG,WAAW,eAAe;"}