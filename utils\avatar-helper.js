// 头像管理工具
import { isDebug } from '@/config/env.js';
import { AssetUrlGenerator, ASSETS_CONFIG } from '@/config/assets.js';
import { getBase64Avatar } from '@/utils/default-avatar-base64.js';

/**
 * 头像错误处理配置
 */
export const AVATAR_ERROR_CONFIG = {
  maxRetries: 3,           // 最大重试次数
  retryDelay: 1000,        // 重试延迟（毫秒）
  fallbackDelay: 500,      // 切换到备用头像的延迟
  compressionQuality: 0.8, // 压缩质量
  maxFileSize: 2 * 1024 * 1024, // 最大文件大小 2MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'] // 允许的文件类型
};

/**
 * 获取默认头像URL - 使用资源配置，支持base64回退
 * @param {string} type 头像类型 ('default', 'male', 'female', 'placeholder', 'loading')
 * @param {string} gender 用户性别 (0-男, 1-女, 2-保密)
 * @param {boolean} useBase64Fallback 是否使用base64作为最终回退
 * @returns {string} 头像URL
 */
export function getDefaultAvatar(type = 'default', gender = null, useBase64Fallback = true) {
  // 首先尝试从资源配置获取
  const avatarUrl = AssetUrlGenerator.getAvatarUrl(type, { gender, fallback: true });

  // 如果获取失败且允许base64回退，返回base64头像
  if (!avatarUrl && useBase64Fallback) {
    return getBase64Avatar(type, gender);
  }

  return avatarUrl || getBase64Avatar(type, gender);
}

/**
 * 验证头像文件
 * @param {string} filePath 文件路径
 * @returns {Promise<Object>} 验证结果
 */
export function validateAvatarFile(filePath) {
  return new Promise((resolve, reject) => {
    if (!filePath) {
      reject(new Error('头像文件路径不能为空'));
      return;
    }

    // 获取文件信息
    uni.getFileInfo({
      filePath: filePath,
      success: (res) => {
        const fileSize = res.size;
        
        // 检查文件大小
        if (fileSize > AVATAR_ERROR_CONFIG.maxFileSize) {
          reject(new Error(`头像文件过大，请选择小于${Math.round(AVATAR_ERROR_CONFIG.maxFileSize / 1024 / 1024)}MB的图片`));
          return;
        }
        
        // 检查文件类型（通过后缀名简单判断）
        const extension = filePath.toLowerCase().split('.').pop();
        const validExtensions = ['jpg', 'jpeg', 'png', 'webp'];
        
        if (!validExtensions.includes(extension)) {
          reject(new Error('不支持的图片格式，请选择JPG、PNG或WebP格式的图片'));
          return;
        }
        
        resolve({
          valid: true,
          fileSize,
          extension,
          filePath
        });
      },
      fail: (error) => {
        reject(new Error('获取头像文件信息失败：' + error.errMsg));
      }
    });
  });
}

/**
 * 压缩头像图片
 * @param {string} filePath 原始文件路径
 * @param {Object} options 压缩选项
 * @returns {Promise<string>} 压缩后的文件路径
 */
export function compressAvatar(filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const defaultOptions = {
      quality: AVATAR_ERROR_CONFIG.compressionQuality,
      width: 400,  // 压缩到400x400
      height: 400,
      ...options
    };

    // 使用uni.compressImage进行压缩
    uni.compressImage({
      src: filePath,
      quality: Math.round(defaultOptions.quality * 100),
      width: defaultOptions.width,
      height: defaultOptions.height,
      success: (res) => {
        if (isDebug()) {
          console.log('头像压缩成功:', {
            original: filePath,
            compressed: res.tempFilePath
          });
        }
        resolve(res.tempFilePath);
      },
      fail: (error) => {
        if (isDebug()) {
          console.warn('头像压缩失败，使用原图:', error);
        }
        // 压缩失败时返回原图
        resolve(filePath);
      }
    });
  });
}

/**
 * 处理头像加载错误 - 增强版本，支持多级回退
 * @param {string} avatarUrl 原始头像URL
 * @param {Object} options 选项
 * @returns {string} 备用头像URL
 */
export function handleAvatarError(avatarUrl, options = {}) {
  const { gender, useGenderDefault = true, useBase64Fallback = true } = options;

  if (isDebug()) {
    console.warn('头像加载失败，使用默认头像:', avatarUrl);
  }

  // 如果启用性别默认头像且有性别信息
  if (useGenderDefault && gender !== undefined) {
    return getDefaultAvatar('default', gender, useBase64Fallback);
  }

  // 返回通用默认头像，优先使用base64确保可用性
  return getDefaultAvatar('default', null, useBase64Fallback);
}

/**
 * 上传头像到服务器
 * @param {string} filePath 本地文件路径
 * @param {Object} options 上传选项
 * @returns {Promise<Object>} 上传结果
 */
export function uploadAvatar(filePath, options = {}) {
  return new Promise(async (resolve, reject) => {
    try {
      // 验证文件
      await validateAvatarFile(filePath);
      
      // 压缩图片
      const compressedPath = await compressAvatar(filePath, options.compression);
      
      const defaultOptions = {
        url: '/api/upload/avatar',
        name: 'avatar',
        timeout: 30000,
        ...options
      };

      // 上传文件
      uni.uploadFile({
        url: defaultOptions.url,
        filePath: compressedPath,
        name: defaultOptions.name,
        timeout: defaultOptions.timeout,
        header: {
          'Authorization': uni.getStorageSync('token') || '',
          ...defaultOptions.header
        },
        success: (res) => {
          try {
            const data = JSON.parse(res.data);
            if (data.success) {
              resolve({
                success: true,
                url: data.data.url,
                originalPath: filePath,
                compressedPath: compressedPath
              });
            } else {
              reject(new Error(data.message || '上传失败'));
            }
          } catch (parseError) {
            reject(new Error('服务器响应格式错误'));
          }
        },
        fail: (error) => {
          let errorMessage = '上传失败';
          if (error.errMsg) {
            if (error.errMsg.includes('timeout')) {
              errorMessage = '上传超时，请重试';
            } else if (error.errMsg.includes('fail')) {
              errorMessage = '网络连接失败，请检查网络';
            } else {
              errorMessage = '上传失败：' + error.errMsg;
            }
          }
          reject(new Error(errorMessage));
        }
      });
    } catch (error) {
      reject(error);
    }
  });
}

/**
 * 创建头像预览URL（用于显示）- 增强版本
 * @param {string} avatarUrl 头像URL
 * @param {Object} options 选项
 * @returns {string} 预览URL
 */
export function createAvatarPreviewUrl(avatarUrl, options = {}) {
  const { addTimestamp = false, gender } = options;

  if (!avatarUrl || !AssetUrlGenerator.isValidUrl(avatarUrl)) {
    return getDefaultAvatar('placeholder', gender);
  }

  // 如果是本地临时文件，直接返回
  if (avatarUrl.startsWith('wxfile://') || avatarUrl.startsWith('http://tmp/')) {
    return avatarUrl;
  }

  // 如果需要添加时间戳
  if (addTimestamp) {
    return AssetUrlGenerator.addTimestamp(avatarUrl);
  }

  return avatarUrl;
}

/**
 * 清理临时头像文件
 * @param {Array<string>} filePaths 文件路径数组
 */
export function cleanupTempAvatars(filePaths) {
  if (!Array.isArray(filePaths)) {
    filePaths = [filePaths];
  }
  
  filePaths.forEach(filePath => {
    if (filePath && (filePath.startsWith('wxfile://') || filePath.includes('tmp'))) {
      try {
        uni.removeSavedFile({
          filePath: filePath,
          success: () => {
            if (isDebug()) {
              console.log('临时头像文件已清理:', filePath);
            }
          },
          fail: (error) => {
            if (isDebug()) {
              console.warn('清理临时头像文件失败:', filePath, error);
            }
          }
        });
      } catch (error) {
        if (isDebug()) {
          console.warn('清理临时头像文件异常:', error);
        }
      }
    }
  });
}
