"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "protocol_preview",
  setup(__props) {
    const receivedFileUrl = common_vendor.ref("");
    const receivedFileType = common_vendor.ref("");
    const receivedFileName = common_vendor.ref("");
    const receivedCaseNumber = common_vendor.ref("");
    const fileUrl = common_vendor.ref("http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx");
    const pdfImagesBaseUrl = common_vendor.ref("http://192.168.1.101:10010/pdf_images/scheme/485/");
    const fileType = common_vendor.ref("pdf");
    const fileName = common_vendor.ref("调解协议.pdf");
    const solutionData = common_vendor.ref(null);
    const apiError = common_vendor.ref(null);
    const showImagePreview = common_vendor.ref(false);
    const pdfImages = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const loadedImages = common_vendor.ref(/* @__PURE__ */ new Set());
    const scrollTop = common_vendor.ref(0);
    const scrollProgress = common_vendor.ref(0);
    common_vendor.ref(0);
    common_vendor.ref(0);
    const svgImages = common_vendor.computed(() => {
      return pdfImages.value.map((image, index) => ({ ...image, originalIndex: index })).filter((image) => image.type === "svg");
    });
    const normalImages = common_vendor.computed(() => {
      return pdfImages.value.map((image, index) => ({ ...image, originalIndex: index })).filter((image) => !image.type || image.type !== "svg");
    });
    const initializeApiData = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:177", "开始初始化API数据...");
        apiError.value = null;
        common_vendor.index.showLoading({ title: "获取数据中..." });
        await fetchSolutionDetails();
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:188", "API数据初始化完成");
        common_vendor.index.showToast({
          title: "数据获取成功",
          icon: "success",
          duration: 1500
        });
        return true;
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:199", "API数据初始化失败:", error);
        common_vendor.index.showToast({
          title: "数据获取失败",
          icon: "none",
          duration: 2e3
        });
        return false;
      }
    };
    const handlePdfPreview = async () => {
      try {
        isLoading.value = true;
        showImagePreview.value = true;
        if (!solutionData.value) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:222", "首次获取API数据...");
          await fetchSolutionDetails();
        } else {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:225", "使用已缓存的API数据");
        }
        if (solutionData.value) {
          if (solutionData.value.pdfUrl) {
            common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:232", "发现直接PDF文件URL，尝试直接预览");
            fileUrl.value = solutionData.value.pdfUrl;
            await handleDirectPdfPreview();
            return;
          }
          if (solutionData.value.fileStream || solutionData.value.fileData) {
            common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:240", "发现文件流数据，处理文件流预览");
            await handleFileStreamPreview();
            return;
          }
          if (solutionData.value.pdfImages && solutionData.value.pdfImages.length > 0) {
            common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:247", "发现PDF图片列表，使用图片预览");
            pdfImages.value = solutionData.value.pdfImages;
            return;
          }
        }
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:254", "使用默认图片预览模式");
        const images = await getPdfImages();
        if (images.length > 0) {
          pdfImages.value = images;
        } else {
          throw new Error("无法获取预览内容");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:263", "PDF预览处理失败:", error);
        common_vendor.index.showToast({
          title: "预览加载失败",
          icon: "none",
          duration: 2e3
        });
        closeImagePreview();
      } finally {
        isLoading.value = false;
      }
    };
    const handleDirectPdfPreview = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:280", "开始直接PDF预览，文件URL:", fileUrl.value);
        common_vendor.index.downloadFile({
          url: fileUrl.value,
          success: (res) => {
            if (res.statusCode === 200) {
              common_vendor.index.openDocument({
                filePath: res.tempFilePath,
                showMenu: true,
                success: () => {
                  common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:291", "PDF文件直接预览成功");
                  closeImagePreview();
                },
                fail: (error) => {
                  common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:296", "PDF直接预览失败，转为图片预览模式:", error);
                  fallbackToImagePreview();
                }
              });
            } else {
              common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:302", "PDF下载失败，转为图片预览模式");
              fallbackToImagePreview();
            }
          },
          fail: (error) => {
            common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:307", "PDF下载失败，转为图片预览模式:", error);
            fallbackToImagePreview();
          }
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:312", "直接PDF预览失败:", error);
        fallbackToImagePreview();
      }
    };
    const handleFileStreamPreview = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:322", "开始处理文件流预览");
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:323", "API返回的数据结构:", solutionData.value);
        const fileData = solutionData.value.fileStream || solutionData.value.fileData || solutionData.value.data || solutionData.value.file || solutionData.value;
        if (!fileData) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:333", "未找到文件流数据，转为图片预览模式");
          fallbackToImagePreview();
          return;
        }
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:338", "文件流数据类型:", typeof fileData);
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:339", "文件流数据长度:", fileData.length || "unknown");
        const filePath = await convertStreamToTempFile(fileData);
        if (typeof filePath === "string" && (filePath.startsWith("http") || filePath.startsWith("/"))) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:346", "获取到文件URL，开始下载:", filePath);
          common_vendor.index.downloadFile({
            url: filePath,
            success: (res) => {
              if (res.statusCode === 200) {
                common_vendor.index.openDocument({
                  filePath: res.tempFilePath,
                  showMenu: true,
                  success: () => {
                    common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:356", "文件流URL预览成功");
                    closeImagePreview();
                  },
                  fail: (error) => {
                    common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:360", "文件流URL预览失败，转为图片预览模式:", error);
                    fallbackToImagePreview();
                  }
                });
              } else {
                common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:365", "文件下载失败，转为图片预览模式");
                fallbackToImagePreview();
              }
            },
            fail: (error) => {
              common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:370", "文件下载失败，转为图片预览模式:", error);
              fallbackToImagePreview();
            }
          });
        } else {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:376", "获取到临时文件路径，直接打开:", filePath);
          common_vendor.index.openDocument({
            filePath,
            showMenu: true,
            success: () => {
              common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:382", "文件流预览成功");
              closeImagePreview();
            },
            fail: (error) => {
              common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:386", "文件流预览失败，转为图片预览模式:", error);
              fallbackToImagePreview();
            }
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:393", "文件流预览失败:", error);
        fallbackToImagePreview();
      }
    };
    const fallbackToImagePreview = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:403", "回退到图片预览模式");
        const images = await getPdfImages();
        if (images.length > 0) {
          pdfImages.value = images;
        } else {
          throw new Error("无法获取预览图片");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:411", "图片预览回退失败:", error);
        common_vendor.index.showToast({
          title: "预览失败",
          icon: "none"
        });
        closeImagePreview();
      }
    };
    const convertStreamToTempFile = async (fileData) => {
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:425", "开始转换文件流，数据类型:", typeof fileData);
        if (typeof fileData === "string" && fileData.startsWith("data:")) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:429", "检测到base64文件流数据");
          const base64Data = fileData.split(",")[1];
          const tempFileName = `temp_pdf_${Date.now()}.pdf`;
          const tempFilePath = `${common_vendor.index.env.USER_DATA_PATH}/${tempFileName}`;
          return new Promise((resolve, reject) => {
            common_vendor.index.getFileSystemManager().writeFile({
              filePath: tempFilePath,
              data: base64Data,
              encoding: "base64",
              success: () => {
                common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:441", "base64文件写入成功:", tempFilePath);
                resolve(tempFilePath);
              },
              fail: (error) => {
                common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:445", "base64文件写入失败:", error);
                reject(new Error(`文件写入失败: ${error.errMsg || error.message}`));
              }
            });
          });
        }
        if (typeof fileData === "string" && /^[A-Za-z0-9+/]+=*$/.test(fileData)) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:454", "检测到纯base64字符串数据");
          const tempFileName = `temp_pdf_${Date.now()}.pdf`;
          const tempFilePath = `${common_vendor.index.env.USER_DATA_PATH}/${tempFileName}`;
          return new Promise((resolve, reject) => {
            common_vendor.index.getFileSystemManager().writeFile({
              filePath: tempFilePath,
              data: fileData,
              encoding: "base64",
              success: () => {
                common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:465", "纯base64文件写入成功:", tempFilePath);
                resolve(tempFilePath);
              },
              fail: (error) => {
                common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:469", "纯base64文件写入失败:", error);
                reject(new Error(`文件写入失败: ${error.errMsg || error.message}`));
              }
            });
          });
        }
        if (fileData instanceof ArrayBuffer) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:478", "检测到ArrayBuffer文件流数据");
          const tempFileName = `temp_pdf_${Date.now()}.pdf`;
          const tempFilePath = `${common_vendor.index.env.USER_DATA_PATH}/${tempFileName}`;
          return new Promise((resolve, reject) => {
            common_vendor.index.getFileSystemManager().writeFile({
              filePath: tempFilePath,
              data: fileData,
              success: () => {
                common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:488", "ArrayBuffer文件写入成功:", tempFilePath);
                resolve(tempFilePath);
              },
              fail: (error) => {
                common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:492", "ArrayBuffer文件写入失败:", error);
                reject(new Error(`文件写入失败: ${error.errMsg || error.message}`));
              }
            });
          });
        }
        if (typeof fileData === "string" && (fileData.startsWith("http") || fileData.startsWith("/"))) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:501", "检测到文件URL，直接返回:", fileData);
          return fileData;
        }
        if (typeof fileData === "object" && fileData !== null) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:507", "检测到对象类型的文件数据:", fileData);
          const possibleUrl = fileData.url || fileData.fileUrl || fileData.downloadUrl || fileData.path;
          if (possibleUrl && typeof possibleUrl === "string") {
            common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:512", "从对象中提取到文件URL:", possibleUrl);
            return possibleUrl;
          }
          const possibleBase64 = fileData.base64 || fileData.data || fileData.content;
          if (possibleBase64 && typeof possibleBase64 === "string") {
            common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:519", "从对象中提取到base64数据");
            return await convertStreamToTempFile(possibleBase64);
          }
        }
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:525", "不支持的文件流格式:", typeof fileData, fileData);
        throw new Error(`不支持的文件流格式: ${typeof fileData}`);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:529", "文件流转换失败:", error);
        throw error;
      }
    };
    const viewAsImages = async () => {
      try {
        isLoading.value = true;
        showImagePreview.value = true;
        const images = await getPdfImages();
        if (images.length > 0) {
          pdfImages.value = images;
        } else {
          common_vendor.index.showToast({
            title: "暂无可预览内容",
            icon: "none"
          });
          closeImagePreview();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:554", "加载图片预览失败:", error);
        common_vendor.index.showToast({
          title: "加载失败，请稍后重试",
          icon: "error"
        });
        closeImagePreview();
      } finally {
        isLoading.value = false;
      }
    };
    const fetchSolutionDetails = async () => {
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:570", "开始调用 api.solution.getDetails() 接口...");
        const result = await utils_api.api.solution.getDetails();
        if (result.state === "success" && result.data) {
          solutionData.value = result.data;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:577", "方案详情数据获取成功:", result.data);
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:580", "=== API数据结构分析 ===");
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:581", "数据类型:", typeof result.data);
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:582", "数据键值:", Object.keys(result.data));
          const fileFields = ["fileStream", "fileData", "data", "file", "pdfUrl", "url", "downloadUrl", "pdfInfo", "pdfImages"];
          fileFields.forEach((field) => {
            if (result.data[field] !== void 0) {
              common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:588", `发现字段 ${field}:`, typeof result.data[field], result.data[field]);
            }
          });
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:591", "=== 数据结构分析结束 ===");
          if (result.data.pdfInfo) {
            const pdfInfo = result.data.pdfInfo;
            if (pdfInfo.fileUrl) {
              fileUrl.value = pdfInfo.fileUrl;
            }
            if (pdfInfo.fileName) {
              fileName.value = pdfInfo.fileName;
            }
            if (pdfInfo.baseUrl) {
              pdfImagesBaseUrl.value = pdfInfo.baseUrl;
            }
          }
          return result.data;
        } else {
          const errorMsg = result.msg || "获取方案详情失败";
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:610", "API调用失败:", errorMsg);
          apiError.value = errorMsg;
          common_vendor.index.showToast({
            title: errorMsg,
            icon: "none",
            duration: 2e3
          });
          throw new Error(errorMsg);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:622", "调用 api.solution.getDetails() 失败:", error);
        apiError.value = error.message || "网络请求失败";
        common_vendor.index.showToast({
          title: "获取数据失败，请检查网络连接",
          icon: "none",
          duration: 2e3
        });
        throw error;
      }
    };
    const getPdfImages = async () => {
      var _a;
      try {
        if (solutionData.value && solutionData.value.pdfImages) {
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:643", "使用API返回的PDF图片列表");
          return solutionData.value.pdfImages;
        }
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:648", "使用默认PDF图片列表生成逻辑");
        await new Promise((resolve) => setTimeout(resolve, 1e3));
        const pageCount = ((_a = solutionData.value) == null ? void 0 : _a.pageCount) || 5;
        const images = [];
        for (let i = 1; i <= pageCount; i++) {
          images.push({
            url: `${pdfImagesBaseUrl.value}page_${i}.jpg`,
            page: i
          });
        }
        return images;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:666", "获取PDF图片失败:", error);
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:669", "使用模拟数据");
        await new Promise((resolve) => setTimeout(resolve, 500));
        return [
          { url: `${pdfImagesBaseUrl.value}page_1.jpg`, page: 1 },
          { url: `${pdfImagesBaseUrl.value}page_2.jpg`, page: 2 },
          { url: `${pdfImagesBaseUrl.value}page_3.jpg`, page: 3 },
          { url: `${pdfImagesBaseUrl.value}page_4.jpg`, page: 4 },
          { url: `${pdfImagesBaseUrl.value}page_5.jpg`, page: 5 }
        ];
      }
    };
    const onScroll = (e) => {
      const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = e.detail;
      scrollTop.value = currentScrollTop;
      if (scrollHeight > clientHeight) {
        scrollProgress.value = currentScrollTop / (scrollHeight - clientHeight) * 100;
      }
    };
    const onImageLoad = (index) => {
      loadedImages.value.add(index);
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:702", `图片 ${index + 1} 加载成功`);
    };
    const onImageError = (index) => {
      common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:709", `图片 ${index + 1} 加载失败`);
      common_vendor.index.showToast({
        title: `第${index + 1}页加载失败`,
        icon: "none",
        duration: 2e3
      });
    };
    const closeImagePreview = () => {
      showImagePreview.value = false;
      scrollTop.value = 0;
      scrollProgress.value = 0;
      loadedImages.value.clear();
    };
    const showApiDataDebug = () => {
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:731", "=== 显示API调试信息 ===");
      let debugInfo = "";
      if (solutionData.value) {
        debugInfo = `API数据已获取
`;
        debugInfo += `数据类型: ${typeof solutionData.value}
`;
        debugInfo += `数据键值: ${Object.keys(solutionData.value).join(", ")}

`;
        const fileFields = ["fileStream", "fileData", "data", "file", "pdfUrl", "url", "downloadUrl"];
        fileFields.forEach((field) => {
          if (solutionData.value[field] !== void 0) {
            const value = solutionData.value[field];
            debugInfo += `${field}: ${typeof value} (${(value == null ? void 0 : value.length) || "unknown"})
`;
          }
        });
      } else if (apiError.value) {
        debugInfo = `API调用失败
错误信息: ${apiError.value}`;
      } else {
        debugInfo = "API数据尚未获取";
      }
      common_vendor.index.showModal({
        title: "API数据调试信息",
        content: debugInfo,
        showCancel: true,
        cancelText: "关闭",
        confirmText: "重新获取",
        success: (res) => {
          if (res.confirm) {
            initializeApiData();
          }
        }
      });
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:767", "=== API调试信息显示完成 ===");
    };
    const showSvgPreview = () => {
      try {
        isLoading.value = true;
        showImagePreview.value = true;
        pdfImages.value = [{
          url: fileUrl.value,
          page: 1,
          type: "svg"
        }];
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:785", "SVG文件预览准备完成:", fileUrl.value);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:787", "SVG预览失败:", error);
        common_vendor.index.showToast({
          title: "SVG预览失败",
          icon: "error"
        });
      } finally {
        isLoading.value = false;
      }
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:894", "预览页面参数:", options);
      if (options.fileUrl) {
        try {
          receivedFileUrl.value = decodeURIComponent(options.fileUrl);
          fileUrl.value = receivedFileUrl.value;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:901", "接收到文件URL:", fileUrl.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:903", "fileUrl参数解码失败:", error);
          receivedFileUrl.value = options.fileUrl;
          fileUrl.value = options.fileUrl;
        }
      }
      if (options.fileType) {
        try {
          receivedFileType.value = decodeURIComponent(options.fileType);
          fileType.value = receivedFileType.value;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:914", "接收到文件类型:", fileType.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:916", "fileType参数解码失败:", error);
          receivedFileType.value = options.fileType;
          fileType.value = options.fileType;
        }
      }
      if (options.fileName) {
        try {
          receivedFileName.value = decodeURIComponent(options.fileName);
          fileName.value = receivedFileName.value;
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:927", "接收到文件名:", fileName.value);
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:929", "fileName参数解码失败:", error);
          receivedFileName.value = options.fileName;
          fileName.value = options.fileName;
        }
      }
      if (options.caseNumber) {
        try {
          receivedCaseNumber.value = decodeURIComponent(options.caseNumber);
          common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:939", "接收到案件号:", receivedCaseNumber.value);
          pdfImagesBaseUrl.value = `http://192.168.1.101:10010/pdf_images/scheme/${receivedCaseNumber.value}/`;
        } catch (error) {
          common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:943", "caseNumber参数解码失败:", error);
          receivedCaseNumber.value = options.caseNumber;
          pdfImagesBaseUrl.value = `http://192.168.1.101:10010/pdf_images/scheme/${options.caseNumber}/`;
        }
      }
    });
    common_vendor.onMounted(async () => {
      const title = fileName.value ? `预览 - ${fileName.value}` : "文件预览";
      common_vendor.index.setNavigationBarTitle({ title });
      try {
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:957", "页面加载完成，开始获取方案详情数据...");
        await fetchSolutionDetails();
        if (fileType.value === "svg") {
          showSvgPreview();
        } else if (fileType.value === "pdf" || fileType.value === "docx") {
          handlePdfPreview();
        } else {
          viewAsImages();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/protocol_preview/protocol_preview.vue:972", "页面初始化过程中发生错误:", error);
        common_vendor.index.__f__("log", "at pages/protocol_preview/protocol_preview.vue:974", "API调用失败，使用默认预览方式");
        if (fileType.value === "svg") {
          showSvgPreview();
        } else {
          viewAsImages();
        }
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: showImagePreview.value
      }, showImagePreview.value ? common_vendor.e({
        b: solutionData.value
      }, solutionData.value ? {} : apiError.value ? {
        d: common_vendor.t(apiError.value)
      } : {}, {
        c: apiError.value,
        e: common_vendor.t(Math.round(scrollProgress.value)),
        f: isLoading.value ? 1 : "",
        g: common_vendor.o(initializeApiData),
        h: isLoading.value,
        i: common_vendor.o(showApiDataDebug),
        j: isLoading.value
      }, isLoading.value ? common_vendor.e({
        k: common_vendor.t(solutionData.value ? "正在加载PDF预览..." : "正在获取方案数据..."),
        l: !solutionData.value && !apiError.value
      }, !solutionData.value && !apiError.value ? {} : {}) : apiError.value && !solutionData.value ? {
        n: common_vendor.t(apiError.value),
        o: common_vendor.o(initializeApiData)
      } : {
        p: common_vendor.f(svgImages.value, (image, index, i0) => {
          return {
            a: image.url,
            b: index === 0 ? 1 : "",
            c: common_vendor.o(($event) => onImageLoad(image.originalIndex), `svg-${index}`),
            d: common_vendor.o(($event) => onImageError(image.originalIndex), `svg-${index}`),
            e: `svg-${index}`
          };
        }),
        q: common_vendor.f(normalImages.value, (image, index, i0) => {
          return {
            a: `img-${index}`,
            b: image.url,
            c: index === 0 ? 1 : "",
            d: common_vendor.o(($event) => onImageLoad(image.originalIndex), `img-${index}`),
            e: common_vendor.o(($event) => onImageError(image.originalIndex), `img-${index}`)
          };
        })
      }, {
        m: apiError.value && !solutionData.value,
        r: scrollTop.value,
        s: common_vendor.o(onScroll),
        t: scrollProgress.value + "%"
      }) : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f6102ab"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/protocol_preview/protocol_preview.js.map
