{"version": 3, "file": "wechat-error-handler.js", "sources": ["utils/wechat-error-handler.js"], "sourcesContent": ["// 微信小程序专用错误处理器\nimport { isDebug } from '@/config/env.js';\nimport { ERROR_TYPES, showError } from '@/utils/error-handler.js';\n\n/**\n * 微信错误代码映射\n */\nexport const WECHAT_ERROR_CODES = {\n  // wx.login 错误码\n  '40029': '无效的code',\n  '45011': 'API调用太频繁',\n  '40013': '无效的AppID',\n  \n  // wx.getUserProfile 错误码\n  '40003': '无效的openid',\n  '41001': '缺少access_token参数',\n  '42001': 'access_token超时',\n  \n  // 网络相关\n  'request:fail': '网络请求失败',\n  'timeout': '请求超时',\n  \n  // 用户操作\n  'getUserProfile:fail cancel': '用户取消授权',\n  'getUserProfile:fail': '获取用户信息失败',\n  'chooseAvatar:fail cancel': '用户取消选择头像',\n  \n  // 系统错误\n  'system error': '系统错误',\n  'busy': '系统繁忙'\n};\n\n/**\n * 微信错误类型枚举\n */\nexport const WECHAT_ERROR_TYPES = {\n  LOGIN_FAILED: 'wechat_login_failed',           // 微信登录失败\n  CODE_INVALID: 'wechat_code_invalid',           // code无效\n  USER_CANCELLED: 'wechat_user_cancelled',       // 用户取消\n  PROFILE_FAILED: 'wechat_profile_failed',       // 获取用户信息失败\n  AVATAR_FAILED: 'wechat_avatar_failed',         // 头像选择失败\n  API_LIMIT: 'wechat_api_limit',                 // API调用限制\n  NETWORK_ERROR: 'wechat_network_error',         // 网络错误\n  SYSTEM_ERROR: 'wechat_system_error'            // 系统错误\n};\n\n/**\n * 分析微信错误\n * @param {Error|string|Object} error 错误对象\n * @returns {Object} 错误分析结果\n */\nexport function analyzeWechatError(error) {\n  let message = '';\n  let errCode = '';\n  \n  if (error instanceof Error) {\n    message = error.message || '';\n  } else if (typeof error === 'string') {\n    message = error;\n  } else if (error && error.errMsg) {\n    message = error.errMsg;\n    errCode = error.errCode || '';\n  } else {\n    message = String(error || '');\n  }\n  \n  const lowerMessage = message.toLowerCase();\n  \n  // 用户取消操作\n  if (lowerMessage.includes('cancel') || \n      lowerMessage.includes('取消') ||\n      message.includes('getUserProfile:fail cancel') ||\n      message.includes('chooseAvatar:fail cancel')) {\n    return {\n      type: WECHAT_ERROR_TYPES.USER_CANCELLED,\n      level: 'info',\n      message: '用户取消操作',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // 网络相关错误\n  if (lowerMessage.includes('network') ||\n      lowerMessage.includes('网络') ||\n      lowerMessage.includes('request:fail') ||\n      lowerMessage.includes('timeout') ||\n      lowerMessage.includes('超时')) {\n    return {\n      type: WECHAT_ERROR_TYPES.NETWORK_ERROR,\n      level: 'warning',\n      message: '网络连接失败，请检查网络后重试',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // API调用限制\n  if (errCode === '45011' || \n      lowerMessage.includes('too many requests') ||\n      lowerMessage.includes('api limit')) {\n    return {\n      type: WECHAT_ERROR_TYPES.API_LIMIT,\n      level: 'warning',\n      message: 'API调用过于频繁，请稍后重试',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // code相关错误\n  if (errCode === '40029' || \n      lowerMessage.includes('invalid code') ||\n      lowerMessage.includes('code无效')) {\n    return {\n      type: WECHAT_ERROR_TYPES.CODE_INVALID,\n      level: 'error',\n      message: '登录凭证无效，请重新登录',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // 获取用户信息失败\n  if (lowerMessage.includes('getuserprofile') ||\n      lowerMessage.includes('获取用户信息')) {\n    return {\n      type: WECHAT_ERROR_TYPES.PROFILE_FAILED,\n      level: 'warning',\n      message: '获取用户信息失败，可稍后在个人中心完善',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // 头像选择失败\n  if (lowerMessage.includes('chooseavatar') ||\n      lowerMessage.includes('头像')) {\n    return {\n      type: WECHAT_ERROR_TYPES.AVATAR_FAILED,\n      level: 'warning',\n      message: '头像选择失败，请重试',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // 系统错误\n  if (lowerMessage.includes('system') ||\n      lowerMessage.includes('系统') ||\n      errCode === 'busy' ||\n      lowerMessage.includes('busy')) {\n    return {\n      type: WECHAT_ERROR_TYPES.SYSTEM_ERROR,\n      level: 'error',\n      message: '系统繁忙，请稍后重试',\n      originalMessage: message,\n      errCode\n    };\n  }\n  \n  // 默认登录失败\n  return {\n    type: WECHAT_ERROR_TYPES.LOGIN_FAILED,\n    level: 'error',\n    message: '登录失败，请重试',\n    originalMessage: message,\n    errCode\n  };\n}\n\n/**\n * 显示微信错误提示\n * @param {Error|string|Object} error 错误对象\n * @param {Object} options 选项\n */\nexport function showWechatError(error, options = {}) {\n  const errorAnalysis = analyzeWechatError(error);\n  \n  // 在调试模式下记录详细信息\n  if (isDebug()) {\n    console.error('微信错误详情:', {\n      type: errorAnalysis.type,\n      level: errorAnalysis.level,\n      message: errorAnalysis.message,\n      original: errorAnalysis.originalMessage,\n      errCode: errorAnalysis.errCode\n    });\n  }\n  \n  // 用户取消操作通常不需要显示错误提示\n  if (errorAnalysis.type === WECHAT_ERROR_TYPES.USER_CANCELLED) {\n    return;\n  }\n  \n  const defaultOptions = {\n    duration: 2000,\n    icon: 'none',\n    ...options\n  };\n  \n  uni.showToast({\n    title: errorAnalysis.message,\n    icon: defaultOptions.icon,\n    duration: defaultOptions.duration\n  });\n}\n\n/**\n * 检查是否为可重试的微信错误\n * @param {Error|string|Object} error 错误对象\n * @returns {boolean} 是否可重试\n */\nexport function isRetryableWechatError(error) {\n  const errorAnalysis = analyzeWechatError(error);\n  \n  const retryableTypes = [\n    WECHAT_ERROR_TYPES.NETWORK_ERROR,\n    WECHAT_ERROR_TYPES.SYSTEM_ERROR,\n    WECHAT_ERROR_TYPES.API_LIMIT\n  ];\n  \n  return retryableTypes.includes(errorAnalysis.type);\n}\n\n/**\n * 获取微信错误的重试延迟时间\n * @param {Error|string|Object} error 错误对象\n * @param {number} attempt 重试次数\n * @returns {number} 延迟时间（毫秒）\n */\nexport function getWechatRetryDelay(error, attempt = 1) {\n  const errorAnalysis = analyzeWechatError(error);\n  \n  switch (errorAnalysis.type) {\n    case WECHAT_ERROR_TYPES.API_LIMIT:\n      // API限制错误需要更长的延迟\n      return Math.min(5000 * attempt, 30000);\n    \n    case WECHAT_ERROR_TYPES.NETWORK_ERROR:\n      // 网络错误使用递增延迟\n      return Math.min(1000 * Math.pow(1.5, attempt - 1), 10000);\n    \n    case WECHAT_ERROR_TYPES.SYSTEM_ERROR:\n      // 系统错误使用固定延迟\n      return 3000;\n    \n    default:\n      return 1000;\n  }\n}\n\n/**\n * 微信登录错误处理包装器\n * @param {Function} operation 登录操作\n * @param {Object} options 选项\n * @returns {Promise} 操作结果\n */\nexport function withWechatErrorHandling(operation, options = {}) {\n  const defaultOptions = {\n    maxRetries: 3,\n    showError: true,\n    onRetry: null,\n    ...options\n  };\n  \n  return new Promise(async (resolve, reject) => {\n    let lastError = null;\n    \n    for (let attempt = 1; attempt <= defaultOptions.maxRetries + 1; attempt++) {\n      try {\n        const result = await operation();\n        resolve(result);\n        return;\n      } catch (error) {\n        lastError = error;\n        \n        // 检查是否可重试\n        if (attempt <= defaultOptions.maxRetries && isRetryableWechatError(error)) {\n          const delay = getWechatRetryDelay(error, attempt);\n          \n          if (isDebug()) {\n            console.log(`微信操作失败，${delay}ms后进行第${attempt}次重试:`, error);\n          }\n          \n          // 调用重试回调\n          if (typeof defaultOptions.onRetry === 'function') {\n            defaultOptions.onRetry(attempt, error, delay);\n          }\n          \n          // 等待后重试\n          await new Promise(resolve => setTimeout(resolve, delay));\n        } else {\n          // 不可重试或达到最大重试次数\n          break;\n        }\n      }\n    }\n    \n    // 显示错误提示\n    if (defaultOptions.showError) {\n      showWechatError(lastError);\n    }\n    \n    reject(lastError);\n  });\n}\n"], "names": ["isDebug", "uni"], "mappings": ";;;AAmCO,MAAM,qBAAqB;AAAA,EAChC,cAAc;AAAA;AAAA,EACd,cAAc;AAAA;AAAA,EACd,gBAAgB;AAAA;AAAA,EAChB,gBAAgB;AAAA;AAAA,EAChB,eAAe;AAAA;AAAA,EACf,WAAW;AAAA;AAAA,EACX,eAAe;AAAA;AAAA,EACf,cAAc;AAAA;AAChB;AAOO,SAAS,mBAAmB,OAAO;AACxC,MAAI,UAAU;AACd,MAAI,UAAU;AAEd,MAAI,iBAAiB,OAAO;AAC1B,cAAU,MAAM,WAAW;AAAA,EAC/B,WAAa,OAAO,UAAU,UAAU;AACpC,cAAU;AAAA,EACd,WAAa,SAAS,MAAM,QAAQ;AAChC,cAAU,MAAM;AAChB,cAAU,MAAM,WAAW;AAAA,EAC/B,OAAS;AACL,cAAU,OAAO,SAAS,EAAE;AAAA,EAC7B;AAED,QAAM,eAAe,QAAQ;AAG7B,MAAI,aAAa,SAAS,QAAQ,KAC9B,aAAa,SAAS,IAAI,KAC1B,QAAQ,SAAS,4BAA4B,KAC7C,QAAQ,SAAS,0BAA0B,GAAG;AAChD,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,IAAI,KAC1B,aAAa,SAAS,cAAc,KACpC,aAAa,SAAS,SAAS,KAC/B,aAAa,SAAS,IAAI,GAAG;AAC/B,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,YAAY,WACZ,aAAa,SAAS,mBAAmB,KACzC,aAAa,SAAS,WAAW,GAAG;AACtC,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,YAAY,WACZ,aAAa,SAAS,cAAc,KACpC,aAAa,SAAS,QAAQ,GAAG;AACnC,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,gBAAgB,KACtC,aAAa,SAAS,QAAQ,GAAG;AACnC,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,cAAc,KACpC,aAAa,SAAS,IAAI,GAAG;AAC/B,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,MAAI,aAAa,SAAS,QAAQ,KAC9B,aAAa,SAAS,IAAI,KAC1B,YAAY,UACZ,aAAa,SAAS,MAAM,GAAG;AACjC,WAAO;AAAA,MACL,MAAM,mBAAmB;AAAA,MACzB,OAAO;AAAA,MACP,SAAS;AAAA,MACT,iBAAiB;AAAA,MACjB;AAAA,IACN;AAAA,EACG;AAGD,SAAO;AAAA,IACL,MAAM,mBAAmB;AAAA,IACzB,OAAO;AAAA,IACP,SAAS;AAAA,IACT,iBAAiB;AAAA,IACjB;AAAA,EACJ;AACA;AAOO,SAAS,gBAAgB,OAAO,UAAU,IAAI;AACnD,QAAM,gBAAgB,mBAAmB,KAAK;AAG9C,MAAIA,WAAO,QAAA,GAAI;AACbC,kBAAAA,MAAA,MAAA,SAAA,wCAAc,WAAW;AAAA,MACvB,MAAM,cAAc;AAAA,MACpB,OAAO,cAAc;AAAA,MACrB,SAAS,cAAc;AAAA,MACvB,UAAU,cAAc;AAAA,MACxB,SAAS,cAAc;AAAA,IAC7B,CAAK;AAAA,EACF;AAGD,MAAI,cAAc,SAAS,mBAAmB,gBAAgB;AAC5D;AAAA,EACD;AAED,QAAM,iBAAiB;AAAA,IACrB,UAAU;AAAA,IACV,MAAM;AAAA,IACN,GAAG;AAAA,EACP;AAEEA,gBAAAA,MAAI,UAAU;AAAA,IACZ,OAAO,cAAc;AAAA,IACrB,MAAM,eAAe;AAAA,IACrB,UAAU,eAAe;AAAA,EAC7B,CAAG;AACH;;"}