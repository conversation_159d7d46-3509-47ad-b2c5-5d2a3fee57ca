{"version": 3, "file": "assets.js", "sources": ["config/assets.js"], "sourcesContent": ["// 静态资源配置\nexport const ASSETS_CONFIG = {\n  // 头像相关\n  avatars: {\n    // 默认头像\n    default: '/static/default-avatar.svg',\n    defaultPng: '/static/default-avatar.png',\n    \n    // 性别头像\n    male: '/static/avatar-male.svg',\n    female: '/static/avatar-female.svg',\n    \n    // 占位符头像（base64编码的SVG）\n    placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNGM0Y0RjYiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjMwIiBmaWxsPSIjRDFENURCIi8+PC9zdmc+',\n    \n    // 加载中头像\n    loading: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxMDAiIGZpbGw9IiNFNUU3RUIiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjIwIiBmaWxsPSIjOUNBM0FGIiBvcGFjaXR5PSIwLjUiLz48L3N2Zz4='\n  },\n  \n  // 图标相关\n  icons: {\n    // 登录页面图标\n    wechat: '/static/icons/wechat.png',\n    phone: '/static/icons/phone.png',\n    \n    // 功能图标\n    camera: '/static/icons/camera.png',\n    edit: '/static/icons/edit.png',\n    success: '/static/icons/success.png',\n    error: '/static/icons/error.png',\n    warning: '/static/icons/warning.png',\n    info: '/static/icons/info.png'\n  },\n  \n  // Logo相关\n  logos: {\n    app: '/static/logo.png',\n    appLarge: '/static/logo-large.png',\n    splash: '/static/splash.png'\n  },\n  \n  // 背景图片\n  backgrounds: {\n    login: '/static/bg/login-bg.jpg',\n    default: '/static/bg/default-bg.jpg'\n  }\n};\n\n// 资源URL生成器\nexport class AssetUrlGenerator {\n  /**\n   * 获取头像URL\n   * @param {string} type 头像类型\n   * @param {Object} options 选项\n   * @returns {string} 头像URL\n   */\n  static getAvatarUrl(type = 'default', options = {}) {\n    const { gender, fallback = true } = options;\n    \n    // 根据性别返回对应头像\n    if (type === 'default' && gender !== undefined) {\n      switch (parseInt(gender)) {\n        case 0: // 男性\n          return ASSETS_CONFIG.avatars.male;\n        case 1: // 女性\n          return ASSETS_CONFIG.avatars.female;\n        default: // 保密或其他\n          return ASSETS_CONFIG.avatars.default;\n      }\n    }\n    \n    // 获取指定类型的头像\n    const avatarUrl = ASSETS_CONFIG.avatars[type];\n    if (avatarUrl) {\n      return avatarUrl;\n    }\n    \n    // 如果找不到且允许回退，返回默认头像\n    if (fallback) {\n      return ASSETS_CONFIG.avatars.default;\n    }\n    \n    return null;\n  }\n  \n  /**\n   * 获取图标URL\n   * @param {string} name 图标名称\n   * @returns {string} 图标URL\n   */\n  static getIconUrl(name) {\n    return ASSETS_CONFIG.icons[name] || null;\n  }\n  \n  /**\n   * 获取Logo URL\n   * @param {string} type Logo类型\n   * @returns {string} Logo URL\n   */\n  static getLogoUrl(type = 'app') {\n    return ASSETS_CONFIG.logos[type] || ASSETS_CONFIG.logos.app;\n  }\n  \n  /**\n   * 获取背景图片URL\n   * @param {string} type 背景类型\n   * @returns {string} 背景图片URL\n   */\n  static getBackgroundUrl(type = 'default') {\n    return ASSETS_CONFIG.backgrounds[type] || ASSETS_CONFIG.backgrounds.default;\n  }\n  \n  /**\n   * 检查资源是否存在（简单的URL格式检查）\n   * @param {string} url 资源URL\n   * @returns {boolean} 是否为有效URL\n   */\n  static isValidUrl(url) {\n    if (!url || typeof url !== 'string') {\n      return false;\n    }\n    \n    // 检查是否为有效的URL格式\n    return url.startsWith('/') || \n           url.startsWith('http://') || \n           url.startsWith('https://') || \n           url.startsWith('data:') ||\n           url.startsWith('wxfile://');\n  }\n  \n  /**\n   * 添加时间戳防止缓存\n   * @param {string} url 原始URL\n   * @returns {string} 带时间戳的URL\n   */\n  static addTimestamp(url) {\n    if (!this.isValidUrl(url)) {\n      return url;\n    }\n    \n    // 对于data URL和本地文件，不添加时间戳\n    if (url.startsWith('data:') || url.startsWith('wxfile://')) {\n      return url;\n    }\n    \n    const separator = url.includes('?') ? '&' : '?';\n    return `${url}${separator}t=${Date.now()}`;\n  }\n}\n\n// 资源预加载器\nexport class AssetPreloader {\n  constructor() {\n    this.loadedAssets = new Set();\n    this.failedAssets = new Set();\n  }\n  \n  /**\n   * 预加载图片资源\n   * @param {Array<string>} urls 图片URL数组\n   * @returns {Promise<Object>} 加载结果\n   */\n  async preloadImages(urls) {\n    if (!Array.isArray(urls)) {\n      urls = [urls];\n    }\n    \n    const results = {\n      success: [],\n      failed: [],\n      total: urls.length\n    };\n    \n    const loadPromises = urls.map(url => {\n      return new Promise((resolve) => {\n        if (this.loadedAssets.has(url)) {\n          results.success.push(url);\n          resolve();\n          return;\n        }\n        \n        if (this.failedAssets.has(url)) {\n          results.failed.push(url);\n          resolve();\n          return;\n        }\n        \n        // 使用uni.getImageInfo预加载图片\n        uni.getImageInfo({\n          src: url,\n          success: () => {\n            this.loadedAssets.add(url);\n            results.success.push(url);\n            resolve();\n          },\n          fail: () => {\n            this.failedAssets.add(url);\n            results.failed.push(url);\n            resolve();\n          }\n        });\n      });\n    });\n    \n    await Promise.all(loadPromises);\n    \n    return {\n      ...results,\n      successRate: results.success.length / results.total\n    };\n  }\n  \n  /**\n   * 预加载关键资源\n   * @returns {Promise<Object>} 加载结果\n   */\n  async preloadCriticalAssets() {\n    const criticalAssets = [\n      ASSETS_CONFIG.avatars.default,\n      ASSETS_CONFIG.avatars.defaultPng,\n      ASSETS_CONFIG.logos.app,\n      ASSETS_CONFIG.icons.wechat\n    ].filter(url => url && !url.startsWith('data:'));\n    \n    return await this.preloadImages(criticalAssets);\n  }\n  \n  /**\n   * 清除预加载缓存\n   */\n  clearCache() {\n    this.loadedAssets.clear();\n    this.failedAssets.clear();\n  }\n}\n\n// 创建全局实例\nexport const assetPreloader = new AssetPreloader();\n\n// 便捷方法\nexport const getAvatarUrl = AssetUrlGenerator.getAvatarUrl;\nexport const getIconUrl = AssetUrlGenerator.getIconUrl;\nexport const getLogoUrl = AssetUrlGenerator.getLogoUrl;\nexport const getBackgroundUrl = AssetUrlGenerator.getBackgroundUrl;\n"], "names": ["uni"], "mappings": ";;AACO,MAAM,gBAAgB;AAAA;AAAA,EAE3B,SAAS;AAAA;AAAA,IAEP,SAAS;AAAA,IACT,YAAY;AAAA;AAAA,IAGZ,MAAM;AAAA,IACN,QAAQ;AAAA;AAAA,IAGR,aAAa;AAAA;AAAA,IAGb,SAAS;AAAA,EACV;AAAA;AAAA,EAGD,OAAO;AAAA;AAAA,IAEL,QAAQ;AAAA,IACR,OAAO;AAAA;AAAA,IAGP,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,SAAS;AAAA,IACT,MAAM;AAAA,EACP;AAAA;AAAA,EAGD,OAAO;AAAA,IACL,KAAK;AAAA,IACL,UAAU;AAAA,IACV,QAAQ;AAAA,EACT;AAAA;AAAA,EAGD,aAAa;AAAA,IACX,OAAO;AAAA,IACP,SAAS;AAAA,EACV;AACH;AAGO,MAAM,kBAAkB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,OAAO,aAAa,OAAO,WAAW,UAAU,CAAA,GAAI;AAClD,UAAM,EAAE,QAAQ,WAAW,KAAI,IAAK;AAGpC,QAAI,SAAS,aAAa,WAAW,QAAW;AAC9C,cAAQ,SAAS,MAAM,GAAC;AAAA,QACtB,KAAK;AACH,iBAAO,cAAc,QAAQ;AAAA,QAC/B,KAAK;AACH,iBAAO,cAAc,QAAQ;AAAA,QAC/B;AACE,iBAAO,cAAc,QAAQ;AAAA,MAChC;AAAA,IACF;AAGD,UAAM,YAAY,cAAc,QAAQ,IAAI;AAC5C,QAAI,WAAW;AACb,aAAO;AAAA,IACR;AAGD,QAAI,UAAU;AACZ,aAAO,cAAc,QAAQ;AAAA,IAC9B;AAED,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,WAAW,MAAM;AACtB,WAAO,cAAc,MAAM,IAAI,KAAK;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,WAAW,OAAO,OAAO;AAC9B,WAAO,cAAc,MAAM,IAAI,KAAK,cAAc,MAAM;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,iBAAiB,OAAO,WAAW;AACxC,WAAO,cAAc,YAAY,IAAI,KAAK,cAAc,YAAY;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,WAAW,KAAK;AACrB,QAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACnC,aAAO;AAAA,IACR;AAGD,WAAO,IAAI,WAAW,GAAG,KAClB,IAAI,WAAW,SAAS,KACxB,IAAI,WAAW,UAAU,KACzB,IAAI,WAAW,OAAO,KACtB,IAAI,WAAW,WAAW;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,OAAO,aAAa,KAAK;AACvB,QAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,aAAO;AAAA,IACR;AAGD,QAAI,IAAI,WAAW,OAAO,KAAK,IAAI,WAAW,WAAW,GAAG;AAC1D,aAAO;AAAA,IACR;AAED,UAAM,YAAY,IAAI,SAAS,GAAG,IAAI,MAAM;AAC5C,WAAO,GAAG,GAAG,GAAG,SAAS,KAAK,KAAK,IAAK,CAAA;AAAA,EACzC;AACH;AAGO,MAAM,eAAe;AAAA,EAC1B,cAAc;AACZ,SAAK,eAAe,oBAAI;AACxB,SAAK,eAAe,oBAAI;EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOD,MAAM,cAAc,MAAM;AACxB,QAAI,CAAC,MAAM,QAAQ,IAAI,GAAG;AACxB,aAAO,CAAC,IAAI;AAAA,IACb;AAED,UAAM,UAAU;AAAA,MACd,SAAS,CAAE;AAAA,MACX,QAAQ,CAAE;AAAA,MACV,OAAO,KAAK;AAAA,IAClB;AAEI,UAAM,eAAe,KAAK,IAAI,SAAO;AACnC,aAAO,IAAI,QAAQ,CAAC,YAAY;AAC9B,YAAI,KAAK,aAAa,IAAI,GAAG,GAAG;AAC9B,kBAAQ,QAAQ,KAAK,GAAG;AACxB;AACA;AAAA,QACD;AAED,YAAI,KAAK,aAAa,IAAI,GAAG,GAAG;AAC9B,kBAAQ,OAAO,KAAK,GAAG;AACvB;AACA;AAAA,QACD;AAGDA,sBAAAA,MAAI,aAAa;AAAA,UACf,KAAK;AAAA,UACL,SAAS,MAAM;AACb,iBAAK,aAAa,IAAI,GAAG;AACzB,oBAAQ,QAAQ,KAAK,GAAG;AACxB;UACD;AAAA,UACD,MAAM,MAAM;AACV,iBAAK,aAAa,IAAI,GAAG;AACzB,oBAAQ,OAAO,KAAK,GAAG;AACvB;UACD;AAAA,QACX,CAAS;AAAA,MACT,CAAO;AAAA,IACP,CAAK;AAED,UAAM,QAAQ,IAAI,YAAY;AAE9B,WAAO;AAAA,MACL,GAAG;AAAA,MACH,aAAa,QAAQ,QAAQ,SAAS,QAAQ;AAAA,IACpD;AAAA,EACG;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,wBAAwB;AAC5B,UAAM,iBAAiB;AAAA,MACrB,cAAc,QAAQ;AAAA,MACtB,cAAc,QAAQ;AAAA,MACtB,cAAc,MAAM;AAAA,MACpB,cAAc,MAAM;AAAA,IAC1B,EAAM,OAAO,SAAO,OAAO,CAAC,IAAI,WAAW,OAAO,CAAC;AAE/C,WAAO,MAAM,KAAK,cAAc,cAAc;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKD,aAAa;AACX,SAAK,aAAa;AAClB,SAAK,aAAa;EACnB;AACH;AAGY,MAAC,iBAAiB,IAAI,eAAiB;AAGvB,kBAAkB;AACpB,kBAAkB;AAClB,kBAAkB;AACZ,kBAAkB;;;"}