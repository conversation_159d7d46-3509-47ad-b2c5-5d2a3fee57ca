"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "case_detail",
  setup(__props) {
    const caseDetail = common_vendor.ref({});
    common_vendor.onLoad((options) => {
      const caseId = options.id;
      if (caseId) {
        loadCaseDetail(caseId);
      }
    });
    common_vendor.onMounted(() => {
      common_vendor.index.__f__("log", "at pages/case_detail/case_detail.vue:74", "案例详情页面已加载");
    });
    const loadCaseDetail = (caseId) => {
      const mockData = getCaseById(caseId);
      caseDetail.value = mockData;
    };
    const getCaseById = (id) => {
      const cases = [
        {
          id: "1",
          title: "信用卡欠款减免案例",
          type: "信用卡欠款",
          debtAmount: 8e4,
          resolvedAmount: 28e3,
          reductionRate: "减免65%",
          date: "2023-10-15",
          tags: ["信用卡", "减免成功"],
          background: "张先生因疫情影响，无法按时偿还卡款，通过调解平台申请调解，最终与银行达成和解",
          process: "1. 提交调解申请；2. 银行方面同意调解；3. 双方协商还款方案；4. 达成一致意见",
          result: "最终协商确定还款金额为28,000元，分36期还款，张先生表示满意"
        },
        {
          id: "2",
          title: "车贷逾期调解案例",
          type: "车贷逾期",
          debtAmount: 12e4,
          resolvedAmount: 12e4,
          reductionRate: "减免全部罚息",
          date: "2023-09-28",
          tags: ["车贷", "延期还款"],
          background: "李女士因生意周转困难，车贷逾期3个月，通过调解平台寻求帮助",
          process: "调解员协调双方，了解李女士实际困难，与金融机构协商延期还款方案",
          result: "金融机构同意免除全部罚息，延期6个月还款，李女士非常感谢"
        }
      ];
      return cases.find((c) => c.id === id) || {};
    };
    const formatAmount = (amount) => {
      if (!amount)
        return "0.00";
      return amount.toLocaleString("zh-CN", { minimumFractionDigits: 2 });
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(caseDetail.value.title),
        b: common_vendor.f(caseDetail.value.tags, (tag, k0, i0) => {
          return {
            a: common_vendor.t(tag),
            b: tag
          };
        }),
        c: common_vendor.t(caseDetail.value.type),
        d: common_vendor.t(formatAmount(caseDetail.value.debtAmount)),
        e: common_vendor.t(formatAmount(caseDetail.value.resolvedAmount)),
        f: common_vendor.t(caseDetail.value.reductionRate),
        g: common_vendor.t(caseDetail.value.date),
        h: common_vendor.t(caseDetail.value.background),
        i: common_vendor.t(caseDetail.value.process),
        j: common_vendor.t(caseDetail.value.result)
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d60749c0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/case_detail/case_detail.js.map
