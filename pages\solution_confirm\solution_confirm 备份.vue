<template>
	<view class="solution-confirm-container">
		<!-- 工单基本信息 -->
		<view class="work-order-card">
			<view class="work-order-header">
				<view class="work-order-title">
					<text>工单号: </text>
					<text class="work-order-id">{{workOrderData.id || 'MED20230001'}}</text>
				</view>
				<view class="work-order-status">
					<text class="status-label" :class="{'status-processing': workOrderData.status === '进行中'}">{{workOrderData.status || '进行中'}}</text>
				</view>
			</view>
			<view class="work-order-date">发起日期: {{workOrderData.createDate || '2023-11-01'}}</view>
		</view>
		
		<!-- 进度条 -->
		<view class="progress-bar">
			<view class="progress-steps">
				<view class="progress-step completed">
					<view class="step-circle">1</view>
					<view class="step-line completed"></view>
					<view class="step-label">工单确认</view>
				</view>
				<view class="progress-step active">
					<view class="step-circle">2</view>
					<view class="step-line"></view>
					<view class="step-label">方案确认</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">3</view>
					<view class="step-line"></view>
					<view class="step-label">还款录入</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">4</view>
					<view class="step-line"></view>
					<view class="step-label">签署协议</view>
				</view>
				<view class="progress-step">
					<view class="step-circle">5</view>
					<view class="step-label">完成</view>
				</view>
			</view>
		</view>
		
		<!-- 方案列表 -->
		<view class="solutions-container">
			<!-- 方案一 -->
			<view 
				class="solution-card" 
				:class="{'selected': selectedSolutionIndex === 0}"
				@click="selectSolution(0)"
			>
				<view class="solution-header">
					<view class="solution-title-wrap">
						<text class="solution-title">方案一</text>
						<text v-if="solutions[0].isRecommended" class="solution-tag">（推荐）</text>
					</view>
					<view class="solution-select">
						<view class="radio-button" :class="{'selected': selectedSolutionIndex === 0}">
							<view class="radio-inner" v-if="selectedSolutionIndex === 0"></view>
						</view>
					</view>
				</view>
				
				<view class="solution-content">
					<text>{{solutions[0].content}}</text>
				</view>
				
				<view class="solution-details">
					<view class="solution-item">
						<text class="solution-label">还款总额</text>
						<text class="solution-value">¥{{solutions[0].totalAmount.toFixed(2)}}</text>
					</view>
					<view class="solution-item">
						<text class="solution-label">月还款额</text>
						<text class="solution-value">¥{{solutions[0].monthlyPayment.toFixed(2)}}</text>
					</view>
				</view>
				
				<view class="solution-footer">
					<text class="discount-amount">减免金额: ¥{{solutions[0].discountAmount.toFixed(2)}}</text>
					<text class="view-detail" @click.stop="viewSolutionDetail(solutions[0])">查看详情</text>
				</view>
			</view>
			
			<!-- 方案二 -->
			<view 
				class="solution-card" 
				:class="{'selected': selectedSolutionIndex === 1}"
				@click="selectSolution(1)"
			>
				<view class="solution-header">
					<view class="solution-title-wrap">
						<text class="solution-title">方案二</text>
						<text v-if="solutions[1].isRecommended" class="solution-tag">（推荐）</text>
					</view>
					<view class="solution-select">
						<view class="radio-button" :class="{'selected': selectedSolutionIndex === 1}">
							<view class="radio-inner" v-if="selectedSolutionIndex === 1"></view>
						</view>
					</view>
				</view>
				
				<view class="solution-content">
					<text>{{solutions[1].content}}</text>
				</view>
				
				<view class="solution-details">
					<view class="solution-item">
						<text class="solution-label">还款总额</text>
						<text class="solution-value">¥{{solutions[1].totalAmount.toFixed(2)}}</text>
					</view>
					<view class="solution-item">
						<text class="solution-label">月还款额</text>
						<text class="solution-value">¥{{solutions[1].monthlyPayment.toFixed(2)}}</text>
					</view>
				</view>
				
				<view class="solution-footer">
					<text class="discount-amount">减免金额: ¥{{solutions[1].discountAmount.toFixed(2)}}</text>
					<text class="view-detail" @click.stop="viewSolutionDetail(solutions[1])">查看详情</text>
				</view>
			</view>
		</view>
		
		<!-- 联系客服 -->
		<view class="contact-section">
			<view class="contact-button phone" @click="handleContact('phone')">
				<text class="fa fa-phone"></text>
				<text>电话咨询</text>
			</view>
			<view class="contact-button wechat" @click="handleContact('wechat')">
				<text class="fab fa-weixin"></text>
				<text>微信咨询</text>
			</view>
		</view>
		
		<!-- 底部操作按钮 -->
		<view class="action-buttons">
			<button class="confirm-button" @click="handleConfirm">确认选择{{solutions[selectedSolutionIndex].title}}</button>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { api } from '@/utils/api.js';

/**
 * 调解方案确认页面
 * 
 * 此页面用于展示和选择多个调解方案，支持方案对比和确认功能
 * 用户可以查看方案详情，选择方案并确认选择
 */

// 接收页面参数 - 工单ID
const orderId = ref('');

// 工单基本数据
const workOrderData = ref({
	id: 'MED20230001',
	status: '进行中', // 修改为与图片一致的状态
	createDate: '2023-11-01'
});

// 选中的方案索引（默认选中方案一）
const selectedSolutionIndex = ref(0);

// 调解方案数组 - 包含多个方案
const solutions = ref([
	{
		id: 1,
		title: '方案一',
		isRecommended: true, // 是否推荐方案
		content: '免除全部逾期利息，本金分24期等额还款', // 方案内容描述
		totalAmount: 50000.00, // 还款总额
		monthlyPayment: 2083.33, // 月还款额
		discountAmount: 10000.00, // 减免金额
		periods: 24 // 还款期数
	},
	{
		id: 2,
		title: '方案二',
		isRecommended: false,
		content: '减免50%逾期利息，本金分36期等额还款',
		totalAmount: 55000.00,
		monthlyPayment: 1527.78,
		discountAmount: 5000.00,
		periods: 36
	}
]);

// 计算当前选中的方案
const selectedSolution = computed(() => {
	return solutions.value[selectedSolutionIndex.value];
});

// 生命周期钩子 - 页面加载时执行
onMounted(() => {
	// 获取页面参数
	const pages = getCurrentPages();
	const currentPage = pages[pages.length - 1];
	const options = currentPage.$page?.options;
	
	if (options && options.orderId) {
		orderId.value = options.orderId;
		console.log('接收到工单ID:', orderId.value);
		fetchSolutionDetail(orderId.value);
	} else {
		// 使用默认数据
		console.log('使用默认方案数据');
	}
});

/**
 * 获取方案详情数据
 * @param {String} id - 工单ID
 */
const fetchSolutionDetail = (id) => {
	if (id) {
		// 使用API获取数据
		uni.showLoading({
			title: '加载中...'
		});
		
		api.solution.getDetail(id)
			.then(res => {
				uni.hideLoading();
				if (res.code === 0) {
					// 设置工单基本信息
					workOrderData.value = res.data.workOrder;
					
					// 如果API返回了多个方案，则更新方案数组
					if (res.data.solutions && res.data.solutions.length > 0) {
						solutions.value = res.data.solutions;
					}
				} else {
					uni.showToast({
						title: res.message || '获取方案详情失败',
						icon: 'none'
					});
				}
			})
			.catch(err => {
				uni.hideLoading();
				console.error('获取方案详情失败', err);
				uni.showToast({
					title: '获取方案详情失败',
					icon: 'none'
				});
			});
	} else {
		// 使用模拟数据
		setTimeout(() => {
			console.log('调解方案数据已加载（模拟）');
		}, 500);
	}
};

/**
 * 选择方案
 * @param {Number} index - 方案索引
 */
const selectSolution = (index) => {
	selectedSolutionIndex.value = index;
	console.log('选择了方案:', solutions.value[index].title);
};

/**
 * 查看方案详情
 * @param {Object} solution - 方案对象
 */
const viewSolutionDetail = (solution) => {
	uni.showModal({
		title: `${solution.title}详情`,
		content: `${solution.content}\n\n还款总额: ¥${solution.totalAmount.toFixed(2)}\n月还款额: ¥${solution.monthlyPayment.toFixed(2)}\n还款期数: ${solution.periods}期\n减免金额: ¥${solution.discountAmount.toFixed(2)}`,
		showCancel: false
	});
};

/**
 * 处理确认方案
 * 确认用户选择的方案并提交到服务器
 */
const handleConfirm = () => {
	const selectedSolution = solutions.value[selectedSolutionIndex.value];
	
	// 显示确认对话框
	uni.showModal({
		title: '确认方案',
		content: `您确定要选择${selectedSolution.title}吗？`,
		success: (res) => {
			if (res.confirm) {
				// 用户点击确定
				uni.showLoading({
					title: '处理中...'
				});
				
				// 调用API确认方案
				if (orderId.value) {
					api.solution.confirmSolution(orderId.value, { solutionId: selectedSolution.id })
						.then(res => {
							uni.hideLoading();
							if (res.code === 0) {
								uni.showToast({
									title: '方案已确认',
									icon: 'success',
									duration: 2000
								});
								
								// 成功后跳转到首页
								setTimeout(() => {
									uni.switchTab({
										url: '/pages/index/index',
										success: () => {
											console.log('跳转到首页');
										},
										fail: (err) => {
											console.error('跳转失败', err);
											uni.showToast({
												title: '跳转失败',
												icon: 'none'
											});
										}
									});
								}, 2000);
							} else {
								uni.showToast({
									title: res.message || '操作失败',
									icon: 'none'
								});
							}
						})
						.catch(err => {
							uni.hideLoading();
							console.error('确认方案失败', err);
							uni.showToast({
								title: '确认方案失败',
								icon: 'none'
							});
						});
				} else {
					// 模拟API调用
					setTimeout(() => {
						uni.hideLoading();
						
						uni.showToast({
							title: '方案已确认',
							icon: 'success',
							duration: 2000,
							success: () => {
								// 延迟跳转到首页
								setTimeout(() => {
									uni.switchTab({
										url: '/pages/index/index',
										success: () => {
											console.log('跳转到首页');
										},
										fail: (err) => {
											console.error('跳转失败', err);
											uni.showToast({
												title: '跳转失败',
												icon: 'none'
											});
										}
									});
								}, 2000);
							}
						});
					}, 1000);
				}
			}
		}
	});
};

/**
 * 处理联系客服
 * @param {String} type - 联系方式类型 ('phone' | 'wechat')
 */
const handleContact = (type) => {
	if (type === 'phone') {
		// 拨打电话
		uni.makePhoneCall({
			phoneNumber: '10086', // 替换为实际电话号码
			success: () => {
				console.log('拨打电话成功');
			},
			fail: (err) => {
				console.error('拨打电话失败', err);
			}
		});
	} else if (type === 'wechat') {
		// 复制微信号
		uni.setClipboardData({
			data: 'YourWeChatID', // 替换为实际微信号
			success: () => {
				uni.showToast({
					title: '微信号已复制',
					icon: 'success'
				});
			}
		});
	}
};
</script>

<style lang="scss">
.solution-confirm-container {
	min-height: 100vh;
	background-color: #f5f5f5;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 工单卡片样式 */
.work-order-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.work-order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 10rpx;
}

.work-order-title {
	font-size: 30rpx;
	color: #333;
}

.work-order-id {
	font-weight: bold;
}

.work-order-status {
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
}

.status-label {
	font-size: 24rpx;
	padding: 6rpx 20rpx;
	border-radius: 30rpx;
	background-color: #f0f0f0;
	color: #666;
}

.status-processing {
	background-color: #e5f1ff;
	color: #2979ff;
}

/* 进度条样式 */
.progress-bar {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 20rpx 40rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.progress-steps {
	display: flex;
	justify-content: space-between;
	position: relative;
}

.progress-step {
	display: flex;
	flex-direction: column;
	align-items: center;
	flex: 1;
	position: relative;
}

.step-circle {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background-color: #e0e0e0;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	font-weight: bold;
	margin-bottom: 15rpx;
	position: relative;
	z-index: 2;
}

.step-line {
	position: absolute;
	top: 24rpx;
	left: 50%;
	right: -50%;
	height: 2rpx;
	background-color: #e0e0e0;
	z-index: 1;
}

.progress-step:last-child .step-line {
	display: none;
}

.step-label {
	font-size: 24rpx;
	color: #999;
	text-align: center;
}

.progress-step.active .step-circle {
	background-color: #2979ff;
}

.progress-step.active .step-label {
	color: #2979ff;
	font-weight: bold;
}

.progress-step.completed .step-circle {
	background-color: #2979ff;
}

.step-line.completed {
	background-color: #2979ff;
}

.progress-step.completed .step-label {
	color: #2979ff;
}

/* 方案容器 */
.solutions-container {
	display: flex;
	flex-direction: column;
	gap: 30rpx;
}

/* 方案卡片样式 */
.solution-card {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	border: 2rpx solid #eee;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;
}

.solution-card.selected {
	border-color: #2979ff;
	box-shadow: 0 4rpx 20rpx rgba(41, 121, 255, 0.1);
}

.solution-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #eee;
}

.solution-title-wrap {
	display: flex;
	align-items: center;
}

.solution-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.solution-tag {
	font-size: 24rpx;
	color: #2979ff;
	margin-left: 10rpx;
}

.solution-select {
	display: flex;
	align-items: center;
}

.radio-button {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	border: 2rpx solid #ddd;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.radio-button.selected {
	border-color: #2979ff;
}

.radio-inner {
	width: 24rpx;
	height: 24rpx;
	border-radius: 50%;
	background-color: #2979ff;
}

.solution-content {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 30rpx;
	line-height: 1.5;
}

.solution-details {
	margin-bottom: 20rpx;
}

.solution-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16rpx 0;
}

.solution-label {
	font-size: 28rpx;
	color: #666;
}

.solution-value {
	font-size: 30rpx;
	color: #333;
	font-weight: 500;
}

.solution-footer {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 20rpx;
	border-top: 1px solid #eee;
}

.discount-amount {
	font-size: 24rpx;
	color: #ff3b30;
}

.view-detail {
	font-size: 26rpx;
	color: #2979ff;
	display: flex;
	align-items: center;
}

.view-detail::after {
	content: 'i';
	display: inline-block;
	width: 30rpx;
	height: 30rpx;
	line-height: 30rpx;
	text-align: center;
	font-size: 22rpx;
	font-style: italic;
	font-weight: bold;
	border-radius: 50%;
	background-color: #2979ff;
	color: #fff;
	margin-left: 8rpx;
}

/* 联系客服区域 */
.contact-section {
	display: flex;
	gap: 30rpx;
}

.contact-button {
	flex: 1;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	gap: 10rpx;
	padding: 20rpx;
	border-radius: 12rpx;
	background-color: #fff;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	font-size: 28rpx;
}

.contact-button.phone {
	color: #2979ff;
}

.contact-button.wechat {
	color: #07c160;
}

.contact-button .fa,
.contact-button .fab {
	font-size: 32rpx;
}

/* 底部按钮 */
.action-buttons {
	margin-top: 40rpx;
	padding: 20rpx 0;
}

.confirm-button {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	border-radius: 45rpx;
	font-size: 32rpx;
	background-color: #2979ff;
	color: #fff;
}
</style> 