// 统一API管理模块 - 集成定义和调用方法
// 引入HTTP请求拦截器
import { request, authUtils, createRequest } from '@/server/require.js';
// 引入用户状态管理
import userStore from '@/utils/user-store.js';
// 引入环境配置
import { getBaseURL } from '@/config/env.js';

// 创建不需要认证的请求函数（用于登录等接口）
const requestWithoutAuth = createRequest({ skipAuth: true });

// ==================== API常量定义 ====================

/**
 * 用户相关API
 */
export const USER_API = {
  // 基础用户操作
  INFO: '/user/user_info/',
  LOGIN: '/user/login',
  UPDATE: '/user/update',
  BIND_PHONE: '/user/bind-phone',
  SEND_CODE: '/user/send-code',
  PHONE_LOGIN: '/user/phone-login',

  // 用户操作日志
  OPERATION_LOG: '/user/operation_log/'
};

/**
 * 微信认证相关API
 */
export const WECHAT_API = {
  LOGIN: '/wechat/login/',
  REFRESH: '/wechat/refresh/',
  BIND: '/wechat/bind/',
  UNBIND: '/wechat/unbind/',
  USER_INFO: '/wechat/userinfo/',
  FACEID_AUTH: '/wechat/faceid/auth/'  // 人脸核身认证接口
};

/**
 * 首页相关API
 */
export const HOME_API = {
  GRID_DATA: '/home/<USER>'
};

/**
 * 调解查询相关API
 */
export const MEDIATION_QUERY_API = {
  LIST: '/mediation_management/mediation_case/wechat/list',
  DETAIL: '/mediation_management/mediation_case',
  CASE_BY_NUMBER: '/mediation_management/mediation_case/',
  CASE_COUNT_BY_IDENTITY: '/mediation_management/mediation_case/wechat/by_debtor/'
};

/**
 * 相关API
 */
export const WORK_ORDER_API = {
  DETAIL: '/mediation_management/mediation_case',
  ACCEPT: '/mediation_management/mediation_case/wechat',
  REJECT: '/work-order/reject'
};

/**
 * 调解方案相关API
 */
export const SOLUTION_API = {
  DETAIL: '/solution/detail',
  CONFIRM: '/mediation_management/mediation_case/wechat',
  ADJUST: '/solution/adjust'
};

/**
 * 电子签名相关API
 */
export const ELECTRONIC_SIGNATURE_API = {
  UPDATE: '/mediation_management/mediation_case/wechat',

  // 错误码定义
  ERROR_CODES: {
    FILE_TOO_LARGE: 'FILE_TOO_LARGE',
    INVALID_FORMAT: 'INVALID_FORMAT',
    UPLOAD_FAILED: 'UPLOAD_FAILED',
    CASE_NOT_FOUND: 'CASE_NOT_FOUND',
    PERMISSION_DENIED: 'PERMISSION_DENIED',
    SIGNATURE_EXISTS: 'SIGNATURE_EXISTS'
  },

  // 支持的文件格式
  SUPPORTED_FORMATS: ['png', 'jpg', 'jpeg'],

  // 文件大小限制 (2MB)
  MAX_FILE_SIZE: 2 * 1024 * 1024
};

/**
 * 债权确认相关API
 */
export const DEBT_CONFIRM_API = {
  LIST: '/debt-confirm/list',
  DETAIL: '/debt-confirm/detail',
  SUBMIT: '/debt-confirm/submit'
};

/**
 * 调解投诉相关API
 */
export const MEDIATION_COMPLAINT_API = {
  LIST: '/mediation-complaint/list',
  DETAIL: '/mediation-complaint/detail',
  SUBMIT: '/mediation-complaint/submit'
};

/**
 * 案例展示相关API
 */
export const REAL_CASE_API = {
  LIST: '/case_display/case_display/',
  DETAIL: '/real-case/detail',
  SUBMIT: '/user/files/download/8de29944-dd25-40ad-a5b9-c581ba9d3f5e/',
};

/**
 * 意见反馈相关API
 */
export const FEEDBACK_API = {
  SUBMIT: '/feedback/submit',
  HISTORY: '/feedback/history'
};

/**
 * 所有API的统一导出
 */
export const API_PATHS = {
  USER: USER_API,
  WECHAT: WECHAT_API,
  HOME: HOME_API,
  MEDIATION_QUERY: MEDIATION_QUERY_API,
  WORK_ORDER: WORK_ORDER_API,
  SOLUTION: SOLUTION_API,
  ELECTRONIC_SIGNATURE: ELECTRONIC_SIGNATURE_API,
  DEBT_CONFIRM: DEBT_CONFIRM_API,
  MEDIATION_COMPLAINT: MEDIATION_COMPLAINT_API,
  REAL_CASE: REAL_CASE_API,
  FEEDBACK: FEEDBACK_API
};

/**
 * 根据模板和参数生成完整URL
 * @param {string} pathTemplate - 模板（如 '/user/detail/{id}'）
 * @param {object} params - 参数对象
 * @returns {string} 完整的URL
 */
export const buildApiPath = (pathTemplate, params = {}) => {
  let path = pathTemplate;

  // 替换中的参数占位符
  Object.keys(params).forEach(key => {
    path = path.replace(`{${key}}`, params[key]);
  });

  return path;
};

/**
 * 获取带参数的API辅助函数
 */
export const getApiPath = {
  // 获取用户详情
  userDetail: (id) => `${USER_API.DETAIL}/${id}`,

  // 调解查询列表
  mediationSingleList: () => `${MEDIATION_QUERY_API.LIST}/`,

  // 获取调解查询详情==》调解确认（调解信息、相关文件）
  mediationQueryDetail: (id) => `${MEDIATION_QUERY_API.DETAIL}/${id}/content/`,

  // 获取调解查询详情==》（获取单条调解数据基本信息：案件号、状态、日期）
  mediationSingleDetail: (id) => `${MEDIATION_QUERY_API.LIST}/${id}`,
  

  // 获取待确认案件信息详情
  workOrderDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}/content/`,

  // 获取进行中方案信息详情
  workPlanDetail: (id) => `${WORK_ORDER_API.DETAIL}/${id}/plan_config/`,

  // 获取调解查询详情==》调解确认（接受调解）
  workOrderAccept: (id) => `${WORK_ORDER_API.ACCEPT}/${id}/confirm_status/`,

  // 获取调解查询详情==》已完成
  workOrderCompleted: (id) => `${WORK_ORDER_API.ACCEPT}/${id}/detail/`,

  // 获取拒绝
  workOrderReject: (id) => `${WORK_ORDER_API.REJECT}/${id}`,

  // 获取调解方案详情
  solutionDetail: (orderId) => `${SOLUTION_API.DETAIL}/${orderId}`,

  // 获取调解方案确认
  solutionConfirm: (caseNumber) => `${SOLUTION_API.CONFIRM}/${caseNumber}/update_mediation_plan/`,

  // 获取调解方案调整
  solutionAdjust: (orderId) => `${SOLUTION_API.ADJUST}/${orderId}`,

  // 保存电子签名
  updateElectronicSignature: (caseNumber) => `${ELECTRONIC_SIGNATURE_API.UPDATE}/${caseNumber}/update_electronic_signature/`,

  // 获取债权确认详情
  debtConfirmDetail: (id) => `${DEBT_CONFIRM_API.DETAIL}/${id}`,

  // 获取调解投诉详情
  mediationComplaintDetail: (id) => `${MEDIATION_COMPLAINT_API.DETAIL}/${id}`,

  // 获取案例详情
  realCaseDetail: (id) => `${REAL_CASE_API.DETAIL}/${id}`,

  realCase: () => `${REAL_CASE_API.SUBMIT}`
};

// ==================== API调用方法 ====================

// API调用模块 - 保持原有结构，确保向后兼容
export const api = {
  // Token管理工具 - 使用server/require.js中的authUtils
  auth: authUtils,
  
  // 用户相关
  user: {
    // 获取用户信息
    getUserInfo: () => {
      return request({
        url: USER_API.INFO,
        method: 'GET'
      });
    },

    // 普通登录 - 增强版本，支持登录成功后自动存储认证信息
    login: async (data) => {
      try {
        // 发送登录请求（跳过Authorization头部）
        const response = await requestWithoutAuth({
          url: USER_API.LOGIN,
          method: 'POST',
          data
        });

        // 检查登录是否成功
        if (response && response.success !== false) {
          // 获取登录响应数据
          const loginData = response.data || response;
          
          // 1. 存储token信息（支持新旧格式）
          if (loginData.access_token && loginData.token_type) {
            // 新格式：使用access_token和token_type
            authUtils.setTokenInfo({
              access_token: loginData.access_token,
              token_type: loginData.token_type,
              expires_in: loginData.expires_in
            });
          } else if (loginData.token) {
            // 旧格式兼容：直接设置token
            authUtils.setToken(loginData.token);
          }

          // 2. 存储refresh_token
          if (loginData.refresh_token) {
            authUtils.setRefreshToken(loginData.refresh_token);
          }

          // 3. 存储openid
          if (loginData.openid) {
            uni.setStorageSync('openid', loginData.openid);
          }

          // 4. 存储unionid（如果有）
          if (loginData.unionid) {
            uni.setStorageSync('unionid', loginData.unionid);
          }

          // 5. 存储用户信息
          if (loginData.user_info || loginData.userInfo) {
            const userInfo = loginData.user_info || loginData.userInfo;
            userStore.setUserInfo(userInfo);
          }

          // 6. 存储微信用户信息（如果有）
          if (loginData.wechat_userInfo || loginData.wechatInfo) {
            const wechatInfo = loginData.wechat_userInfo || loginData.wechatInfo;
            userStore.setWechatUserInfo(wechatInfo);
          }

          console.log('登录成功，认证信息已自动存储');
        }

        return response;
      } catch (error) {
        console.error('登录失败:', error);
        throw error;
      }
    },

    // 更新用户信息
    updateUserInfo: (data) => {
      return request({
        url: USER_API.UPDATE,
        method: 'PUT',
        data
      });
    },

    // 绑定手机号
    bindPhone: (data) => {
      return request({
        url: USER_API.BIND_PHONE,
        method: 'POST',
        data
      });
    },

    // 发送验证码（跳过Authorization头部）
    sendVerifyCode: (data) => {
      return requestWithoutAuth({
        url: USER_API.SEND_CODE,
        method: 'POST',
        data
      });
    },

    // 手机号登录 - 增强版本，支持登录成功后自动存储认证信息
    phoneLogin: async (data) => {
      try {
        // 发送手机号登录请求（跳过Authorization头部）
        const response = await requestWithoutAuth({
          url: USER_API.PHONE_LOGIN,
          method: 'POST',
          data
        });

        // 检查登录是否成功
        if (response && response.success !== false) {
          // 获取登录响应数据
          const loginData = response.data || response;
          
          // 1. 存储token信息
          if (loginData.access_token && loginData.token_type) {
            authUtils.setTokenInfo({
              access_token: loginData.access_token,
              token_type: loginData.token_type,
              expires_in: loginData.expires_in
            });
          } else if (loginData.token) {
            // 兼容旧格式
            authUtils.setToken(loginData.token);
          }

          // 2. 存储refresh_token
          if (loginData.refresh_token) {
            authUtils.setRefreshToken(loginData.refresh_token);
          }

          // 3. 存储openid（如果有）
          if (loginData.openid) {
            uni.setStorageSync('openid', loginData.openid);
          }

          // 4. 存储用户信息
          if (loginData.user_info || loginData.userInfo) {
            const userInfo = loginData.user_info || loginData.userInfo;
            userStore.setUserInfo(userInfo);
          }

          console.log('手机号登录成功，认证信息已自动存储');
        }

        return response;
      } catch (error) {
        console.error('手机号登录失败:', error);
        throw error;
      }
    }
  },

  // 微信认证相关
  wechat: {
    // 微信登录 - 增强版本，支持登录成功后自动存储认证信息
    login: async (data) => {
      try {
        // 发送微信登录请求（跳过Authorization头部）
        const response = await requestWithoutAuth({
          url: WECHAT_API.LOGIN,
          method: 'POST',
          data
        });

        // 检查登录是否成功
        if (response && response.success !== false) {
          // 获取登录响应数据
          const loginData = response.data || response;
          
          // 1. 存储token信息
          if (loginData.access_token && loginData.token_type) {
            authUtils.setTokenInfo({
              access_token: loginData.access_token,
              token_type: loginData.token_type,
              expires_in: loginData.expires_in
            });
          } else if (loginData.token) {
            // 兼容旧格式
            authUtils.setToken(loginData.token);
          }

          // 2. 存储refresh_token
          if (loginData.refresh_token) {
            authUtils.setRefreshToken(loginData.refresh_token);
          }

          // 3. 存储openid
          if (loginData.openid) {
            uni.setStorageSync('openid', loginData.openid);
          }

          // 4. 存储unionid（如果有）
          if (loginData.unionid) {
            uni.setStorageSync('unionid', loginData.unionid);
          }

          // 5. 存储sessionKey（如果有）
          if (loginData.sessionKey || loginData.session_key) {
            uni.setStorageSync('sessionKey', loginData.sessionKey || loginData.session_key);
          }

          // 6. 存储用户信息
          if (loginData.user_info || loginData.userInfo) {
            const userInfo = loginData.user_info || loginData.userInfo;
            userStore.setUserInfo(userInfo);
          }

          // 7. 存储微信用户信息
          if (loginData.wechat_userInfo || loginData.wechatInfo) {
            const wechatInfo = loginData.wechat_userInfo || loginData.wechatInfo;
            userStore.setWechatUserInfo(wechatInfo);
          }

          console.log('微信登录成功，认证信息已自动存储');
        }

        return response;
      } catch (error) {
        console.error('微信登录失败:', error);
        throw error;
      }
    },

    // 微信token刷新（使用refresh_token，跳过Authorization头部）
    refresh: (data) => {
      return requestWithoutAuth({
        url: WECHAT_API.REFRESH,
        method: 'POST',
        data
      });
    },

    // 绑定微信
    bind: (data) => {
      return request({
        url: WECHAT_API.BIND,
        method: 'POST',
        data
      });
    },

    // 解绑微信
    unbind: () => {
      return request({
        url: WECHAT_API.UNBIND,
        method: 'POST'
      });
    },

    // 获取微信用户信息
    getUserInfo: (data) => {
      return request({
        url: WECHAT_API.USER_INFO,
        method: 'POST',
        data
      });
    },

    // 人脸核身认证
    faceIdAuth: (data) => {
      return request({
        url: WECHAT_API.FACEID_AUTH,
        method: 'POST',
        data
      });
    }
  },
  
  // 首页相关
  home: {
    // 获取首页卡片数据（公开数据，跳过Authorization头部）
    getGridData: () => {
      return requestWithoutAuth({
        url: HOME_API.GRID_DATA,
        method: 'GET'
      });
    }
  },
  
  // 调解查询
  mediationQuery: {
    // 获取调解查询列表
    getAuthenticatedList: (params) => {
      return request({
        url: getApiPath.mediationSingleList(),
        // url: MEDIATION_QUERY_API.LIST,
        method: 'GET',
        data: params
      });
    },

    // 获取调解查询详情
    getDetail: (id) => {
      return request({
        url: getApiPath.mediationQueryDetail(id),
        method: 'GET'
      });
    },

    // 获取单条调解数据基本信息（案件号、状态、日期）
    getSingleDetail: (id) => {
      return request({
        url: getApiPath.mediationSingleDetail(id),
        method: 'GET'
      });
    },

    // 根据调解案件号查询调解案件
    getCaseByNumber: (caseNumber) => {
      return request({
        url: MEDIATION_QUERY_API.CASE_BY_NUMBER,
        method: 'GET',
        data: {
          case_number: caseNumber
        }
      });
    },

    // 根据身份信息查询调解案件数量
    getCaseCountByIdentity: (params) => {
      return request({
        url: MEDIATION_QUERY_API.CASE_COUNT_BY_IDENTITY,
        method: 'GET',
        data: params
      });
    }
  },
  
  // 调解信息相关
  workOrder: {
    // 获取详情
    getDetail: (id) => {
      return request({
        url: getApiPath.workOrderDetail(id),
        method: 'GET'
      });
    },

    // 接受调解
    acceptWorkOrder: (id) => {
      return request({
        url: getApiPath.workOrderAccept(id),
        method: 'PUT'
      });
    },

    // 获取已完成的调解信息，还款方案
    getCompletedWorkOrder: (id) => {
      return request({
        url: getApiPath.workOrderCompleted(id),
        method: 'GET'
      });
    },
    // 拒绝
    /* rejectWorkOrder: (id, reason) => {
      return request({
        url: getApiPath.workOrderReject(id),
        method: 'POST',
        data: { reason }
      });
    } */
  },
  
  // 调解方案相关
  solution: {
    // 获取调解方案详情
    getDetail: (orderId) => {
      return request({
        url: getApiPath.solutionDetail(orderId),
        method: 'GET'
      });
    },

    // 获取工作方案详情配置数据
    getPlanDetail: (id) => {
      return request({
        url: getApiPath.workPlanDetail(id),
        method: 'GET'
      });
    },

    // 确认调解方案
    confirmSolution: (caseNumber, data) => {
      return request({
        url: getApiPath.solutionConfirm(caseNumber),
        method: 'PUT',
        data
      });
    },
    

    getDetails: () => {
      return request({
        url: getApiPath.realCase(),
        method: 'GET'
      });
    },

    // 申请调整方案
    adjustSolution: (orderId, data) => {
      return request({
        url: getApiPath.solutionAdjust(orderId),
        method: 'POST',
        data
      });
    }
  },

  // 电子签名相关 - 简化版本，只提供API路径和工具函数
  electronicSignature: {
    // 获取更新电子签名的API路径
    getUpdateSignatureUrl: (caseNumber,data) => {
      return request({
        url: getApiPath.updateElectronicSignature(caseNumber),
        method: 'POST',
        data,
      });
    },
    /* getUpdateSignatureUrl: (caseNumber) => {
      return `${getBaseURL()}${getApiPath.getUpdateSignatureUrl: (caseNumber) => {(caseNumber)}`;
    }, */

    // 验证文件格式
    validateFileFormat: (format) => {
      return ELECTRONIC_SIGNATURE_API.SUPPORTED_FORMATS.includes(format.toLowerCase());
    },

    // 验证文件大小
    validateFileSize: (size) => {
      return size <= ELECTRONIC_SIGNATURE_API.MAX_FILE_SIZE;
    },

    // 获取错误信息
    getErrorMessage: (errorCode) => {
      const errorMessages = {
        [ELECTRONIC_SIGNATURE_API.ERROR_CODES.FILE_TOO_LARGE]: `文件过大，最大支持${Math.round(ELECTRONIC_SIGNATURE_API.MAX_FILE_SIZE / 1024 / 1024)}MB`,
        [ELECTRONIC_SIGNATURE_API.ERROR_CODES.INVALID_FORMAT]: `不支持的文件格式，仅支持${ELECTRONIC_SIGNATURE_API.SUPPORTED_FORMATS.join('、').toUpperCase()}格式`,
        [ELECTRONIC_SIGNATURE_API.ERROR_CODES.UPLOAD_FAILED]: '文件上传失败，请重试',
        [ELECTRONIC_SIGNATURE_API.ERROR_CODES.CASE_NOT_FOUND]: '案件不存在或已被删除',
        [ELECTRONIC_SIGNATURE_API.ERROR_CODES.PERMISSION_DENIED]: '权限不足，无法上传电子签名',
        [ELECTRONIC_SIGNATURE_API.ERROR_CODES.SIGNATURE_EXISTS]: '电子签名已存在，无需重复上传'
      };
      return errorMessages[errorCode] || '未知错误';
    }
  },
  
  // 债权确认
  debtConfirm: {
    // 获取债权确认列表
    getList: (params) => {
      return request({
        url: DEBT_CONFIRM_API.LIST,
        method: 'GET',
        data: params
      });
    },
    
    // 获取债权确认详情
    getDetail: (id) => {
      return request({
        url: getApiPath.debtConfirmDetail(id),
        method: 'GET'
      });
    },
    
    // 提交债权确认
    submit: (data) => {
      return request({
        url: DEBT_CONFIRM_API.SUBMIT,
        method: 'POST',
        data
      });
    }
  },
  
  // 调解投诉
  mediationComplaint: {
    // 获取投诉列表
    getList: (params) => {
      return request({
        url: MEDIATION_COMPLAINT_API.LIST,
        method: 'GET',
        data: params
      });
    },
    
    // 获取投诉详情
    getDetail: (id) => {
      return request({
        url: getApiPath.mediationComplaintDetail(id),
        method: 'GET'
      });
    },
    
    // 提交投诉
    submit: (data) => {
      return request({
        url: MEDIATION_COMPLAINT_API.SUBMIT,
        method: 'POST',
        data
      });
    }
  },
  
  // 案例展示
  realCase: {
    // 获取案例列表（公开数据，跳过Authorization头部）
    getList: (params) => {
      return requestWithoutAuth({
        url: REAL_CASE_API.LIST,
        method: 'GET',
        data: params
      });
    },
    
    // 获取案例详情（公开数据，跳过Authorization头部）
    getDetail: (id) => {
      return requestWithoutAuth({
        url: getApiPath.realCaseDetail(id),
        method: 'GET'
      });
    },
     /* getSingleDetail: (id) => {
      return request({
        url: getApiPath.mediationSingleDetail(id),
        method: 'GET'
      });
    }, */
  },

  // 用户操作日志 - 支持操作记录功能
  operationLog: {
    // 记录用户操作
    recordOperation: (data) => {
      return request({
        url: USER_API.OPERATION_LOG,
        method: 'POST',
        data
      }).catch(error => {
        // 操作日志记录失败时静默处理，不影响主流程
        console.warn('操作日志记录失败:', error.message);
        return Promise.resolve();
      });
    }
  },
  
  // 意见反馈
  feedback: {
    // 提交反馈
    submitFeedback: (data) => {
      return request({
        url: FEEDBACK_API.SUBMIT,
        method: 'POST',
        data
      });
    },
    
    // 获取反馈历史
    getFeedbackHistory: () => {
      return request({
        url: FEEDBACK_API.HISTORY,
        method: 'GET'
      });
    }
  }
};

export default api; 