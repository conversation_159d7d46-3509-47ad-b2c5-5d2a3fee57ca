@echo off
chcp 65001 >nul
echo ===============================================
echo 🔧 修复webview页面导航错误
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 当前项目目录: %CD%
echo.

echo 📋 诊断结果：webview页面在pages.json中配置正确，但编译后丢失
echo.

echo 🚀 开始修复流程...
echo.

echo 📁 第一步：清理编译缓存...
if exist "unpackage\dist\dev\mp-weixin" (
    echo 正在删除开发环境编译缓存...
    rmdir /s /q "unpackage\dist\dev\mp-weixin" 2>nul
    echo ✅ 开发环境缓存已清理
) else (
    echo ℹ️ 开发环境缓存不存在
)

if exist "unpackage\dist\build\mp-weixin" (
    echo 正在删除生产环境编译缓存...
    rmdir /s /q "unpackage\dist\build\mp-weixin" 2>nul
    echo ✅ 生产环境缓存已清理
) else (
    echo ℹ️ 生产环境缓存不存在
)

echo.
echo 🗂️ 第二步：清理HBuilderX缓存...
if exist ".hbuilderx" (
    rmdir /s /q ".hbuilderx" 2>nul
    echo ✅ HBuilderX缓存已清理
) else (
    echo ℹ️ HBuilderX缓存不存在
)

echo.
echo 📄 第三步：验证关键文件完整性...

echo 检查 pages.json...
if exist "pages.json" (
    findstr /c:"pages/webview/webview" pages.json >nul 2>&1
    if %errorlevel%==0 (
        echo ✅ pages.json中webview页面配置存在
    ) else (
        echo ❌ pages.json中缺少webview页面配置
    )
) else (
    echo ❌ pages.json文件不存在
)

echo 检查 webview页面文件...
if exist "pages\webview\webview.vue" (
    echo ✅ webview页面文件存在
) else (
    echo ❌ webview页面文件不存在
)

echo 检查 manifest.json...
if exist "manifest.json" (
    echo ✅ manifest.json存在
) else (
    echo ❌ manifest.json不存在
)

echo.
echo 🔧 第四步：检查TypeScript配置问题...
findstr /c:"lang=\"ts\"" pages\webview\webview.vue >nul 2>&1
if %errorlevel%==0 (
    echo ⚠️ 发现webview页面使用TypeScript，这可能导致编译问题
    echo 建议：如果项目不完全支持TS，请考虑改为JavaScript
) else (
    echo ✅ webview页面未使用TypeScript
)

echo.
echo 🌐 第五步：检查域名白名单配置...
findstr /c:"urlCheck" manifest.json >nul 2>&1
if %errorlevel%==0 (
    echo ✅ 已设置urlCheck配置
) else (
    echo ⚠️ 建议在manifest.json的mp-weixin中添加："setting": {"urlCheck": false}
)

echo.
echo ===============================================
echo ✨ 清理完成！接下来请按步骤操作：
echo ===============================================
echo.
echo 1️⃣ 完全关闭 HBuilderX
echo.
echo 2️⃣ 重新打开 HBuilderX
echo.
echo 3️⃣ 在 HBuilderX 中执行：
echo    菜单栏 → 运行 → 清理项目
echo.
echo 4️⃣ 重新编译项目：
echo    运行 → 运行到小程序模拟器 → 微信开发者工具
echo.
echo 5️⃣ 验证修复：
echo    - 检查开发者工具控制台是否还有错误
echo    - 尝试导航到webview页面
echo.
echo 6️⃣ 如果问题仍存在：
echo    - 检查webview页面的TypeScript语法
echo    - 考虑临时改为JavaScript实现
echo    - 确认域名白名单配置
echo.
echo ⚠️ 注意事项：
echo   - 首次编译可能较慢
echo   - 确保微信开发者工具已开启服务端口
echo   - 如需访问外部域名，请在小程序后台配置业务域名
echo.
echo ===============================================
pause 