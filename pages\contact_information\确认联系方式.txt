<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>确认联系方式</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Custom styles that can't be done with Tailwind */
    .gradient-bg {
      background: linear-gradient(135deg, #e6f0ff 0%, #e6f0ff 100%);
    }
    .primary-btn {
      background-color: #3b7eeb;
    }
    .primary-btn:hover {
      background-color: #2a6ed8;
    }
    .input-style {
      border: 1px solid #e2e8f0;
      transition: border-color 0.3s;
    }
    .input-style:focus {
      border-color: #3b7eeb;
      outline: none;
    }
  </style>
</head>
<body class="bg-gray-50 min-h-screen">
  <div id="app" class="container mx-auto px-4 py-8 max-w-md">
    <!-- Header Card with Gradient Background -->
    <div class="gradient-bg rounded-xl p-6 mb-6 shadow-sm text-center">
      <div class="flex justify-center mb-4">
        <i class="fas fa-mobile-alt text-4xl text-blue-600"></i>
      </div>
      <h2 class="text-xl font-semibold text-gray-800 mb-2">确认联系方式</h2>
      <p class="text-gray-600 text-sm">为了及时向您发送调解结果短信，便于日后快速查找相关信息，请确认您的手机号码</p>
    </div>

    <!-- Form Card -->
    <div class="bg-white rounded-xl p-6 shadow-sm">
      <h3 class="text-lg font-medium text-gray-800 mb-6">确认联系方式</h3>
      
      <!-- Phone Number Input -->
      <div class="mb-4">
        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">手机号码</label>
        <input 
          type="tel" 
          id="phone" 
          v-model="phoneNumber" 
          placeholder="请输入您的手机号码" 
          class="w-full px-4 py-3 rounded-lg input-style text-gray-700"
        >
        <p class="text-xs text-gray-500 mt-1">此手机号仅用于发送调解结果短信，不会用于其他用途</p>
      </div>
      
      <!-- Verification Code -->
      <div class="mb-6">
        <label for="code" class="block text-sm font-medium text-gray-700 mb-1">短信验证码</label>
        <div class="flex gap-2">
          <input 
            type="text" 
            id="code" 
            v-model="verificationCode" 
            placeholder="请输入验证码" 
            class="flex-1 px-4 py-3 rounded-lg input-style text-gray-700"
          >
          <button 
            @click="getVerificationCode" 
            :disabled="countdown > 0"
            class="px-4 py-3 rounded-lg text-sm font-medium whitespace-nowrap"
            :class="countdown > 0 ? 'bg-gray-200 text-gray-500' : 'bg-blue-100 text-blue-600 hover:bg-blue-200'"
          >
            {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
          </button>
        </div>
      </div>
      
      <!-- Submit Button -->
      <button 
        @click="confirmContact" 
        class="w-full py-3 rounded-lg text-white font-medium primary-btn hover:shadow-md transition-all"
      >
        确认联系方式
      </button>
    </div>
  </div>

  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
  <script>
    const { createApp, ref } = Vue;
    
    createApp({
      setup() {
        // 模拟数据状态
        const phoneNumber = ref(''); // 手机号码
        const verificationCode = ref(''); // 验证码
        const countdown = ref(0); // 验证码倒计时
        const isSubmitting = ref(false); // 提交状态
        
        // 模拟获取验证码
        const getVerificationCode = () => {
          if (!phoneNumber.value) {
            alert('请输入手机号码');
            return;
          }
          
          // 简单的手机号验证
          if (!/^1[3-9]\d{9}$/.test(phoneNumber.value)) {
            alert('请输入正确的手机号码');
            return;
          }
          
          // 模拟API调用 - 实际开发中替换为真实的API调用
          console.log('调用发送验证码API，手机号:', phoneNumber.value);
          
          // 开始倒计时
          countdown.value = 60;
          const timer = setInterval(() => {
            countdown.value--;
            if (countdown.value <= 0) {
              clearInterval(timer);
            }
          }, 1000);
          
          // 模拟API返回 - 实际开发中处理真实的API响应
          setTimeout(() => {
            console.log('验证码已发送');
            // 这里可以添加提示，如Toast通知
          }, 1000);
        };
        
        // 模拟确认联系方式
        const confirmContact = () => {
          if (!phoneNumber.value) {
            alert('请输入手机号码');
            return;
          }
          
          if (!verificationCode.value) {
            alert('请输入验证码');
            return;
          }
          
          // 简单的验证码验证
          if (verificationCode.value.length !== 6) {
            alert('请输入6位验证码');
            return;
          }
          
          isSubmitting.value = true;
          
          // 模拟API调用 - 实际开发中替换为真实的API调用
          console.log('调用确认联系方式API', {
            phone: phoneNumber.value,
            code: verificationCode.value
          });
          
          // 模拟API响应 - 实际开发中处理真实的API响应
          setTimeout(() => {
            isSubmitting.value = false;
            alert('联系方式确认成功！');
            // 这里可以添加跳转逻辑，如跳转到下一个页面
          }, 1500);
        };
        
        return {
          phoneNumber,
          verificationCode,
          countdown,
          isSubmitting,
          getVerificationCode,
          confirmContact
        };
      }
    }).mount('#app');
  </script>
</body>
</html>