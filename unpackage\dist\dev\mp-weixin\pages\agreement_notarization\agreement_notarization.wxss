/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-2eac678c:root {
  --text-color: #333;
  --text-secondary: #666;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --primary-color: #3b7eeb;
  --primary-dark: #2c62c9;
  --border-color: #e0e0e0;
  --transition-normal: all 0.3s ease;
}
.work-order-detail-container.data-v-2eac678c {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}
.work-order-card.data-v-2eac678c {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.work-order-header.data-v-2eac678c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.work-order-title.data-v-2eac678c {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}
.work-order-status.data-v-2eac678c {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}
.status-label.data-v-2eac678c {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #52c41a;
  color: #fff;
}
.status-pending.data-v-2eac678c {
  background-color: #52c41a;
}
.work-order-date.data-v-2eac678c {
  font-size: 28rpx;
  color: #666;
}
.progress-steps.data-v-2eac678c {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.progress-step.data-v-2eac678c {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}
.step-circle.data-v-2eac678c {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}
.step-line.data-v-2eac678c {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}
.progress-step:last-child .step-line.data-v-2eac678c {
  display: none;
}
.step-line.active.data-v-2eac678c {
  background-color: #2979ff;
}
.step-label.data-v-2eac678c {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.progress-step.active .step-circle.data-v-2eac678c {
  background-color: #2979ff;
}
.progress-step.active .step-label.data-v-2eac678c {
  color: #2979ff;
  font-weight: bold;
}
.card.data-v-2eac678c {
  background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%);
  border: 2rpx solid #ffd54f;
  border-radius: 32rpx;
  padding: 50rpx 40rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}
.info-section.data-v-2eac678c {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.section-title.data-v-2eac678c {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  display: inline-flex;
}

/* 免费公证服务卡片样式 */
.free-service-card.data-v-2eac678c {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 2rpx solid #b3e5fc;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  position: relative;
  overflow: hidden;
}
.free-service-card.data-v-2eac678c::before {
  content: "";
  position: absolute;
  top: -20rpx;
  right: -20rpx;
  width: 120rpx;
  height: 120rpx;
  background: #d9f0ff;
  border-radius: 50%;
}
.service-header.data-v-2eac678c {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.service-header .service-icon.data-v-2eac678c {
  width: 100rpx;
  height: 100rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
}
.service-header .service-icon .fas.data-v-2eac678c {
  color: #fff;
  font-size: 40rpx;
}
.service-title.data-v-2eac678c {
  font-size: 36rpx;
  font-weight: bold;
  color: #3b7eeb;
  margin-bottom: 10rpx;
}
.service-subtitle.data-v-2eac678c {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.price-section.data-v-2eac678c {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.price-item.data-v-2eac678c {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.price-item.data-v-2eac678c:last-child {
  margin-bottom: 0;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}
.price-label.data-v-2eac678c {
  font-size: 28rpx;
  color: #333;
}
.price-content.data-v-2eac678c {
  display: flex;
  align-items: center;
}
.original-price.data-v-2eac678c {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
  margin-right: 15rpx;
}
.free-price.data-v-2eac678c {
  font-size: 32rpx;
  color: var(--success-color);
  font-weight: bold;
}
.final-price.data-v-2eac678c {
  font-size: 36rpx;
  color: var(--success-color);
  font-weight: bold;
}
.service-tips.data-v-2eac678c {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--success-color);
  line-height: 1.5;
}
.service-tips .fas.data-v-2eac678c {
  color: var(--success-color);
  font-size: 28rpx;
  margin-right: 12rpx;
}

/* 公证决策模块样式更新 */
.notarization-decision.data-v-2eac678c {
  background: white;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}
.decision-header.data-v-2eac678c {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}
.decision-title.data-v-2eac678c {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.decision-description.data-v-2eac678c {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 40rpx;
}
.decision-option.data-v-2eac678c {
  border: 3rpx solid #e0e0e0;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fafafa;
}
.decision-option.data-v-2eac678c:last-child {
  margin-bottom: 0;
}
.decision-option.data-v-2eac678c:active {
  transform: scale(0.98);
}

/* 选中申请协议公证的样式 */
.decision-option.selected-apply.data-v-2eac678c {
  border-color: #3b7eeb;
  background: #3b7eeb;
}
.decision-option.selected-apply .option-title.data-v-2eac678c {
  color: white;
}
.decision-option.selected-apply .option-description.data-v-2eac678c {
  color: rgba(255, 255, 255, 0.9);
}
.decision-option.selected-apply .option-badge.data-v-2eac678c {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 选中暂不公证的样式 */
.decision-option.selected-skip.data-v-2eac678c {
  border-color: #faad14;
  background: #fff7e6;
}
.decision-option.selected-skip .option-title.data-v-2eac678c {
  color: #faad14;
}
.decision-option.selected-skip .option-description.data-v-2eac678c {
  color: #b8860b;
}
.option-header.data-v-2eac678c {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  position: relative;
}
.radio-container.data-v-2eac678c {
  margin-right: 20rpx;
}
.radio-button.data-v-2eac678c {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #e0e0e0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: white;
}

/* 申请协议公证选中时的单选框样式 */
.radio-button.radio-checked-apply.data-v-2eac678c {
  border-color: #3b7eeb;
  background: var(--primary-color);
}

/* 暂不公证选中时的单选框样式 */
.radio-button.radio-checked-skip.data-v-2eac678c {
  border-color: #faad14;
  background: #faad14;
}
.option-title.data-v-2eac678c {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}
.option-badge.data-v-2eac678c {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%);
  color: white;
  font-size: 20rpx;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}
.option-description.data-v-2eac678c {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-left: 60rpx;
}
.action-buttons.data-v-2eac678c {
  margin-top: 40rpx;
  padding: 20rpx 0;
}
.confirm-button.data-v-2eac678c {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  background-color: #2979ff;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm-button .fas.data-v-2eac678c {
  margin-right: 10rpx;
}