{"version": 3, "file": "avatar-helper.js", "sources": ["utils/avatar-helper.js"], "sourcesContent": ["// 头像管理工具\nimport { isDebug } from '@/config/env.js';\nimport { AssetUrlGenerator, ASSETS_CONFIG } from '@/config/assets.js';\nimport { getBase64Avatar } from '@/utils/default-avatar-base64.js';\n\n/**\n * 头像错误处理配置\n */\nexport const AVATAR_ERROR_CONFIG = {\n  maxRetries: 3,           // 最大重试次数\n  retryDelay: 1000,        // 重试延迟（毫秒）\n  fallbackDelay: 500,      // 切换到备用头像的延迟\n  compressionQuality: 0.8, // 压缩质量\n  maxFileSize: 2 * 1024 * 1024, // 最大文件大小 2MB\n  allowedTypes: ['image/jpeg', 'image/png', 'image/webp'] // 允许的文件类型\n};\n\n/**\n * 获取默认头像URL - 使用资源配置，支持base64回退\n * @param {string} type 头像类型 ('default', 'male', 'female', 'placeholder', 'loading')\n * @param {string} gender 用户性别 (0-男, 1-女, 2-保密)\n * @param {boolean} useBase64Fallback 是否使用base64作为最终回退\n * @returns {string} 头像URL\n */\nexport function getDefaultAvatar(type = 'default', gender = null, useBase64Fallback = true) {\n  // 首先尝试从资源配置获取\n  const avatarUrl = AssetUrlGenerator.getAvatarUrl(type, { gender, fallback: true });\n\n  // 如果获取失败且允许base64回退，返回base64头像\n  if (!avatarUrl && useBase64Fallback) {\n    return getBase64Avatar(type, gender);\n  }\n\n  return avatarUrl || getBase64Avatar(type, gender);\n}\n\n/**\n * 验证头像文件\n * @param {string} filePath 文件路径\n * @returns {Promise<Object>} 验证结果\n */\nexport function validateAvatarFile(filePath) {\n  return new Promise((resolve, reject) => {\n    if (!filePath) {\n      reject(new Error('头像文件路径不能为空'));\n      return;\n    }\n\n    // 获取文件信息\n    uni.getFileInfo({\n      filePath: filePath,\n      success: (res) => {\n        const fileSize = res.size;\n        \n        // 检查文件大小\n        if (fileSize > AVATAR_ERROR_CONFIG.maxFileSize) {\n          reject(new Error(`头像文件过大，请选择小于${Math.round(AVATAR_ERROR_CONFIG.maxFileSize / 1024 / 1024)}MB的图片`));\n          return;\n        }\n        \n        // 检查文件类型（通过后缀名简单判断）\n        const extension = filePath.toLowerCase().split('.').pop();\n        const validExtensions = ['jpg', 'jpeg', 'png', 'webp'];\n        \n        if (!validExtensions.includes(extension)) {\n          reject(new Error('不支持的图片格式，请选择JPG、PNG或WebP格式的图片'));\n          return;\n        }\n        \n        resolve({\n          valid: true,\n          fileSize,\n          extension,\n          filePath\n        });\n      },\n      fail: (error) => {\n        reject(new Error('获取头像文件信息失败：' + error.errMsg));\n      }\n    });\n  });\n}\n\n/**\n * 压缩头像图片\n * @param {string} filePath 原始文件路径\n * @param {Object} options 压缩选项\n * @returns {Promise<string>} 压缩后的文件路径\n */\nexport function compressAvatar(filePath, options = {}) {\n  return new Promise((resolve, reject) => {\n    const defaultOptions = {\n      quality: AVATAR_ERROR_CONFIG.compressionQuality,\n      width: 400,  // 压缩到400x400\n      height: 400,\n      ...options\n    };\n\n    // 使用uni.compressImage进行压缩\n    uni.compressImage({\n      src: filePath,\n      quality: Math.round(defaultOptions.quality * 100),\n      width: defaultOptions.width,\n      height: defaultOptions.height,\n      success: (res) => {\n        if (isDebug()) {\n          console.log('头像压缩成功:', {\n            original: filePath,\n            compressed: res.tempFilePath\n          });\n        }\n        resolve(res.tempFilePath);\n      },\n      fail: (error) => {\n        if (isDebug()) {\n          console.warn('头像压缩失败，使用原图:', error);\n        }\n        // 压缩失败时返回原图\n        resolve(filePath);\n      }\n    });\n  });\n}\n\n/**\n * 处理头像加载错误 - 增强版本，支持多级回退\n * @param {string} avatarUrl 原始头像URL\n * @param {Object} options 选项\n * @returns {string} 备用头像URL\n */\nexport function handleAvatarError(avatarUrl, options = {}) {\n  const { gender, useGenderDefault = true, useBase64Fallback = true } = options;\n\n  if (isDebug()) {\n    console.warn('头像加载失败，使用默认头像:', avatarUrl);\n  }\n\n  // 如果启用性别默认头像且有性别信息\n  if (useGenderDefault && gender !== undefined) {\n    return getDefaultAvatar('default', gender, useBase64Fallback);\n  }\n\n  // 返回通用默认头像，优先使用base64确保可用性\n  return getDefaultAvatar('default', null, useBase64Fallback);\n}\n\n/**\n * 上传头像到服务器\n * @param {string} filePath 本地文件路径\n * @param {Object} options 上传选项\n * @returns {Promise<Object>} 上传结果\n */\nexport function uploadAvatar(filePath, options = {}) {\n  return new Promise(async (resolve, reject) => {\n    try {\n      // 验证文件\n      await validateAvatarFile(filePath);\n      \n      // 压缩图片\n      const compressedPath = await compressAvatar(filePath, options.compression);\n      \n      const defaultOptions = {\n        url: '/api/upload/avatar',\n        name: 'avatar',\n        timeout: 30000,\n        ...options\n      };\n\n      // 上传文件\n      uni.uploadFile({\n        url: defaultOptions.url,\n        filePath: compressedPath,\n        name: defaultOptions.name,\n        timeout: defaultOptions.timeout,\n        header: {\n          'Authorization': uni.getStorageSync('token') || '',\n          ...defaultOptions.header\n        },\n        success: (res) => {\n          try {\n            const data = JSON.parse(res.data);\n            if (data.success) {\n              resolve({\n                success: true,\n                url: data.data.url,\n                originalPath: filePath,\n                compressedPath: compressedPath\n              });\n            } else {\n              reject(new Error(data.message || '上传失败'));\n            }\n          } catch (parseError) {\n            reject(new Error('服务器响应格式错误'));\n          }\n        },\n        fail: (error) => {\n          let errorMessage = '上传失败';\n          if (error.errMsg) {\n            if (error.errMsg.includes('timeout')) {\n              errorMessage = '上传超时，请重试';\n            } else if (error.errMsg.includes('fail')) {\n              errorMessage = '网络连接失败，请检查网络';\n            } else {\n              errorMessage = '上传失败：' + error.errMsg;\n            }\n          }\n          reject(new Error(errorMessage));\n        }\n      });\n    } catch (error) {\n      reject(error);\n    }\n  });\n}\n\n/**\n * 创建头像预览URL（用于显示）- 增强版本\n * @param {string} avatarUrl 头像URL\n * @param {Object} options 选项\n * @returns {string} 预览URL\n */\nexport function createAvatarPreviewUrl(avatarUrl, options = {}) {\n  const { addTimestamp = false, gender } = options;\n\n  if (!avatarUrl || !AssetUrlGenerator.isValidUrl(avatarUrl)) {\n    return getDefaultAvatar('placeholder', gender);\n  }\n\n  // 如果是本地临时文件，直接返回\n  if (avatarUrl.startsWith('wxfile://') || avatarUrl.startsWith('http://tmp/')) {\n    return avatarUrl;\n  }\n\n  // 如果需要添加时间戳\n  if (addTimestamp) {\n    return AssetUrlGenerator.addTimestamp(avatarUrl);\n  }\n\n  return avatarUrl;\n}\n\n/**\n * 清理临时头像文件\n * @param {Array<string>} filePaths 文件路径数组\n */\nexport function cleanupTempAvatars(filePaths) {\n  if (!Array.isArray(filePaths)) {\n    filePaths = [filePaths];\n  }\n  \n  filePaths.forEach(filePath => {\n    if (filePath && (filePath.startsWith('wxfile://') || filePath.includes('tmp'))) {\n      try {\n        uni.removeSavedFile({\n          filePath: filePath,\n          success: () => {\n            if (isDebug()) {\n              console.log('临时头像文件已清理:', filePath);\n            }\n          },\n          fail: (error) => {\n            if (isDebug()) {\n              console.warn('清理临时头像文件失败:', filePath, error);\n            }\n          }\n        });\n      } catch (error) {\n        if (isDebug()) {\n          console.warn('清理临时头像文件异常:', error);\n        }\n      }\n    }\n  });\n}\n"], "names": ["AssetUrlGenerator", "getBase64Avatar", "isDebug", "uni"], "mappings": ";;;;;AAwBO,SAAS,iBAAiB,OAAO,WAAW,SAAS,MAAM,oBAAoB,MAAM;AAE1F,QAAM,YAAYA,cAAiB,kBAAC,aAAa,MAAM,EAAE,QAAQ,UAAU,KAAI,CAAE;AAGjF,MAAI,CAAC,aAAa,mBAAmB;AACnC,WAAOC,0BAAe,gBAAC,MAAM,MAAM;AAAA,EACpC;AAED,SAAO,aAAaA,0BAAAA,gBAAgB,MAAM,MAAM;AAClD;AAgGO,SAAS,kBAAkB,WAAW,UAAU,IAAI;AACzD,QAAM,EAAE,QAAQ,mBAAmB,MAAM,oBAAoB,KAAM,IAAG;AAEtE,MAAIC,WAAO,QAAA,GAAI;AACbC,kBAAA,MAAA,MAAA,QAAA,iCAAa,kBAAkB,SAAS;AAAA,EACzC;AAGD,MAAI,oBAAoB,WAAW,QAAW;AAC5C,WAAO,iBAAiB,WAAW,QAAQ,iBAAiB;AAAA,EAC7D;AAGD,SAAO,iBAAiB,WAAW,MAAM,iBAAiB;AAC5D;AA6EO,SAAS,uBAAuB,WAAW,UAAU,IAAI;AAC9D,QAAM,EAAE,eAAe,OAAO,OAAM,IAAK;AAEzC,MAAI,CAAC,aAAa,CAACH,cAAiB,kBAAC,WAAW,SAAS,GAAG;AAC1D,WAAO,iBAAiB,eAAe,MAAM;AAAA,EAC9C;AAGD,MAAI,UAAU,WAAW,WAAW,KAAK,UAAU,WAAW,aAAa,GAAG;AAC5E,WAAO;AAAA,EACR;AAGD,MAAI,cAAc;AAChB,WAAOA,cAAiB,kBAAC,aAAa,SAAS;AAAA,EAChD;AAED,SAAO;AACT;AAMO,SAAS,mBAAmB,WAAW;AAC5C,MAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAY,CAAC,SAAS;AAAA,EACvB;AAED,YAAU,QAAQ,cAAY;AAC5B,QAAI,aAAa,SAAS,WAAW,WAAW,KAAK,SAAS,SAAS,KAAK,IAAI;AAC9E,UAAI;AACFG,sBAAAA,MAAI,gBAAgB;AAAA,UAClB;AAAA,UACA,SAAS,MAAM;AACb,gBAAID,WAAO,QAAA,GAAI;AACbC,4BAAY,MAAA,MAAA,OAAA,iCAAA,cAAc,QAAQ;AAAA,YACnC;AAAA,UACF;AAAA,UACD,MAAM,CAAC,UAAU;AACf,gBAAID,WAAO,QAAA,GAAI;AACbC,iFAAa,eAAe,UAAU,KAAK;AAAA,YAC5C;AAAA,UACF;AAAA,QACX,CAAS;AAAA,MACF,SAAQ,OAAO;AACd,YAAID,WAAO,QAAA,GAAI;AACbC,wBAAA,MAAA,MAAA,QAAA,iCAAa,eAAe,KAAK;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AAAA,EACL,CAAG;AACH;;;;;"}