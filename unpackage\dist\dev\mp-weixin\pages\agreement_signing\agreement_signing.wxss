/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-dd2a7f81:root {
  --primary-color:#3b7eeb;
  --primary-dark:#2c62c9;
  --primary-light: #e6f0ff;
  --success-color: #52c41a;
  --success-dark: #45a049;
  --text-primary: #333;
  --text-secondary: var(--text-secondary);
  --text-disabled: #999;
  --bg-primary: #fff;
  --bg-secondary: #f5f5f5;
  --bg-disabled: #87B0F2;
  --border-radius: 12rpx;
  --border-radius-large: 24rpx;
  --box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  --transition-duration: 0.3s;
}
.solution-confirm-container.data-v-dd2a7f81 {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 工单卡片样式 */
.work-order-card.data-v-dd2a7f81 {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius);
  padding: 30rpx;
  box-shadow: var(--box-shadow);
}
.work-order-header.data-v-dd2a7f81 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.work-order-title.data-v-dd2a7f81 {
  font-size: 30rpx;
  color: var(--text-primary);
  font-weight: bold;
}
.work-order-status.data-v-dd2a7f81 {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}
.status-label.data-v-dd2a7f81 {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  color: var(--bg-primary);
}
.status-processing.data-v-dd2a7f81 {
  background-color: #1890ff;
  color: var(--bg-primary);
}
.work-order-date.data-v-dd2a7f81 {
  font-size: 28rpx;
  color: var(--text-secondary);
}

/* 进度条样式 */
/* .progress-bar {
	background-color: var(--bg-primary);
	border-radius: var(--border-radius);
	padding: 30rpx;
	box-shadow: var(--box-shadow);
} */
.progress-steps.data-v-dd2a7f81 {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.progress-step.data-v-dd2a7f81 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}
.step-circle.data-v-dd2a7f81 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: var(--bg-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
  transition: all var(--transition-duration) ease;
}
.step-line.data-v-dd2a7f81 {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
  transition: all var(--transition-duration) ease;
}
.progress-step:last-child .step-line.data-v-dd2a7f81 {
  display: none;
}
.step-label.data-v-dd2a7f81 {
  font-size: 24rpx;
  color: var(--text-disabled);
  text-align: center;
  transition: all var(--transition-duration) ease;
}
.progress-step.active .step-circle.data-v-dd2a7f81 {
  background-color: var(--primary-color);
}
.progress-step.active .step-label.data-v-dd2a7f81 {
  color: var(--primary-color);
  font-weight: bold;
}
.progress-step.completed .step-circle.data-v-dd2a7f81 {
  background-color: var(--primary-color);
}
.step-line.completed.data-v-dd2a7f81 {
  background-color: var(--primary-color);
}
.progress-step.completed .step-label.data-v-dd2a7f81 {
  color: var(--primary-color);
}

/* 方案容器 */
.solutions-container.data-v-dd2a7f81 {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 方案卡片样式 */
.solution-card.data-v-dd2a7f81 {
  background-color: linear-gradient(135deg, white 0%, #f8fafc 100%);
  position: relative;
  overflow: hidden;
  border: 2rpx solid var(--primary-color);
  border-radius: var(--border-radius-large);
  transition: all var(--transition-duration) ease;
}
.solution-bg.data-v-dd2a7f81 {
  position: absolute;
  top: -40rpx;
  right: -40rpx;
  width: 200rpx;
  height: 200rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  opacity: 0.1;
}
.agreement-document.data-v-dd2a7f81 {
  text-align: center;
  padding: 60rpx 40rpx;
}
.agreement-document-icon.data-v-dd2a7f81 {
  width: 160rpx;
  height: 160rpx;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 40rpx;
  box-shadow: 0 16rpx 40rpx rgba(59, 126, 235, 0.3);
  position: relative;
  z-index: 2;
}
.agreement-document-icon .fas.data-v-dd2a7f81 {
  color: var(--bg-primary);
  font-size: 64rpx;
}
.agreement-document-title.data-v-dd2a7f81 {
  margin: 0 0 16rpx 0;
  color: var(--text-primary);
  font-size: 40rpx;
  font-weight: bold;
}
.agreement-document-tip.data-v-dd2a7f81 {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
}
.agreement-document-button.data-v-dd2a7f81 {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  text-align: center;
  font-weight: 500;
  box-shadow: rgba(0, 0, 0, 0.1) 0 4rpx 8rpx;
  height: 96rpx;
  white-space: nowrap;
  line-height: 1.5;
  padding: 24rpx 40rpx;
  border-radius: 16rpx;
  transition: all var(--transition-duration) ease;
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
  margin: 50rpx 110rpx 30rpx 110rpx;
  transform: scale(1);
  will-change: transform;
  background-color: var(--primary-color);
  color: var(--bg-primary);
}
.agreement-document-button .fas.data-v-dd2a7f81 {
  margin-right: 20rpx;
  font-size: 36rpx;
}
.solution-border.data-v-dd2a7f81 {
  width: 120rpx;
  height: 6rpx;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: 4rpx;
  margin: 40rpx auto;
}
.confirm-signing.data-v-dd2a7f81 {
  margin-top: 50rpx;
  /* 签名完成状态样式 */
}
.confirm-signing .sign.data-v-dd2a7f81 {
  margin-bottom: 40rpx;
}
.confirm-signing .sign .fas.data-v-dd2a7f81 {
  font-size: 80rpx;
  color: var(--primary-color);
  margin-bottom: 24rpx;
}
.confirm-signing .sign-title.data-v-dd2a7f81 {
  margin: 0 0 16rpx 0;
  color: var(--text-primary);
  font-size: 36rpx;
  font-weight: bold;
}
.confirm-signing .sign-tip.data-v-dd2a7f81 {
  margin: 0;
  font-size: 28rpx;
  color: var(--text-secondary);
}
.confirm-signing .agreement-check.data-v-dd2a7f81 {
  background-color: var(--bg-primary);
  border: 4rpx solid var(--primary-light);
  border-radius: var(--border-radius-large);
  padding: 36rpx;
  margin: 40rpx 0;
  transition: all var(--transition-duration) ease;
}
.confirm-signing .agreement-check.data-v-dd2a7f81:hover {
  box-shadow: 0 4rpx 20rpx rgba(59, 126, 235, 0.1);
}
.confirm-signing .agreement-check-signed.data-v-dd2a7f81 {
  border-color: var(--success-color) !important;
  background-color: #f6ffed !important;
}
.confirm-signing .agreement-check-signed.data-v-dd2a7f81:hover {
  box-shadow: 0 4rpx 20rpx rgba(82, 196, 26, 0.1);
}
.confirm-signing .agreement-content.data-v-dd2a7f81 {
  display: flex;
  text-align: left;
  flex-direction: column;
}
.confirm-signing .electronic-signature.data-v-dd2a7f81 {
  color: var(--text-secondary);
  font-size: 26rpx;
  line-height: 1.4;
  margin-left: 57rpx;
  display: block;
  transition: all var(--transition-duration) ease;
}
.confirm-signing .signature-completed.data-v-dd2a7f81 {
  color: var(--success-color) !important;
}
.data-v-dd2a7f81 .checklist-text {
  font-size: 30rpx !important;
  color: var(--text-primary) !important;
  font-weight: bold !important;
  margin-left: 10rpx !important;
  line-height: 1.5 !important;
  transition: all var(--transition-duration) ease !important;
}
.agreement-check-signed.data-v-dd2a7f81 .checklist-text {
  color: var(--success-color) !important;
}
.data-v-dd2a7f81 .checkbox__inner {
  width: 40rpx !important;
  height: 40rpx !important;
  border: 4rpx solid var(--primary-color) !important;
  border-radius: 8rpx !important;
  background-color: var(--bg-primary);
  z-index: 1 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  margin-right: 15rpx !important;
  transition: all var(--transition-duration) ease !important;
}
.data-v-dd2a7f81 .checkbox__inner-icon {
  height: 18rpx !important;
  width: 10rpx !important;
  border-right-width: 4rpx !important;
  border-bottom-width: 4rpx !important;
  opacity: 1 !important;
}
.data-v-dd2a7f81 .checklist-group .checklist-box.is--default.is-disable .checkbox__inner {
  background-color: #3b7eeb !important;
  border-color: #3b7eeb !important;
}
.agreement-check-signed.data-v-dd2a7f81 .checkbox__inner {
  border-color: var(--success-color) !important;
}

/* 底部按钮 */
.action-buttons.data-v-dd2a7f81 {
  padding: 20rpx 0;
}
.confirm-button.data-v-dd2a7f81 {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  transition: all var(--transition-duration) ease;
}
.confirm-button .fas.data-v-dd2a7f81 {
  margin-right: 20rpx;
  font-size: 36rpx;
  color: var(--bg-primary);
}
.confirm-button.data-v-dd2a7f81:active {
  transform: scale(0.98);
}
.disabled-btn.data-v-dd2a7f81 {
  background-color: var(--bg-disabled) !important;
  color: var(--bg-primary) !important;
}
.sign-btn.data-v-dd2a7f81 {
  background-color: var(--primary-color) !important;
  color: var(--bg-primary) !important;
}
.sign-btn.data-v-dd2a7f81:hover {
  background-color: var(--primary-dark) !important;
}
.success-btn.data-v-dd2a7f81 {
  background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%) !important;
  color: var(--bg-primary) !important;
}
.success-btn.data-v-dd2a7f81:hover {
  transform: none;
}
.success-btn.data-v-dd2a7f81:active {
  transform: none;
}