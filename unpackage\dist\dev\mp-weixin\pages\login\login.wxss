/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-e4e4508d {
  min-height: 100vh;
  background: transparent;
  display: flex;
  flex-direction: column;
  padding: 60rpx 40rpx 40rpx;
}
.logo-section.data-v-e4e4508d {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}
.logo.data-v-e4e4508d {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
}
.app-name.data-v-e4e4508d {
  font-size: 48rpx;
  font-weight: bold;
  color: #080808;
  margin-bottom: 20rpx;
}
.app-desc.data-v-e4e4508d {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.login-form.data-v-e4e4508d {
  margin-top: 120rpx;
}
.wechat-login-btn.data-v-e4e4508d {
  width: 100%;
  height: 100rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 40rpx;
  border: none;
}
.wechat-login-btn.data-v-e4e4508d:disabled {
  background-color: #cccccc;
}
.wechat-icon.data-v-e4e4508d {
  font-family: "iconfont";
  font-size: 36rpx;
  margin-right: 20rpx;
}
.other-login.data-v-e4e4508d {
  margin-top: 60rpx;
}
.divider.data-v-e4e4508d {
  text-align: center;
  margin-bottom: 40rpx;
}
.divider-text.data-v-e4e4508d {
  font-size: 24rpx;
  color: #999999;
  background: transparent;
  padding: 0 20rpx;
}
.phone-login-btn.data-v-e4e4508d {
  width: 100%;
  height: 88rpx;
  background-color: #f8f8f8;
  color: #333333;
  border: 2rpx solid #e5e5e5;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}
.phone-icon.data-v-e4e4508d {
  font-family: "iconfont";
  font-size: 32rpx;
  margin-right: 16rpx;
}
.agreement-section.data-v-e4e4508d {
  text-align: center;
}
.agreement-text.data-v-e4e4508d {
  display: flex;
  flex-wrap: wrap;
  font-size: 30rpx;
  color: #989898;
}
.link.data-v-e4e4508d {
  color: #6E9FEB;
}
.phone-login-popup.data-v-e4e4508d {
  background-color: #ffffff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 40rpx;
}
.popup-header.data-v-e4e4508d {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}
.popup-title.data-v-e4e4508d {
  font-size: 36rpx;
  font-weight: 500;
  color: #333333;
}
.popup-close.data-v-e4e4508d {
  font-size: 48rpx;
  color: #999999;
}
.phone-form .input-group.data-v-e4e4508d {
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
}
.phone-form .phone-input.data-v-e4e4508d,
.phone-form .code-input.data-v-e4e4508d {
  flex: 1;
  height: 88rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 30rpx;
}
.phone-form .send-code-btn.data-v-e4e4508d {
  width: 200rpx;
  height: 88rpx;
  background-color: #007aff;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin-left: 20rpx;
}
.phone-form .send-code-btn.data-v-e4e4508d:disabled {
  background-color: #cccccc;
}
.phone-form .phone-submit-btn.data-v-e4e4508d {
  width: 100%;
  height: 88rpx;
  background-color: #007aff;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 32rpx;
  margin-top: 20rpx;
}
.phone-form .phone-submit-btn.data-v-e4e4508d:disabled {
  background-color: #cccccc;
}
.loading-mask.data-v-e4e4508d {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.loading-content.data-v-e4e4508d {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
}