<template>
  <view class="work-order-detail-container">
    <!-- 案件基本信息 -->
    <view class="work-order-card">
      <view class="work-order-header">
        <view class="work-order-title">
          <text>调解案件号: </text>
          <text class="work-order-id">{{
            workOrderData.case_number
          }}</text>
        </view>
        <view class="work-order-status">
          <text
            class="status-label"
            :class="{ 'status-pending': workOrderData.case_status_cn === '已完成' }"
            >{{ workOrderData.case_status_cn }}</text
          >
        </view>
      </view>
      <view class="work-order-date"
        >发起日期: {{ workOrderData.initiate_date }}</view
      >
    </view>

    <!-- 进度条 -->
    <view class="progress-bar">
      <view class="progress-steps">
        <view class="progress-step active">
          <view class="step-circle">1</view>
          <view class="step-line active"></view>
          <view class="step-label">调解确认</view>
        </view>
        <view class="progress-step active">
          <view class="step-circle">2</view>
          <view class="step-line active"></view>
          <view class="step-label">方案确认</view>
        </view>
        <view class="progress-step active">
          <view class="step-circle">3</view>
          <view class="step-line active"></view>
          <view class="step-label">协议签署</view>
        </view>
        <view class="progress-step active">
          <view class="step-circle">4</view>
          <view class="step-label">完成</view>
        </view>
      </view>
    </view>

    <!-- 协议公证状态 -->
    <view class="info-section">
      <view class="section-title">协议公证状态</view>
      <view
        class="notarization-status-card notarization-pending"
        v-if="workOrderData.notarizationStatus === '未公证'"
        @click="handleNotarization"
      >
        <view class="notarization-status-header">
          <view class="notarization-status-icon">
            <i class="fas fa-certificate"></i>
          </view>
          <view class="notarization-status-info">
            <view class="notarization-status-title">未公证</view>
            <view class="notarization-status-desc">协议尚未进行公证，点击进行公证以获得法律强制执行力</view>
          </view>
          <view class="notarization-action-arrow">
            <i class="fas fa-chevron-right"></i>
          </view>
        </view>
        <view class="notarization-benefits">
          <view class="notarization-benefit-item">
            <i class="fas fa-shield-alt"></i>
            <text>法律强制执行力</text>
          </view>
          <view class="notarization-benefit-item">
            <i class="fas fa-gavel"></i>
            <text>免费办理</text>
          </view>
        </view>
      </view>
      <view
        class="notarization-status-card notarization-completed notarization-status-card-completed"
        id="notarizationCompletedCard"
        v-if="workOrderData.notarizationStatus === '已公证'"
      >
        <view class="notarization-status-header">
          <view class="notarization-status-icon">
            <i class="fas fa-certificate"></i>
          </view>
          <view class="notarization-status-info">
            <view class="notarization-status-title">已公证</view>
            <view class="notarization-status-desc"
              >协议已完成公证，具备法律强制执行力</view
            >
          </view>
          <view class="notarization-status-badge">
            <i class="fas fa-check-circle"></i>
          </view>
        </view>
        <view class="notarization-details">
          <view class="notarization-detail-row">
            <text class="notarization-detail-label">公证日期：</text>
            <text class="notarization-detail-value">2023-10-15</text>
          </view>
          <view class="notarization-detail-row">
            <text class="notarization-detail-label">公证机构：</text>
            <text class="notarization-detail-value">广东省佛山市公证处</text>
          </view>
          <view class="notarization-detail-row">
            <text class="notarization-detail-label">公证书编号：</text>
            <text class="notarization-detail-value">(2023)粤佛证字第12345号</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 调解信息 -->
    <view class="info-section">
      <view class="section-title">调解信息</view>
      <view class="info-item">
        <text class="info-label">债权人</text>
        <text class="info-value">{{
          workOrderData.creditor || "某银行信用卡中心"
        }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">债务类型</text>
        <text class="info-value">{{
          workOrderData.debtType || "信用卡欠款"
        }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">欠款金额</text>
        <text class="info-value"
          >¥{{ workOrderData.amount || "50,000.00" }}</text
        >
      </view>
      <view class="info-item">
        <text class="info-label">欠款时间</text>
        <text class="info-value"
          >{{ workOrderData.debtDate || "2022-05-01" }} 至今</text
        >
      </view>
    </view>
    <!-- 还款方案 -->
    <view class="info-section">
      <view class="section-title">还款方案</view>
      <view class="info-item">
        <text class="info-label">方案内容</text>
        <text class="info-value">{{
          workOrderData.creditor || "免除全部逾期利息，本金分24期等额还款"
        }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">还款金额</text>
        <text class="info-value"
          >¥{{ workOrderData.amount || "50,000.00" }}</text
        >
      </view>
      <view class="info-item">
        <text class="info-label">月还款额</text>
        <text class="info-value"
          >¥{{ workOrderData.monthlyRepayment || "2,083.33" }}</text
        >
      </view>
      <view class="info-item">
        <text class="info-label">减免金额</text>
        <text class="info-value reduction_amount"
          >¥{{ workOrderData.reduction_amount || "10,000.00" }}</text
        >
      </view>
    </view>
	<!-- 还款渠道 -->
	<view class="info-section">
		<view class="section-title">还款渠道</view>
		<view class="payment-memo-card">
			<view class="payment-memo-header">
				<i class="fas fa-exclamation-triangle payment-memo-icon"></i>
				<view class="payment-memo-title">重要提醒：还款备注信息</view>
			</view>
			<view class="payment-memo-content">{{workOrderData.paymentMemo}}</view>
			<view class="payment-memo-actions">
				<button class="btn btn-sm btn-copy btn-orange" @click="copyMemo" v-if="!isCopied">
					<i class="fas fa-copy"></i>一键复制备注
				</button>
				<button class="btn btn-sm btn-copy btn-orange copied" v-else>
					<i class="fas fa-check"></i>已复制
				</button>
			</view>
			<view class="payment-memo-tip">
				<i class="fas fa-info-circle"></i>
				还款时请务必填写上述备注信息，以便系统自动识别您的还款记录
			</view>
		</view>
		<!-- 银行转账渠道 -->
		<view class="payment-channel-card">
			<view class="payment-channel-header">
				<view class="payment-channel-icon" style="background-color: var(--primary-color);">
					<i class="fas fa-university"></i>
				</view>
				<view class="payment-channel-info">
					<view class="payment-channel-name">银行转账</view>
					<view class="payment-channel-desc">通过银行转账进行还款</view>
				</view>
			</view>
			<view class="payment-channel-details">
				<view class="payment-detail-row">
					<view class="payment-detail-label">收款人姓名</view>
					<view class="payment-detail-value">华泰民商事调解中心</view>
				</view>
				<view class="payment-detail-row">
					<view class="payment-detail-label">收款账号</view>
					<view class="payment-detail-value">6225 8812 3456 7890</view>
				</view>
				<view class="payment-detail-row">
					<view class="payment-detail-label">开户银行</view>
					<view class="payment-detail-value">中国银行佛山分行</view>
				</view>
			</view>
		</view>
		<!-- 微信支付渠道 -->
		<view class="payment-channel-card">
			<view class="payment-channel-header">
				<view class="payment-channel-icon" style="background-color: #09bb07;">
					<i class="fab fa-weixin"></i>
				</view>
				<view class="payment-channel-info">
					<view class="payment-channel-name">微信支付</view>
					<view class="payment-channel-desc">扫码使用微信支付进行还款</view>
				</view>
			</view>
			<view class="payment-channel-qrcode-container">
				<view class="payment-channel-qrcode">
					收款二维码
				</view>
			</view>
		</view>
		<!-- 支付宝支付渠道 -->
		<view class="payment-channel-card">
			<view class="payment-channel-header">
				<view class="payment-channel-icon" style="background-color: #00a0e9;">
					<i class="fab fa-alipay"></i>
				</view>
				<view class="payment-channel-info">
					<view class="payment-channel-name">支付宝</view>
					<view class="payment-channel-desc">扫码使用支付宝进行还款</view>
				</view>
			</view>
			<view class="payment-channel-qrcode-container">
				<view class="payment-channel-qrcode">
					收款二维码
				</view>
			</view>
		</view>
	</view>

    <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <button class="confirm-button" @click="handleViewProtocol"><i class="fas fa-file-contract"></i>查看协议</button>
      <button class="confirm-button svg-btn" @click="handleViewSvgFile"><i class="fas fa-image"></i>查看SVG文件</button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { api } from "@/utils/api.js";

// 接收参数
const caseNumber = ref("");

// 工单数据
const workOrderData = ref({});

const isCopied = ref(false);

// 生命周期钩子
onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options;

  if (options && options.case_number) {
    try {
      // URL解码处理中文参数
      caseNumber.value = decodeURIComponent(options.case_number);
      console.log("接收到工单ID:", caseNumber.value);
      fetchWorkOrderDetail(caseNumber.value);
    } catch (error) {
      console.error('case_number参数解码失败:', error);
      caseNumber.value = options.case_number;
      fetchWorkOrderDetail(caseNumber.value);
    }
  } else if (options && options.id) {
    try {
      // URL解码处理中文参数（兼容旧的id参数）
      caseNumber.value = decodeURIComponent(options.id);
      console.log("接收到工单ID:", caseNumber.value);
      fetchWorkOrderDetail(caseNumber.value);
    } catch (error) {
      console.error('id参数解码失败:', error);
      caseNumber.value = options.id;
      fetchWorkOrderDetail(caseNumber.value);
    }
  } else {
    // 默认使用模拟数据
    fetchWorkOrderDetail();
  }
});

// 获取调解确认数据
const fetchWorkOrderDetail = (id) => {
  if (id) {
    // 使用API获取数据
    uni.showLoading({
      title: "加载中...",
    });

    api.workOrder
      .getDetail(id)
      .then((res) => {
        uni.hideLoading();
        if (res.code === 0) {
          workOrderData.value = res.data;
        } else {
          uni.showToast({
            title: res.message || "获取调解确认失败",
            icon: "none",
          });
        }
      })
      .catch((err) => {
        uni.hideLoading();
        console.error("获取调解确认失败", err);
        uni.showToast({
          title: "获取调解确认失败",
          icon: "none",
        });
      });
  } else {
    // 使用模拟数据
    setTimeout(() => {
      console.log("调解确认数据已加载（模拟）");
    }, 500);
  }
};

// 查看协议
const handleViewProtocol = () => {
  // 构建文件预览参数
  const fileParams = {
    fileUrl: encodeURIComponent('http://192.168.1.101:10010/file_download/scheme/485?fullfilename=ae1bead568afd5d85b5a2e73785bb40c.docx'),
    fileType: encodeURIComponent('pdf'),
    fileName: encodeURIComponent('调解协议.pdf'),
    caseNumber: encodeURIComponent(caseNumber.value || '485')
  };

  // 构建URL参数字符串
  const paramsString = Object.entries(fileParams)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  uni.navigateTo({
    url: `/pages/protocol_preview/protocol_preview?${paramsString}`,
  });
};

// 查看SVG文件示例
const handleViewSvgFile = () => {
  // 构建SVG文件预览参数
  const fileParams = {
    fileUrl: encodeURIComponent('http://192.168.1.101:10010/file_download/svg/sample.svg'),
    fileType: encodeURIComponent('svg'),
    fileName: encodeURIComponent('示例图表.svg'),
    caseNumber: encodeURIComponent(caseNumber.value || '485')
  };

  // 构建URL参数字符串
  const paramsString = Object.entries(fileParams)
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  uni.navigateTo({
    url: `/pages/protocol_preview/protocol_preview?${paramsString}`,
  });
};

// 公证
const handleNotarization = () => {
	// 跳转至协议公证页面
	uni.navigateTo({
		url: "/pages/agreement_notarization/agreement_notarization",
	});
//   workOrderData.value.notarizationStatus = "已公证";
};

// 复制还款备注
const copyMemo = () => {
  uni.setClipboardData({
    data: workOrderData.value.paymentMemo,
  });
  uni.showToast({
    title: "还款备注信息已复制",
    icon: "success",
  });
  isCopied.value = true;
};

</script>

<style lang="scss" scoped>
:root {
	--text-color: #333;
	--text-secondary: #666;
	--success-color: #52c41a;
	--warning-color: #faad14;
	--primary-color: #3b7eeb;
	--border-color: #e0e0e0;
	--transition-normal: all 0.3s ease;
}
.work-order-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.work-order-card {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.work-order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.work-order-title {
  font-size: 30rpx;
  color: #333;
  font-weight: bold;
}

.work-order-status {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}

.status-label {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #52c41a;
  color: #fff;
}

.status-pending {
  background-color: #52c41a;
}

.work-order-date {
  font-size: 28rpx;
  color: #666;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  position: relative;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step-circle {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}

.step-line {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}

.progress-step:last-child .step-line {
  display: none;
}
.step-line.active {
  background-color: #2979ff;
}
.step-label {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}

.progress-step.active .step-circle {
  background-color: #2979ff;
}

.progress-step.active .step-label {
  color: #2979ff;
  font-weight: bold;
}

.info-section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16rpx;
  padding: 20rpx 0;
  color: #333;
  font-size: 32rpx;
}

.info-item:last-child {
  border-bottom: none;
}
.reduction_amount{
  color: #52c41a;
}
.info-label {
  font-weight: bold;
}
.notarization-pending {
  background: linear-gradient(135deg, #fff7e6 0%, #fff2d6 100%);
  border: 2rpx solid #ffd591;
  cursor: pointer;
}
.notarization-pending:hover {
  background: linear-gradient(135deg, #fff2d6 0%, #ffec99 100%);
  border-color: #ffa940;
  // transform: translateY(-2px);
  box-shadow: 0 8rpx 24rpx rgba(255, 140, 0, 0.15);
}
.notarization-status-card {
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  transition: all 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
  position: relative;
  overflow: hidden;
}
.notarization-status-card-completed{
  display: block; 
  opacity: 1;
  transform: translateY(0rpx);
}
.notarization-completed {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border: 2rpx solid #b7eb8f;
}
.notarization-details {
  margin-top: 24rpx;
  padding-top: 24rpx;
  border-top: 2rpx solid #d9f7be;
}
.notarization-detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12rpx;
  font-size: 26rpx;
  .notarization-detail-label {
    color: var(--text-secondary);
    font-weight: bold;
  }
  .notarization-detail-value {
    color: var(--text-color);
    font-weight: bold;
  }
}

.notarization-status-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
  .fa-certificate{
    color: #52c41a;
    font-size: 40rpx;
  }
  .fa-check-circle{
    color: #52c41a; 
    font-size: 32rpx;
  }
}
.notarization-pending .notarization-status-icon {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
}
// .notarization-pending:hover .notarization-action-arrow {
//   transform: translateX(3px);
// }
.notarization-status-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
  .fa-certificate{
    color: #ff8c00;
    font-size: 40rpx
  }
}
.notarization-completed .notarization-status-icon {
    background: linear-gradient(135deg, #f6ffed 0%, #b7eb8f 100%);
}
.notarization-status-info {
  flex: 1;
  min-width: 0;
}
.notarization-status-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
  color: var(--text-color);
}
.notarization-status-desc {
  font-size: 26rpx;
  color: var(--text-secondary);
  line-height: 1.4;
}
.notarization-action-arrow {
  margin-left: 16rpx;
  transition: transform 0.3s ease;
  .fa-chevron-right{
    color: #ff8c00;
    font-size: 32rpx;
  }
}
.notarization-benefits {
  display: flex;
  gap: 40rpx;
  margin-top: 16rpx;
}
.notarization-benefit-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: var(--text-secondary);
  .fa-shield-alt,.fa-gavel{
    color: #ff8c00;
    margin-right: 16rpx;
  }
}

.payment-memo-card {
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border: 2rpx solid #ffcc02;
    border-radius: 24rpx;
    padding: 36rpx;
    // margin-top: 40rpx;
    margin-bottom: 40rpx;
    margin-top: 0;
}
.payment-memo-header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
}
.payment-memo-icon {
    width: 48rpx;
    height: 48rpx;
    color: #ff8f00;
	align-items: center;
    display: flex;
    text-align: center;
}
.payment-memo-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #e65100;
}
.payment-memo-content {
    background-color: white;
    border-radius: 16rpx;
    padding: 24rpx;
    font-size: 28rpx;
    color: var(--text-color);
    font-weight: bold;
    word-break: break-all;
    line-height: 1.4;
    border: 2rpx solid rgba(255, 152, 0, 0.2);
}
.payment-memo-actions {
    margin-top: 24rpx;
    text-align: right;
}
.payment-memo-tip{
  display: flex;
	margin-top: 20rpx;
	font-size: 24rpx;
	color: #e65100;
	line-height: 1.4;
  .fa-info-circle{
    margin-right: 10rpx;
  }
}
.btn-copy.btn-orange {
    background-color: #ff8f00;
    border-color: #ff8f00;
    color: white;
}
.btn-copy.btn-orange:hover {
    background-color: #f57700;
    border-color: #f57700;
}

.btn-copy.btn-orange.copied {
    background-color: var(--success-color);
    border-color: var(--success-color);
    .fa-check{
      margin-left: 20rpx;
    }
}
.btn-copy {
    display: inline-flex;
    align-items: center;
    gap: 10rpx;
}
.btn-sm {
    padding: 16rpx 32rpx;
    font-size: 28rpx;
    border-radius: 12rpx;
    height: 80rpx;
}
.payment-channel-card {
    background: white;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 24rpx;
    border: 2rpx solid var(--border-color);
    transition: var(--transition-normal);
	
	.payment-channel-header {
		display: flex;
		align-items: center;
		margin-bottom: 30rpx;
	}
	.payment-channel-icon {
		width: 90rpx;
		height: 90rpx;
		border-radius: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 30rpx;
		flex-shrink: 0;
    .fa-university,.fa-weixin,.fa-alipay{
      color: white;
      font-size: 40rpx;
    }
	}
	.payment-channel-info {
		flex: 1;
	}
  .payment-channel-qrcode-container{
    text-align: center;
    margin-top: 15px;
    .payment-channel-qrcode{
      width: 80px;
      height: 80px;
      background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
      border: 2px dashed #ccc;
      border-radius: 8px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #999;
    }
  }
	.payment-channel-name {
		font-size: 32rpx;
		font-weight: bold;
		color: var(--text-color);
		margin-bottom: 8rpx;
	}
	.payment-channel-desc {
		font-size: 26rpx;
		color: var(--text-secondary);
	}
	.payment-channel-details {
		padding-top: 30rpx;
		border-top: 2rpx solid rgba(0, 0, 0, 0.06);
	}
	.payment-detail-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
		.payment-detail-label {
			font-size: 28rpx;
			color: var(--text-secondary);
			flex-shrink: 0;
			margin-right: 30rpx;
		}
		.payment-detail-value {
			font-size: 28rpx;
			color: var(--text-color);
			font-weight: bold;
			flex: 1;
			text-align: right;
			word-break: break-all;
		}
	}
}

// 底部按钮
.action-buttons {
  margin-top: 40rpx;
  padding: 20rpx 0;
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.confirm-button {
	width: 100%;
	height: 90rpx;
	line-height: 90rpx;
	text-align: center;
	border-radius: 16rpx;
	font-size: 32rpx;
	background-color: #2979ff;
	color: #fff;
	display: flex;
	align-items: center;
	justify-content: center;
	.fas{
		margin-right: 10rpx;
	}

	&.svg-btn {
		background-color: #52c41a;

		&:active {
			background-color: #45a049;
		}
	}
}
</style> 