"use strict";
const common_vendor = require("../common/vendor.js");
const config_env = require("../config/env.js");
const config_assets = require("../config/assets.js");
class PerformanceMonitor {
  constructor() {
    this.metrics = /* @__PURE__ */ new Map();
    this.observers = [];
    this.isEnabled = config_env.isDebug();
  }
  /**
   * 开始性能测量
   * @param {string} name 测量名称
   */
  start(name) {
    if (!this.isEnabled)
      return;
    this.metrics.set(name, {
      startTime: Date.now(),
      startMemory: this.getMemoryUsage()
    });
  }
  /**
   * 结束性能测量
   * @param {string} name 测量名称
   */
  end(name) {
    if (!this.isEnabled)
      return;
    const metric = this.metrics.get(name);
    if (!metric)
      return;
    const endTime = Date.now();
    const endMemory = this.getMemoryUsage();
    const result = {
      name,
      duration: endTime - metric.startTime,
      memoryDelta: endMemory - metric.startMemory,
      timestamp: endTime
    };
    common_vendor.index.__f__("log", "at utils/performance-optimizer.js:48", `[性能] ${name}: ${result.duration}ms, 内存变化: ${result.memoryDelta}KB`);
    this.observers.forEach((observer) => observer(result));
    this.metrics.delete(name);
    return result;
  }
  /**
   * 获取内存使用情况（简化版本）
   */
  getMemoryUsage() {
    return 0;
  }
  /**
   * 添加性能观察者
   * @param {Function} observer 观察者函数
   */
  addObserver(observer) {
    this.observers.push(observer);
  }
  /**
   * 移除性能观察者
   * @param {Function} observer 观察者函数
   */
  removeObserver(observer) {
    const index = this.observers.indexOf(observer);
    if (index > -1) {
      this.observers.splice(index, 1);
    }
  }
}
class ResourceOptimizer {
  constructor() {
    this.loadQueue = [];
    this.loadingResources = /* @__PURE__ */ new Set();
    this.loadedResources = /* @__PURE__ */ new Set();
    this.maxConcurrent = 3;
  }
  /**
   * 预加载关键资源
   */
  async preloadCriticalResources() {
    const monitor = performanceMonitor;
    monitor.start("preload-critical");
    try {
      const result = await config_assets.assetPreloader.preloadCriticalAssets();
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/performance-optimizer.js:109", "关键资源预加载完成:", result);
      }
      monitor.end("preload-critical");
      return result;
    } catch (error) {
      monitor.end("preload-critical");
      common_vendor.index.__f__("error", "at utils/performance-optimizer.js:116", "关键资源预加载失败:", error);
      return { success: [], failed: [], total: 0, successRate: 0 };
    }
  }
  /**
   * 延迟加载非关键资源
   * @param {Array} resources 资源列表
   */
  async lazyLoadResources(resources) {
    if (!Array.isArray(resources))
      return;
    const monitor = performanceMonitor;
    monitor.start("lazy-load");
    try {
      const batches = this.createBatches(resources, this.maxConcurrent);
      const results = [];
      for (const batch of batches) {
        const batchResults = await Promise.allSettled(
          batch.map((resource) => this.loadResource(resource))
        );
        results.push(...batchResults);
      }
      monitor.end("lazy-load");
      return results;
    } catch (error) {
      monitor.end("lazy-load");
      common_vendor.index.__f__("error", "at utils/performance-optimizer.js:147", "延迟加载失败:", error);
      return [];
    }
  }
  /**
   * 创建资源加载批次
   * @param {Array} resources 资源列表
   * @param {number} batchSize 批次大小
   */
  createBatches(resources, batchSize) {
    const batches = [];
    for (let i = 0; i < resources.length; i += batchSize) {
      batches.push(resources.slice(i, i + batchSize));
    }
    return batches;
  }
  /**
   * 加载单个资源
   * @param {string} resource 资源URL
   */
  loadResource(resource) {
    return new Promise((resolve, reject) => {
      if (this.loadedResources.has(resource)) {
        resolve(resource);
        return;
      }
      if (this.loadingResources.has(resource)) {
        const checkLoaded = () => {
          if (this.loadedResources.has(resource)) {
            resolve(resource);
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
        return;
      }
      this.loadingResources.add(resource);
      common_vendor.index.getImageInfo({
        src: resource,
        success: () => {
          this.loadingResources.delete(resource);
          this.loadedResources.add(resource);
          resolve(resource);
        },
        fail: (error) => {
          this.loadingResources.delete(resource);
          reject(error);
        }
      });
    });
  }
}
class MemoryOptimizer {
  constructor() {
    this.cleanupTasks = [];
    this.cleanupInterval = null;
  }
  /**
   * 启动内存清理
   */
  startCleanup() {
    if (this.cleanupInterval)
      return;
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1e3);
  }
  /**
   * 停止内存清理
   */
  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
  /**
   * 执行内存清理
   */
  performCleanup() {
    const monitor = performanceMonitor;
    monitor.start("memory-cleanup");
    try {
      this.cleanupTasks.forEach((task) => {
        try {
          task();
        } catch (error) {
          common_vendor.index.__f__("error", "at utils/performance-optimizer.js:251", "清理任务执行失败:", error);
        }
      });
      this.clearExpiredCache();
      if (config_env.isDebug()) {
        common_vendor.index.__f__("log", "at utils/performance-optimizer.js:259", "内存清理完成");
      }
      monitor.end("memory-cleanup");
    } catch (error) {
      monitor.end("memory-cleanup");
      common_vendor.index.__f__("error", "at utils/performance-optimizer.js:265", "内存清理失败:", error);
    }
  }
  /**
   * 添加清理任务
   * @param {Function} task 清理任务函数
   */
  addCleanupTask(task) {
    if (typeof task === "function") {
      this.cleanupTasks.push(task);
    }
  }
  /**
   * 移除清理任务
   * @param {Function} task 清理任务函数
   */
  removeCleanupTask(task) {
    const index = this.cleanupTasks.indexOf(task);
    if (index > -1) {
      this.cleanupTasks.splice(index, 1);
    }
  }
  /**
   * 清理过期缓存
   */
  clearExpiredCache() {
    try {
      const now = Date.now();
      const keys = ["token_expire_time", "login_time"];
      keys.forEach((key) => {
        try {
          const expireTime = common_vendor.index.getStorageSync(key);
          if (expireTime && now > expireTime) {
            common_vendor.index.removeStorageSync(key);
          }
        } catch (error) {
        }
      });
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/performance-optimizer.js:309", "清理过期缓存失败:", error);
    }
  }
}
const performanceMonitor = new PerformanceMonitor();
const resourceOptimizer = new ResourceOptimizer();
const memoryOptimizer = new MemoryOptimizer();
function initPerformanceOptimization() {
  if (config_env.isDebug()) {
    common_vendor.index.__f__("log", "at utils/performance-optimizer.js:324", "初始化性能优化...");
  }
  memoryOptimizer.startCleanup();
  resourceOptimizer.preloadCriticalResources().catch((error) => {
    common_vendor.index.__f__("error", "at utils/performance-optimizer.js:332", "关键资源预加载失败:", error);
  });
  memoryOptimizer.addCleanupTask(() => {
  });
}
function destroyPerformanceOptimization() {
  memoryOptimizer.stopCleanup();
  if (config_env.isDebug()) {
    common_vendor.index.__f__("log", "at utils/performance-optimizer.js:353", "性能优化已销毁");
  }
}
exports.destroyPerformanceOptimization = destroyPerformanceOptimization;
exports.initPerformanceOptimization = initPerformanceOptimization;
exports.memoryOptimizer = memoryOptimizer;
exports.performanceMonitor = performanceMonitor;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/performance-optimizer.js.map
