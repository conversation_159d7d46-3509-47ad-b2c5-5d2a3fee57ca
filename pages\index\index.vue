<template>
	<view class="index-container">
		<open-data-list type="groupMembers" members="{{members}}">
			<view class="userinfo" slot:index>
				<open-data-item class="avatar " type="userAvatar" index="{{index}}" />
				<open-data-item class="" type="userNickName" index="{{index}}" />
			</view>
		</open-data-list>
		<view class="welcome-banner-minimal">
			<view class="banner-minimal-content">
				<view class="banner-minimal-left">
					<view class="banner-minimal-lines">
						<view class="line line-1"></view>
						<view class="line line-2"></view>
						<view class="line line-3"></view>
					</view>
				</view>
				<view class="banner-minimal-right">
					<h1 class="banner-minimal-title">华泰民商事调解中心</h1>
					<p class="banner-minimal-subtitle">为您服务</p>
				</view>
			</view>
			<view class="banner-minimal-decoration"></view>
		</view>
		<view class="grid-container">
			<view class="grid-item" @click="navigateTo('mediationQuery')">
				<view class="grid-icon">
					<i class="fas fa-search"></i>
				</view>
				<view class="grid-item-content">
					<view class="grid-title">调解查询</view>
					<view class="grid-subtitle" v-if="gridData.mediationQuery">{{gridData.mediationQuery.subtitle}}</view>
				</view>
				<i class="fas fa-chevron-right feature-arrow"></i>
			</view>

			<view class="grid-item" @click="navigateTo('realCase')">
				<view class="grid-icon">
					<i class="fas fa-file-alt"></i>
				</view>
				<view class="grid-item-content">
					<view class="grid-title">案例展示</view>
					<view class="grid-subtitle" v-if="gridData.realCase">{{gridData.realCase.subtitle}}</view>
				</view>
				<i class="fas fa-chevron-right feature-arrow"></i>
			</view>
			
			<view class="grid-item" @click="navigateTo('mediationComplaint')">
				<view class="grid-icon">
					<i class="fas fa-comments"></i>
				</view>
				<view class="grid-item-content">
					<view class="grid-title">投诉建议</view>
					<view class="grid-subtitle" v-if="gridData.mediationComplaint">{{gridData.mediationComplaint.subtitle}}</view>
				</view>
				<i class="fas fa-chevron-right feature-arrow"></i>
			</view>
		</view>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
// 导入API工具类
import { api } from '@/utils/api.js';
/* 
登录成功后：调用 api.auth.setToken(token) 保存token
页面跳转传参：可通过URL参数传递token，系统会自动识别并保存
退出登录：调用 api.auth.clearToken() 清除所有认证信息


// 设置token
api.auth.setToken('your_token_here');

// 获取token（支持多来源自动获取）
const token = api.auth.getToken();

// 检查token是否存在
const hasToken = api.auth.hasToken();

// 清除token
api.auth.clearToken(); */
// 响应式数据
const gridData = ref({
	mediationQuery: null,
	realCase: null,
	mediationComplaint: null,
});

// 页面加载时执行
onMounted(() => {
	fetchGridData();
});


// 获取网格数据
function fetchGridData() {
	// 这里应该调用实际的API获取数据
	// 暂时使用模拟数据
	setTimeout(() => {
		gridData.value = {
			mediationQuery: {
				subtitle: '查看您的调解进度和历史记录'
			},
			realCase: {
				subtitle: '了解成功调解案例和经验'
			},
			mediationComplaint: {
				subtitle: '提交意见反馈和服务投诉'
			},
		};
	}, 500);
}

// 页面导航
function navigateTo(type) {
	const routes = {
		mediationQuery: '/pages/mediation_query/mediation_query',
		realCase: '/pages/real_case/real_case',
		mediationComplaint: '/pages/mediation_complaint/mediation_complaint',
	};
	
	if (routes[type]) {
		uni.navigateTo({
			url: routes[type]
		});
	}else{
		uni.showToast({
			title: '功能建设中...',
			icon: 'none'
		});
	}
}
</script>

<style lang="scss" scoped>
	:root {
		--primary-color: #3b7eeb;
		--primary-light: #e6f0ff;
		--primary-dark: #2c62c9;
		--secondary-color: #f5f7fa;
		--success-color: #52c41a;
		--warning-color: #faad14;
		--danger-color: #f5222d;
		--info-color: #1890ff;
		--text-color: #333333;
		--text-secondary: #666666;
		--text-light: #999999;
		--border-color: #e8e8e8;
		--background-color: #f5f7fa;
		--card-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		--transition-normal: all 0.3s ease;
	}
	.index-container {
		height: calc(100% - 94px);
		overflow-y: auto;
		padding: 30rpx;
		padding-bottom: 140rpx;
		background-color: #f8fafc;
	}
	
	.welcome-banner-minimal {
		background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
		border-radius: 0;
		margin: -30rpx -30rpx 40rpx -30rpx;
		position: relative;
		overflow: hidden;
		padding: 64rpx 40rpx;
	}
	.banner-minimal-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		z-index: 2;
	}
	.banner-minimal-left {
		flex-shrink: 0;
	}
	.banner-minimal-lines {
		width: 120rpx;
		height: 120rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		gap: 12rpx;
	}
	.line {
		height: 6rpx;
		background: rgba(255, 255, 255, 0.8);
		border-radius: 4rpx;
		// transition: var(--transition-normal);
		// transition: all 0.3s ease;
		transition-property: all;
		transition-duration: 3s;
		transition-timing-function: ease-in-out;
	}
	.line-1 {
		width: 80rpx;
		// animation: line-expand-1 3s ease-in-out infinite;
		animation-name: line-expand-1;
		animation-duration: 3s;
		animation-timing-function: ease-in-out;
		animation-iteration-count: infinite;
	}
	.line-2 {
		width: 110rpx;
		animation-name: line-expand-2;
		animation-duration: 3s;
		animation-timing-function: ease-in-out;
		animation-iteration-count: infinite;
		animation-delay: 0.5s;
	}
	.line-3 {
		width: 60rpx;
		animation-name: line-expand-3;
		animation-duration: 3s;
		animation-timing-function: ease-in-out;
		animation-iteration-count: infinite;
		animation-delay: 1s;
	}
	.banner-minimal-right {
		flex: 1;
		margin-left: 48rpx;
		text-align: left;
	}
	.banner-minimal-title {
		color: white;
		font-size: 40rpx;
		font-weight: 600;
		margin: 0 0 12rpx 0;
		line-height: 1.3;
		letter-spacing: 1rpx;
	}
	.banner-minimal-subtitle {
		color: rgba(255, 255, 255, 0.9);
		font-size: 28rpx;
		margin: 0;
		letter-spacing: 4rpx;
		font-weight: 300;
	}
	.banner-minimal-decoration {
		position: absolute;
		top: -40rpx;
		right: -40rpx;
		width: 240rpx;
		height: 240rpx;
		background: rgba(255, 255, 255, 0.08);
		border-radius: 50%;
		animation: float 6s ease-in-out infinite;
	}
	.grid-container {
		margin-top: 40rpx;
	}
	
	.grid-row {
		display: flex;
		margin-bottom: 30rpx;
	}
	
	.grid-item {
		background-color: white;
		border-radius: 24rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		display: flex;
		align-items: center;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
		transition: var(--transition-normal);
		cursor: pointer;
		border: 2rpx solid rgba(0, 0, 0, 0.03);
	}
	.grid-item:hover{
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		transform: translateY(-4rpx);
	}
	.feature-arrow {
		color: var(--text-light);
		font-size: 28rpx;
		flex-shrink: 0;
	}
	/* // :first-child 是第一个元素
	.grid-item:first-child {
		margin-left: 0;
	}
	// :last-child 是最后一个元素
	.grid-item:last-child {
		margin-right: 0;
	} */

	.grid-icon {
		width: 90rpx;
		height: 90rpx;
		border-radius: 24rpx;
		background-color: var(--primary-light);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 30rpx;
		flex-shrink: 0;
	
		.fas {
			font-size: 40rpx;
			color: var(--primary-color);
		}
	}

	.grid-item-content {
		flex: 1;
	}
	
	.grid-title {
		font-size: 32rpx;
		font-weight: 500;
		color: var(--text-color);
		margin-bottom: 8rpx;
	}
	
	.grid-subtitle {
		font-size: 26rpx;
		color: var(--text-secondary);
		line-height: 1.3;
	}

	/* 电子签名按钮样式 */
	.sign-button {
		margin: 20rpx auto;
		width: 300rpx;
		height: 80rpx;
		line-height: 80rpx;
		text-align: center;
		background-color: #2979ff;
		color: #fff;
		border-radius: 40rpx;
		font-size: 28rpx;
		box-shadow: 0 2rpx 10rpx rgba(41, 121, 255, 0.3);
	}
</style>