"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_api = require("../../utils/api.js");
const _sfc_main = {
  __name: "agreement_notarization",
  setup(__props) {
    const orderId = common_vendor.ref("");
    const workOrderData = common_vendor.ref({
      id: "MED20230001",
      status: "已完成",
      createDate: "2023-11-01",
      creditor: "某银行信用卡中心",
      amount: "50,000.00",
      reduction_amount: "10,000.00",
      monthlyRepayment: "2,083.33",
      notarizationStatus: "未公证",
      paymentMemo: "MED20230003_张先生_123456_华泰民商事调解中心"
    });
    common_vendor.ref(false);
    const notarizationChoice = common_vendor.ref("apply");
    const handleSelectChoice = async (choice) => {
      notarizationChoice.value = choice;
      const buttonName = choice === "apply" ? "选择申请协议公证" : "选择暂不公证";
      await recordOperationLog("协议公证", buttonName);
    };
    const handleConfirmChoice = async () => {
      if (notarizationChoice.value === "apply") {
        await handleApplyNotarization();
      } else {
        await handleSkipNotarization();
      }
    };
    const handleApplyNotarization = async () => {
      await recordOperationLog("协议公证", "确认公证申请");
      common_vendor.index.showModal({
        title: "确认申请",
        content: "确认申请免费协议公证服务？公证完成后协议将具有法律强制执行效力。",
        confirmText: "确认申请",
        cancelText: "取消",
        success: async (res) => {
          var _a, _b, _c;
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "申请中..."
            });
            try {
              const token = common_vendor.index.getStorageSync("token") || ((_c = (_b = (_a = getCurrentPages()[0]) == null ? void 0 : _a.$page) == null ? void 0 : _b.options) == null ? void 0 : _c.token);
              if (!token) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "请先登录",
                  icon: "none"
                });
                return;
              }
              const result = await utils_api.api.notarization.apply({
                workOrderId: orderId.value || workOrderData.value.id
              });
              common_vendor.index.hideLoading();
              if (result.code === 0) {
                common_vendor.index.showToast({
                  title: "申请成功",
                  icon: "success"
                });
                workOrderData.value.notarizationStatus = "申请中";
                setTimeout(() => {
                  common_vendor.index.navigateTo({
                    url: `/pages/notarization_result/notarization_result?id=${orderId.value}&type=apply`
                  });
                }, 1500);
              } else {
                common_vendor.index.showToast({
                  title: result.message || "申请失败",
                  icon: "none"
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/agreement_notarization/agreement_notarization.vue:375", "申请公证失败:", error);
              common_vendor.index.showToast({
                title: "申请失败，请稍后重试",
                icon: "none"
              });
            }
          }
        }
      });
    };
    const handleSkipNotarization = async () => {
      await recordOperationLog("协议公证", "确认暂不公证");
      common_vendor.index.showModal({
        title: "确认选择",
        content: "确认暂不进行协议公证？您可以在协议签署完成后随时申请公证服务。",
        confirmText: "确认",
        cancelText: "取消",
        success: async (res) => {
          var _a, _b, _c;
          if (res.confirm) {
            common_vendor.index.showLoading({
              title: "处理中..."
            });
            try {
              const token = common_vendor.index.getStorageSync("token") || ((_c = (_b = (_a = getCurrentPages()[0]) == null ? void 0 : _a.$page) == null ? void 0 : _b.options) == null ? void 0 : _c.token);
              if (!token) {
                common_vendor.index.hideLoading();
                common_vendor.index.showToast({
                  title: "请先登录",
                  icon: "none"
                });
                return;
              }
              const result = await utils_api.api.notarization.skip({
                workOrderId: orderId.value || workOrderData.value.id
              });
              common_vendor.index.hideLoading();
              if (result.code === 0) {
                common_vendor.index.showToast({
                  title: "已确认",
                  icon: "success"
                });
                workOrderData.value.notarizationStatus = "暂不公证";
                setTimeout(() => {
                  common_vendor.index.navigateTo({
                    url: `/pages/case_completed/case_completed?id=${orderId.value}`
                  });
                }, 1500);
              } else {
                common_vendor.index.showToast({
                  title: result.message || "操作失败",
                  icon: "none"
                });
              }
            } catch (error) {
              common_vendor.index.hideLoading();
              common_vendor.index.__f__("error", "at pages/agreement_notarization/agreement_notarization.vue:444", "跳过公证失败:", error);
              common_vendor.index.showToast({
                title: "操作失败，请稍后重试",
                icon: "none"
              });
            }
          }
        }
      });
    };
    common_vendor.onMounted(() => {
      var _a;
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = (_a = currentPage.$page) == null ? void 0 : _a.options;
      if (options && options.id) {
        orderId.value = options.id;
        common_vendor.index.__f__("log", "at pages/agreement_notarization/agreement_notarization.vue:464", "接收到工单ID:", orderId.value);
        fetchWorkOrderDetail(orderId.value);
      } else {
        fetchWorkOrderDetail();
      }
    });
    const fetchWorkOrderDetail = (id) => {
      if (id) {
        common_vendor.index.showLoading({
          title: "加载中..."
        });
        utils_api.api.workOrder.getDetail(id).then((res) => {
          common_vendor.index.hideLoading();
          if (res.code === 0) {
            workOrderData.value = res.data;
          } else {
            common_vendor.index.showToast({
              title: res.message || "获取调解确认失败",
              icon: "none"
            });
          }
        }).catch((err) => {
          common_vendor.index.hideLoading();
          common_vendor.index.__f__("error", "at pages/agreement_notarization/agreement_notarization.vue:495", "获取调解确认失败", err);
          common_vendor.index.showToast({
            title: "获取调解确认失败",
            icon: "none"
          });
        });
      } else {
        setTimeout(() => {
          common_vendor.index.__f__("log", "at pages/agreement_notarization/agreement_notarization.vue:504", "调解确认数据已加载（模拟）");
        }, 500);
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(workOrderData.value.id || "MED20230001"),
        b: common_vendor.t(workOrderData.value.status || "已完成"),
        c: workOrderData.value.status === "已完成" ? 1 : "",
        d: common_vendor.t(workOrderData.value.createDate || "2023-11-01"),
        e: notarizationChoice.value === "apply"
      }, notarizationChoice.value === "apply" ? {} : {}, {
        f: notarizationChoice.value === "apply" ? 1 : "",
        g: notarizationChoice.value === "apply" ? 1 : "",
        h: common_vendor.o(($event) => handleSelectChoice("apply")),
        i: notarizationChoice.value === "skip"
      }, notarizationChoice.value === "skip" ? {} : {}, {
        j: notarizationChoice.value === "skip" ? 1 : "",
        k: notarizationChoice.value === "skip" ? 1 : "",
        l: common_vendor.o(($event) => handleSelectChoice("skip")),
        m: common_vendor.t(notarizationChoice.value === "apply" ? "确认公证申请" : "确认暂不公证"),
        n: common_vendor.o(handleConfirmChoice)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2eac678c"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/agreement_notarization/agreement_notarization.js.map
