@echo off
chcp 65001 >nul
echo ===============================================
echo 🔧 修复uni-popup编译错误（保留其他uni-ui组件）
echo ===============================================
echo.

cd /d "%~dp0.."

echo 🔍 当前项目目录: %CD%
echo.

echo ℹ️ 检测到项目中使用了以下uni-ui组件：
echo   - uni-easyinput（输入框组件）
echo   - uni-data-select（数据选择组件）
echo   - 其他可能的uni-ui组件
echo.
echo 📋 本脚本将：
echo   ✅ 保留uni-ui依赖和配置
echo   ✅ 只清理uni-popup相关的编译缓存
echo   ✅ 修复pages.json中的easycom配置
echo.

set /p confirm=是否继续修复？(Y/N): 
if /i not "%confirm%"=="Y" (
    echo 操作已取消
    pause
    exit /b
)

echo.
echo 🚀 开始修复...
echo.

echo 📁 第一步：清理uni-popup相关编译缓存...
if exist "unpackage\dist\dev\mp-weixin\node-modules\@dcloudio\uni-ui\lib\uni-popup" (
    rmdir /s /q "unpackage\dist\dev\mp-weixin\node-modules\@dcloudio\uni-ui\lib\uni-popup" 2>nul
    echo ✅ 已删除开发环境uni-popup编译缓存
) else (
    echo ℹ️ 开发环境uni-popup编译缓存不存在
)

if exist "unpackage\dist\build\mp-weixin\node-modules\@dcloudio\uni-ui\lib\uni-popup" (
    rmdir /s /q "unpackage\dist\build\mp-weixin\node-modules\@dcloudio\uni-ui\lib\uni-popup" 2>nul
    echo ✅ 已删除生产环境uni-popup编译缓存
) else (
    echo ℹ️ 生产环境uni-popup编译缓存不存在
)

echo.
echo 📄 第二步：检查并修复pages.json配置...
findstr /c:"uni-ui" pages.json >nul 2>&1
if %errorlevel%==0 (
    echo ✅ pages.json中有uni-ui配置，这是正确的
) else (
    echo ❌ pages.json中缺少uni-ui配置，需要添加
    echo.
    echo 请在pages.json的easycom节点中添加以下配置：
    echo "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    echo.
    echo 完整的easycom配置示例：
    echo {
    echo   "easycom": {
    echo     "autoscan": true,
    echo     "custom": {
    echo       "^miliu-(.*)": "@/components/miliu-$1/miliu-$1.vue",
    echo       "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
    echo     }
    echo   }
    echo }
    echo.
    pause
)

echo.
echo 📦 第三步：检查package.json依赖...
findstr /c:"@dcloudio/uni-ui" package.json >nul 2>&1
if %errorlevel%==0 (
    echo ✅ package.json中有uni-ui依赖，这是正确的
) else (
    echo ❌ package.json中缺少uni-ui依赖，需要安装
    echo.
    echo 请执行以下命令安装uni-ui：
    echo npm install @dcloudio/uni-ui
    echo.
    pause
)

echo.
echo 🔍 第四步：检查代码中的uni-popup引用...
findstr /s /i "uni-popup" pages\*.vue >nul 2>&1
if %errorlevel%==0 (
    echo ❌ 发现代码中仍有uni-popup引用
    echo 请检查以下文件并移除uni-popup相关代码：
    findstr /s /i /n "uni-popup" pages\*.vue
    echo.
    echo 建议使用项目中的simple-popup组件替代uni-popup
    pause
) else (
    echo ✅ 代码中已无uni-popup引用
)

echo.
echo 🗂️ 第五步：清理HBuilderX缓存...
if exist ".hbuilderx" (
    rmdir /s /q ".hbuilderx" 2>nul
    echo ✅ HBuilderX缓存已清理
) else (
    echo ℹ️ .hbuilderx目录不存在
)

echo.
echo ===============================================
echo ✨ uni-popup问题修复完成！
echo ===============================================
echo.
echo 📋 接下来请按顺序执行：
echo.
echo 1️⃣ 关闭HBuilderX
echo.
echo 2️⃣ 重新打开HBuilderX
echo.
echo 3️⃣ 在HBuilderX中执行"运行" → "清理项目"
echo.
echo 4️⃣ 重新编译到微信小程序
echo.
echo 5️⃣ 验证结果：
echo    ✅ uni-easyinput组件应该正常工作
echo    ✅ 不应该再有uni-popup编译错误
echo    ✅ 其他uni-ui组件功能正常
echo.
echo ⚠️ 如果仍有问题：
echo   - 检查是否需要手动修复pages.json配置
echo   - 确认package.json中有@dcloudio/uni-ui依赖
echo   - 检查是否有其他页面使用了uni-popup
echo.
echo ===============================================
pause
