"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "canvas-autograph",
  props: {
    modelValue: Boolean
  },
  emits: ["update:modelValue", "complete"],
  setup(__props, { emit: __emit }) {
    const emits = __emit;
    const props = __props;
    const _this = common_vendor.getCurrentInstance();
    common_vendor.watch(() => props.modelValue, (e) => {
    }, {
      immediate: true
      // 是否默认执行一次  默认为false
    });
    let points = common_vendor.reactive([]);
    let canvaCtx = common_vendor.reactive(common_vendor.index.createCanvasContext("mycanvas", _this));
    canvaCtx.lineWidth = 4;
    canvaCtx.lineCap = "round";
    canvaCtx.lineJoin = "round";
    const touchstart = (e) => {
      let startX = e.changedTouches[0].x;
      let startY = e.changedTouches[0].y;
      let startPoint = { X: startX, Y: startY };
      points.push(startPoint);
      canvaCtx.beginPath();
    };
    const touchmove = (e) => {
      let moveX = e.changedTouches[0].x;
      let moveY = e.changedTouches[0].y;
      let movePoint = { X: moveX, Y: moveY };
      points.push(movePoint);
      let len = points.length;
      if (len >= 2) {
        draw();
      }
    };
    const draw = () => {
      let point1 = points[0];
      let point2 = points[1];
      points.shift();
      canvaCtx.moveTo(point1.X, point1.Y);
      canvaCtx.lineTo(point2.X, point2.Y);
      canvaCtx.stroke();
      canvaCtx.draw(true);
    };
    const touchend = (e) => {
      points = [];
    };
    const clear = () => {
      return common_vendor.index.getSystemInfo().then((res) => {
        canvaCtx.clearRect(0, 0, res.windowWidth, res.windowHeight);
        canvaCtx.draw(true);
        return res;
      }).catch((err) => {
      });
    };
    const confirm = () => {
      common_vendor.index.canvasToTempFilePath({ canvasId: "mycanvas" }, _this, _this.parent).then((res) => {
        common_vendor.index.__f__("log", "at components/canvas-autograph/canvas-autograph.vue:109", res.tempFilePath);
        emits("complete", res.tempFilePath);
        cancel();
      });
    };
    const cancel = () => {
      clear().then((res) => emits("update:modelValue", false));
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.o(touchstart),
        b: common_vendor.o(touchmove),
        c: common_vendor.o(touchend),
        d: common_vendor.o(clear),
        e: common_vendor.o(confirm),
        f: common_vendor.o(cancel),
        g: common_vendor.o(() => {
        }),
        h: common_vendor.o(() => {
        }),
        i: __props.modelValue
      };
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a41641d7"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/components/canvas-autograph/canvas-autograph.js.map
