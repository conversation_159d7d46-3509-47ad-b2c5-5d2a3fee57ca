<template>
  <view class="user-agreement-page">
    <!-- 协议内容 -->
    <scroll-view class="content" scroll-y="true">
      <view class="agreement-content">
        <view class="header-section">
          <text class="main-title">不良资产管理系统用户服务协议</text>
          <text class="update-info">最后更新：2024年7月23日</text>
        </view>

        <view class="section">
          <text class="section-title">一、协议的范围</text>
          <text class="section-text">
            本协议是您与不良资产管理系统之间关于您使用本系统服务所订立的协议。
            本协议描述您与本系统之间关于"不良资产管理系统"软件服务使用方面的权利义务。
            "用户"是指使用本系统的个人。本协议可由本系统随时更新，更新后的协议条款一旦公布即代替原来的协议条款，恕不再另行通知，用户可在本系统中查阅最新版协议条款。
          </text>
        </view>

        <view class="section">
          <text class="section-title">二、账号注册</text>
          <text class="section-text">
            用户在使用本服务前需要注册一个账号。账号应当使用手机号码绑定注册，请用户使用尚未与"不良资产管理系统"账号绑定的手机号码，以及未被本系统根据本协议封禁的手机号码注册账号。
            本系统可以根据用户需求或产品需要对账号注册和绑定的方式进行变更，而无须事先通知用户。
          </text>
        </view>

        <view class="section">
          <text class="section-title">三、用户个人隐私信息保护</text>
          <text class="section-text">
            用户在注册账号或使用本服务的过程中，可能需要填写或提交一些必要的信息，如法律法规、规章规范性文件（以下称"法律法规"）规定的需要填写的身份信息。
            如用户提交的信息不完整或不符合法律法规的规定，则用户可能无法使用本服务或在使用本服务的过程中受到限制。
            本系统将运用各种安全技术和程序建立完善的管理制度来保护用户的个人信息，以免遭受未经授权的访问、使用或披露。
          </text>
        </view>

        <view class="section">
          <text class="section-title">四、用户行为规范</text>
          <text class="section-text">
            用户在使用本服务时，必须遵守中华人民共和国相关法律法规，不得利用本服务从事以下活动：
            1. 发布或传送含有反对宪法确定的基本原则、危害国家安全、泄露国家秘密、颠覆国家政权、破坏国家统一的内容；
            2. 发布或传送含有侮辱、诽谤、恐吓、骚扰等内容；
            3. 发布或传送含有虚假、有害、胁迫、侵害他人隐私、骚扰、侵害、中伤、粗俗、猥亵、或其它道德上令人反感的内容；
            4. 进行其他违法或违反社会公德的行为。
          </text>
        </view>

        <view class="section">
          <text class="section-title">五、服务的变更、中断</text>
          <text class="section-text">
            本系统可能会对服务内容进行变更，也可能会中断、终止服务。
            用户理解并同意，本系统有权自主决定经营策略。在本系统发生合并、分立、收购、资产转让时，本系统可向第三方转让本服务下相关资产；本系统也可在自身经营策略调整的情况下，终止对本服务的经营。
            在前述情形发生时，本系统将尽力确保用户数据的安全。
          </text>
        </view>

        <view class="section">
          <text class="section-title">六、知识产权声明</text>
          <text class="section-text">
            本系统在本服务中提供的内容（包括但不限于软件、技术、程序、网页、文字、图片、图像、音频、视频、图表、版面设计、电子文档等）的知识产权属于本系统所有。
            用户在使用本服务时不得侵犯本系统或第三方的知识产权。
          </text>
        </view>

        <view class="section">
          <text class="section-title">七、免责声明</text>
          <text class="section-text">
            用户理解并同意，本服务可能会受到各种因素的影响或干扰，本系统不保证（包括但不限于）：
            1. 本服务完全适合用户的使用要求；
            2. 本服务不受干扰、及时提供、安全可靠或不出现错误；
            3. 用户通过本服务取得的任何产品、服务或其他材料符合用户的期望。
          </text>
        </view>

        <view class="section">
          <text class="section-title">八、协议的效力</text>
          <text class="section-text">
            本协议自用户点击同意并注册成功之日起生效，至用户注销账号或本系统终止向用户提供服务之日终止。
            本协议终止后，用户无权要求本系统继续向其提供任何服务或履行任何其他义务，包括但不限于要求本系统为用户保留或向用户披露其原账号中的任何信息，或向用户或第三方转发任何其未曾阅读或发送的信息。
          </text>
        </view>

        <view class="section">
          <text class="section-title">九、其他</text>
          <text class="section-text">
            本协议的签订地为中华人民共和国。
            本协议的效力、解释、变更、执行与争议解决均适用中华人民共和国法律，如无相关法律规定的，则应参照通用国际商业惯例和（或）行业惯例。
            如就本协议内容或其执行发生任何争议，应尽量友好协商解决；协商不成时，则争议各方同意提交有管辖权的人民法院裁决。
          </text>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue';

// 页面加载
onMounted(() => {
  // 设置页面标题
  uni.setNavigationBarTitle({
    title: '服务协议'
  });
});
</script>

<style lang="scss" scoped>
.user-agreement-page {
  min-height: 100vh;
  background-color: #ffffff;
}

.content {
  height: 100vh;
  padding: 0 30rpx;
}

.agreement-content {
  padding: 40rpx 0;
}

.header-section {
  text-align: center;
  margin-bottom: 60rpx;
  padding-bottom: 40rpx;
  border-bottom: 2rpx solid #e5e5e5;
}

.main-title {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

.update-info {
  display: block;
  font-size: 24rpx;
  color: #999999;
}

.section {
  margin-bottom: 50rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  line-height: 1.5;
}

.section-text {
  display: block;
  font-size: 28rpx;
  color: #666666;
  line-height: 1.8;
  text-align: justify;
  text-indent: 2em;
}
</style>