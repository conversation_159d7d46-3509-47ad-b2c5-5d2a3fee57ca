"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const utils_api = require("../../utils/api.js");
const utils_wechatAuth = require("../../utils/wechat-auth.js");
require("../../config/env.js");
const _sfc_main = {
  __name: "auth",
  setup(__props) {
    const formData = common_vendor.ref({
      name: "",
      card: ""
    });
    const nameError = common_vendor.ref("");
    const cardError = common_vendor.ref("");
    const isAgreed = common_vendor.ref(false);
    const isLoading = common_vendor.ref(false);
    const nameDisabled = common_vendor.ref(false);
    const cardDisabled = common_vendor.ref(false);
    const canSubmit = common_vendor.computed(() => {
      return isAgreed.value && formData.value.name.trim() && formData.value.card.trim() && !nameError.value && !cardError.value && !isLoading.value;
    });
    const onNameInput = () => {
      if (nameError.value) {
        nameError.value = "";
      }
    };
    const validateName = () => {
      const name = formData.value.name.trim();
      if (!name) {
        nameError.value = "请输入姓名";
        return false;
      }
      if (name.length < 2) {
        nameError.value = "姓名至少2个字符";
        return false;
      }
      if (name.length > 10) {
        nameError.value = "姓名不能超过10个字符";
        return false;
      }
      if (!/^[\u4e00-\u9fa5a-zA-Z]+$/.test(name)) {
        nameError.value = "姓名只能包含中文或英文";
        return false;
      }
      nameError.value = "";
      return true;
    };
    const onCardInput = () => {
      if (cardError.value) {
        cardError.value = "";
      }
      formData.value.card = formData.value.card.toUpperCase();
    };
    const validateCard = () => {
      const card = formData.value.card.trim();
      if (!card) {
        cardError.value = "请输入身份证号码";
        return false;
      }
      const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
      if (!idCardRegex.test(card)) {
        cardError.value = "身份证号码格式不正确";
        return false;
      }
      if (!validateIdCardChecksum(card)) {
        cardError.value = "身份证号码校验失败";
        return false;
      }
      cardError.value = "";
      return true;
    };
    const validateIdCardChecksum = (idCard) => {
      const weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const checkCodes = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
      let sum = 0;
      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard[i]) * weights[i];
      }
      const checkCode = checkCodes[sum % 11];
      return checkCode === idCard[17];
    };
    const toggleAgreement = () => {
      isAgreed.value = !isAgreed.value;
    };
    const navigateTo = (page) => {
      common_vendor.index.navigateTo({
        url: `/pages/${page}/${page}`
      });
    };
    const getUserOpenid = () => {
      try {
        if (utils_wechatAuth.wechatAuth.openid) {
          return utils_wechatAuth.wechatAuth.openid;
        }
        const openid = common_vendor.index.getStorageSync("openid");
        if (openid) {
          return openid;
        }
        throw new Error("未获取到用户openid");
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/auth/auth.vue:230", "获取openid失败:", error);
        throw error;
      }
    };
    const startFaceRecognition = async () => {
      if (!validateName() || !validateCard()) {
        common_vendor.index.showToast({
          title: "请填写正确的信息",
          icon: "none",
          duration: 2e3
        });
        return;
      }
      isLoading.value = true;
      try {
        const openid = getUserOpenid();
        const response = await utils_api.api.wechat.faceIdAuth({
          openid,
          name: formData.value.name.trim(),
          id_card: formData.value.card.trim()
        });
        common_vendor.index.__f__("log", "at pages/auth/auth.vue:267", "人脸核身接口响应:", response);
        if (response.data) {
          common_vendor.index.navigateTo({
            url: `/pages/webview/webview?url=${encodeURIComponent(response.data)}`
          });
        } else {
          throw new Error(response.msg || "获取验证地址失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/auth/auth.vue:286", "人脸识别验证失败:", error);
        let errorMessage = "验证失败，请重试";
        common_vendor.index.showToast({
          title: errorMessage,
          icon: "none",
          duration: 3e3
        });
      } finally {
        isLoading.value = false;
      }
    };
    const $route = common_vendor.ref({
      query: {}
    });
    const parseUrlParams = () => {
      try {
        common_vendor.index.__f__("log", "at pages/auth/auth.vue:333", "开始解析URL查询参数...");
        common_vendor.index.__f__("log", "at pages/auth/auth.vue:334", "当前$route.query:", $route.value.query);
        if ($route.value.query.name) {
          try {
            const decodedName = decodeURIComponent($route.value.query.name);
            common_vendor.index.__f__("log", "at pages/auth/auth.vue:341", "解析到name参数:", decodedName);
            formData.value.name = decodedName;
            nameDisabled.value = true;
            validateName();
          } catch (decodeError) {
            common_vendor.index.__f__("error", "at pages/auth/auth.vue:351", "name参数URL解码失败:", decodeError);
            formData.value.name = $route.value.query.name;
            nameDisabled.value = true;
            validateName();
          }
        }
        if ($route.value.query.idCard) {
          common_vendor.index.__f__("log", "at pages/auth/auth.vue:361", "解析到idCard参数:", $route.value.query.idCard);
          formData.value.card = $route.value.query.idCard;
          cardDisabled.value = true;
          validateCard();
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/auth/auth.vue:373", "解析URL参数失败:", error);
      }
    };
    common_vendor.onLoad((options) => {
      common_vendor.index.__f__("log", "at pages/auth/auth.vue:379", "页面加载，接收参数:", options);
      $route.value.query = { ...options };
      common_vendor.index.__f__("log", "at pages/auth/auth.vue:383", "构建$route.query:", $route.value.query);
      parseUrlParams();
    });
    common_vendor.onMounted(async () => {
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$1,
        b: nameError.value ? 1 : "",
        c: nameDisabled.value ? 1 : "",
        d: nameDisabled.value,
        e: common_vendor.o([($event) => formData.value.name = $event.detail.value, onNameInput]),
        f: common_vendor.o(validateName),
        g: formData.value.name,
        h: nameError.value
      }, nameError.value ? {
        i: common_vendor.t(nameError.value)
      } : {}, {
        j: cardError.value ? 1 : "",
        k: cardDisabled.value ? 1 : "",
        l: cardDisabled.value,
        m: common_vendor.o([($event) => formData.value.card = $event.detail.value, onCardInput]),
        n: common_vendor.o(validateCard),
        o: formData.value.card,
        p: cardError.value
      }, cardError.value ? {
        q: common_vendor.t(cardError.value)
      } : {}, {
        r: isAgreed.value,
        s: common_vendor.o(toggleAgreement),
        t: common_vendor.o(($event) => navigateTo("user_agreement")),
        v: common_vendor.o(($event) => navigateTo("privacy_policy")),
        w: common_vendor.t(isLoading.value ? "验证中..." : "开始人脸识别验证"),
        x: !canSubmit.value,
        y: !canSubmit.value ? 1 : "",
        z: common_vendor.o(startFaceRecognition)
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-98e148d1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/auth/auth.js.map
