<template>
  <view class="contact-fab" :class="{ 'contact-fab-open': isOpen }">
    <!-- 主按钮 -->
    <view class="contact-fab-button" @click="toggleFab">
      <text v-if="isOpen" class="fa fas fa-xmark"></text>
      <text v-else class="fa fas fa-headset"></text>
    </view>
    
    <!-- 展开的菜单项 - 白色背景矩形 -->
    <view class="contact-fab-menu" v-if="isOpen">
      <!-- 电话咨询 -->
      <view class="contact-fab-item" @click="handlePhone">
        <view class="contact-fab-item-icon">
          <text class="fa fas fa-phone"></text>
        </view>
        <text class="contact-fab-item-label">电话咨询</text>
      </view>
      
      <!-- 分隔线 -->
      <view class="contact-fab-divider"></view>
      
      <!-- 微信咨询 -->
      <view class="contact-fab-item" @click="handleWechat">
        <view class="contact-fab-item-icon">
          <text class="fab fa-weixin"></text>
        </view>
        <text class="contact-fab-item-label">微信咨询</text>
      </view>
    </view>
    
    <!-- 遮罩层 -->
    <view class="contact-fab-mask" v-if="isOpen" @click="closeFab"></view>
  </view>
</template>

<script setup>
import { ref } from 'vue';

const isOpen = ref(false);

const toggleFab = () => {
  isOpen.value = !isOpen.value;
};

const closeFab = () => {
  isOpen.value = false;
};

const handlePhone = () => {
  // 处理电话咨询
  uni.makePhoneCall({
    phoneNumber: '10086', // 替换为实际电话号码
    success: () => {
      console.log('拨打电话成功');
      closeFab();
    },
    fail: (err) => {
      console.error('拨打电话失败', err);
    }
  });
};

const handleWechat = () => {
  // 处理微信咨询，可以复制微信号或打开小程序客服
  uni.setClipboardData({
    data: 'YourWeChatID', // 替换为实际微信号
    success: () => {
      uni.showToast({
        title: '微信号已复制',
        icon: 'success'
      });
      closeFab();
    }
  });
};
</script>

<style lang="scss" scoped>
.contact-fab {
  position: fixed;
  right: 30rpx;
  bottom: 120rpx;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.contact-fab-button {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background-color: #2979ff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(41, 121, 255, 0.3);
  z-index: 1001;
  transition: transform 0.3s;
}

.contact-fab-button .fa {
  font-size: 40rpx;
  color: #fff;
}

.contact-fab-menu {
  position: absolute;
  bottom: 120rpx;
  right: 0;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  animation: fadeIn 0.3s ease-out;
  width: 360rpx;
}

.contact-fab-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 20rpx 0;
}

.contact-fab-item-icon {
  margin-right: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-fab-item-icon .fa,
.contact-fab-item-icon .fab {
  font-size: 40rpx;
}

.contact-fab-item-label {
  font-size: 28rpx;
  color: #333;
}

.fa-phone {
  color: #2979ff;
}

.fa-weixin {
  color: #07c160;
}

.contact-fab-divider {
  width: 90%;
  height: 2rpx;
  background-color: #eee;
  margin: 0 auto;
}

.contact-fab-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 998;
}

/* 动画效果 */
.contact-fab-open .contact-fab-button {
  transform: rotate(135deg);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 