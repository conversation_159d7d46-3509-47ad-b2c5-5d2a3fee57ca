{"version": 3, "file": "debt_confirm.js", "sources": ["pages/debt_confirm.vue", "../../../software/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZGVidF9jb25maXJtL2RlYnRfY29uZmlybS52dWU"], "sourcesContent": ["<template>\r\n\t<view class=\"debt-confirm-container\">\r\n\t\t<!-- 这里添加债权确认页面内容 -->\r\n\t</view>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, onMounted } from 'vue';\r\n\r\n// 响应式数据可在此定义\r\n// const debtData = ref(null);\r\n\r\n// 生命周期钩子\r\nonMounted(() => {\r\n\tconsole.log('债权确认页面已加载');\r\n\t// 初始化操作可放在这里\r\n});\r\n\r\n// 方法定义\r\n// function confirmDebt() {\r\n//     // 方法实现\r\n// }\r\n</script>\r\n\r\n<style scoped>\r\n.debt-confirm-container {\r\n\tmin-height: 100vh;\r\n\tbackground-color: #f5f5f5;\r\n\tpadding: 20rpx;\r\n}\r\n</style>", "import MiniProgramPage from 'C:/work/不良资产系统/non-performing-assets/pages/debt_confirm/debt_confirm.vue'\nwx.createPage(MiniProgramPage)"], "names": ["onMounted", "uni"], "mappings": ";;;;;AAaAA,kBAAAA,UAAU,MAAM;AACfC,oBAAAA,MAAY,MAAA,OAAA,6CAAA,WAAW;AAAA,IAExB,CAAC;;;;;;;ACfD,GAAG,WAAW,eAAe;"}