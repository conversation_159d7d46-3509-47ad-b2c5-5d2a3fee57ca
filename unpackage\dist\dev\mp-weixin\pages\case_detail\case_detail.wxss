/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.case-detail-container.data-v-d60749c0 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.detail-card.data-v-d60749c0 {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.case-header.data-v-d60749c0 {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.case-title.data-v-d60749c0 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}
.case-tags.data-v-d60749c0 {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
}
.case-tag.data-v-d60749c0 {
  padding: 8rpx 16rpx;
  background-color: #e3f2fd;
  color: #1976d2;
  font-size: 24rpx;
  border-radius: 20rpx;
}
.case-info.data-v-d60749c0 {
  padding: 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}
.info-row.data-v-d60749c0 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding: 15rpx 0;
}
.info-row.data-v-d60749c0:last-child {
  margin-bottom: 0;
}
.info-label.data-v-d60749c0 {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}
.info-value.data-v-d60749c0 {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}
.debt-amount.data-v-d60749c0 {
  color: #f44336;
}
.resolved-amount.data-v-d60749c0 {
  color: #4caf50;
}
.reduction-rate.data-v-d60749c0 {
  color: #ff9800;
}
.case-content.data-v-d60749c0 {
  padding: 30rpx;
}
.content-section.data-v-d60749c0 {
  margin-bottom: 40rpx;
}
.content-section.data-v-d60749c0:last-child {
  margin-bottom: 0;
}
.section-title.data-v-d60749c0 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  position: relative;
  padding-left: 20rpx;
}
.section-title.data-v-d60749c0::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 8rpx;
  height: 30rpx;
  background-color: #2979ff;
  border-radius: 4rpx;
}
.section-content.data-v-d60749c0 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  text-align: justify;
}