// 错误处理增强工具
import { isDebug } from '@/config/env.js';

/**
 * 错误类型枚举
 */
export const ERROR_TYPES = {
  NETWORK: 'network',           // 网络错误
  TIMEOUT: 'timeout',           // 超时错误
  AUTHORIZATION: 'authorization', // 授权错误
  VALIDATION: 'validation',     // 验证错误
  SERVER: 'server',             // 服务器错误
  USER_CANCELLED: 'user_cancelled', // 用户取消
  SYSTEM: 'system',             // 系统错误
  UNKNOWN: 'unknown'            // 未知错误
};

/**
 * 错误级别枚举
 */
export const ERROR_LEVELS = {
  INFO: 'info',       // 信息级别
  WARNING: 'warning', // 警告级别
  ERROR: 'error',     // 错误级别
  CRITICAL: 'critical' // 严重错误级别
};

/**
 * 错误消息映射
 */
const ERROR_MESSAGES = {
  [ERROR_TYPES.NETWORK]: {
    title: '网络连接失败',
    message: '请检查网络连接后重试',
    icon: 'none'
  },
  [ERROR_TYPES.TIMEOUT]: {
    title: '请求超时',
    message: '网络响应较慢，请稍后重试',
    icon: 'none'
  },
  [ERROR_TYPES.AUTHORIZATION]: {
    title: '授权失败',
    message: '请重新授权后继续使用',
    icon: 'none'
  },
  [ERROR_TYPES.VALIDATION]: {
    title: '输入有误',
    message: '请检查输入信息是否正确',
    icon: 'none'
  },
  [ERROR_TYPES.SERVER]: {
    title: '服务器错误',
    message: '服务器暂时无法响应，请稍后重试',
    icon: 'none'
  },
  [ERROR_TYPES.USER_CANCELLED]: {
    title: '操作已取消',
    message: '您已取消当前操作',
    icon: 'none'
  },
  [ERROR_TYPES.SYSTEM]: {
    title: '系统错误',
    message: '系统出现异常，请重启应用后重试',
    icon: 'none'
  },
  [ERROR_TYPES.UNKNOWN]: {
    title: '未知错误',
    message: '发生了未知错误，请重试',
    icon: 'none'
  }
};

/**
 * 错误分析器 - 根据错误信息判断错误类型
 * @param {Error|string} error 错误对象或错误消息
 * @returns {Object} 错误分析结果
 */
export function analyzeError(error) {
  let message = '';
  let stack = '';
  
  if (error instanceof Error) {
    message = error.message || '';
    stack = error.stack || '';
  } else if (typeof error === 'string') {
    message = error;
  } else if (error && error.errMsg) {
    message = error.errMsg;
  } else {
    message = String(error || '');
  }
  
  const lowerMessage = message.toLowerCase();
  
  // 网络相关错误
  if (lowerMessage.includes('network') || 
      lowerMessage.includes('网络') ||
      lowerMessage.includes('connection') ||
      lowerMessage.includes('连接') ||
      lowerMessage.includes('request:fail')) {
    return {
      type: ERROR_TYPES.NETWORK,
      level: ERROR_LEVELS.WARNING,
      originalMessage: message,
      stack
    };
  }
  
  // 超时错误
  if (lowerMessage.includes('timeout') || 
      lowerMessage.includes('超时') ||
      lowerMessage.includes('time out')) {
    return {
      type: ERROR_TYPES.TIMEOUT,
      level: ERROR_LEVELS.WARNING,
      originalMessage: message,
      stack
    };
  }
  
  // 授权相关错误 - 更精确的判断
  if ((lowerMessage.includes('auth') && !lowerMessage.includes('fail')) ||
      (lowerMessage.includes('授权') && lowerMessage.includes('失败')) ||
      lowerMessage.includes('permission denied') ||
      lowerMessage.includes('权限不足') ||
      (lowerMessage.includes('token') && lowerMessage.includes('invalid'))) {
    return {
      type: ERROR_TYPES.AUTHORIZATION,
      level: ERROR_LEVELS.ERROR,
      originalMessage: message,
      stack
    };
  }
  
  // 用户取消
  if (lowerMessage.includes('cancel') ||
      lowerMessage.includes('取消') ||
      lowerMessage.includes('abort') ||
      lowerMessage.includes('中止')) {
    return {
      type: ERROR_TYPES.USER_CANCELLED,
      level: ERROR_LEVELS.INFO,
      originalMessage: message,
      stack
    };
  }
  
  // 验证错误
  if (lowerMessage.includes('valid') ||
      lowerMessage.includes('验证') ||
      lowerMessage.includes('format') ||
      lowerMessage.includes('格式') ||
      lowerMessage.includes('required') ||
      lowerMessage.includes('必填')) {
    return {
      type: ERROR_TYPES.VALIDATION,
      level: ERROR_LEVELS.WARNING,
      originalMessage: message,
      stack
    };
  }
  
  // 服务器错误
  if (lowerMessage.includes('server') ||
      lowerMessage.includes('服务器') ||
      lowerMessage.includes('500') ||
      lowerMessage.includes('502') ||
      lowerMessage.includes('503') ||
      lowerMessage.includes('504')) {
    return {
      type: ERROR_TYPES.SERVER,
      level: ERROR_LEVELS.ERROR,
      originalMessage: message,
      stack
    };
  }
  
  // 系统错误
  if (lowerMessage.includes('system') ||
      lowerMessage.includes('系统') ||
      lowerMessage.includes('crash') ||
      lowerMessage.includes('崩溃')) {
    return {
      type: ERROR_TYPES.SYSTEM,
      level: ERROR_LEVELS.CRITICAL,
      originalMessage: message,
      stack
    };
  }
  
  // 默认为未知错误
  return {
    type: ERROR_TYPES.UNKNOWN,
    level: ERROR_LEVELS.ERROR,
    originalMessage: message,
    stack
  };
}

/**
 * 获取用户友好的错误消息
 * @param {Object} errorAnalysis 错误分析结果
 * @param {Object} customMessages 自定义错误消息
 * @returns {Object} 错误消息对象
 */
export function getFriendlyErrorMessage(errorAnalysis, customMessages = {}) {
  const defaultMessage = ERROR_MESSAGES[errorAnalysis.type] || ERROR_MESSAGES[ERROR_TYPES.UNKNOWN];
  const customMessage = customMessages[errorAnalysis.type];
  
  return {
    ...defaultMessage,
    ...customMessage,
    originalMessage: errorAnalysis.originalMessage,
    type: errorAnalysis.type,
    level: errorAnalysis.level
  };
}

/**
 * 显示错误提示
 * @param {Error|string|Object} error 错误信息
 * @param {Object} options 显示选项
 */
export function showError(error, options = {}) {
  const defaultOptions = {
    duration: 2000,
    showType: true,  // 是否在调试模式下显示错误类型
    customMessages: {},
    ...options
  };
  
  const errorAnalysis = analyzeError(error);
  const friendlyMessage = getFriendlyErrorMessage(errorAnalysis, defaultOptions.customMessages);
  
  // 在调试模式下记录详细错误信息
  if (isDebug()) {
    console.error('错误详情:', {
      type: errorAnalysis.type,
      level: errorAnalysis.level,
      original: errorAnalysis.originalMessage,
      friendly: friendlyMessage.message,
      stack: errorAnalysis.stack
    });
  }
  
  // 构建显示消息
  let displayMessage = friendlyMessage.message;
  if (isDebug() && defaultOptions.showType) {
    displayMessage = `[${errorAnalysis.type}] ${displayMessage}`;
  }
  
  // 根据错误级别决定是否显示
  if (errorAnalysis.level === ERROR_LEVELS.INFO && errorAnalysis.type === ERROR_TYPES.USER_CANCELLED) {
    // 用户取消操作通常不需要显示错误提示
    return;
  }
  
  uni.showToast({
    title: displayMessage,
    icon: friendlyMessage.icon || 'none',
    duration: defaultOptions.duration,
    mask: false
  });
}

/**
 * 错误重试处理器
 * @param {Function} operation 要重试的操作
 * @param {Object} options 重试选项
 * @returns {Promise} 操作结果
 */
export function withRetry(operation, options = {}) {
  const defaultOptions = {
    maxRetries: 3,
    retryDelay: 1000,
    retryDelayMultiplier: 1.5,
    retryableErrors: [ERROR_TYPES.NETWORK, ERROR_TYPES.TIMEOUT, ERROR_TYPES.SERVER],
    onRetry: null,
    ...options
  };
  
  return new Promise(async (resolve, reject) => {
    let lastError = null;
    
    for (let attempt = 0; attempt <= defaultOptions.maxRetries; attempt++) {
      try {
        const result = await operation();
        resolve(result);
        return;
      } catch (error) {
        lastError = error;
        const errorAnalysis = analyzeError(error);
        
        // 检查是否是可重试的错误
        if (attempt < defaultOptions.maxRetries && 
            defaultOptions.retryableErrors.includes(errorAnalysis.type)) {
          
          // 计算延迟时间
          const delay = defaultOptions.retryDelay * Math.pow(defaultOptions.retryDelayMultiplier, attempt);
          
          if (isDebug()) {
            console.log(`操作失败，${delay}ms后进行第${attempt + 1}次重试:`, errorAnalysis.originalMessage);
          }
          
          // 调用重试回调
          if (typeof defaultOptions.onRetry === 'function') {
            defaultOptions.onRetry(attempt + 1, errorAnalysis, delay);
          }
          
          // 等待后重试
          await new Promise(resolve => setTimeout(resolve, delay));
        } else {
          // 不可重试或达到最大重试次数
          break;
        }
      }
    }
    
    // 所有重试都失败了
    reject(lastError);
  });
}

/**
 * 错误边界处理器 - 用于包装可能出错的异步操作
 * @param {Function} operation 异步操作
 * @param {Object} options 选项
 * @returns {Promise} 处理结果
 */
export function withErrorBoundary(operation, options = {}) {
  const defaultOptions = {
    showError: true,
    fallbackValue: null,
    onError: null,
    ...options
  };
  
  return new Promise(async (resolve) => {
    try {
      const result = await operation();
      resolve({ success: true, data: result, error: null });
    } catch (error) {
      const errorAnalysis = analyzeError(error);
      
      if (isDebug()) {
        console.error('错误边界捕获错误:', errorAnalysis);
      }
      
      // 显示错误提示
      if (defaultOptions.showError) {
        showError(error, options);
      }
      
      // 调用错误回调
      if (typeof defaultOptions.onError === 'function') {
        defaultOptions.onError(errorAnalysis);
      }
      
      resolve({ 
        success: false, 
        data: defaultOptions.fallbackValue, 
        error: errorAnalysis 
      });
    }
  });
}
