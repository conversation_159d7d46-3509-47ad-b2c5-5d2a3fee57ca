<view class="uni-load-more" bindtap="{{q}}"><view wx:if="{{a}}" style="{{'width:' + h + ';' + ('height:' + i)}}" class="uni-load-more__img uni-load-more__img--android-MP"><view class="uni-load-more__img-icon" style="{{'border-top-color:' + b + ';' + ('border-top-width:' + c)}}"></view><view class="uni-load-more__img-icon" style="{{'border-top-color:' + d + ';' + ('border-top-width:' + e)}}"></view><view class="uni-load-more__img-icon" style="{{'border-top-color:' + f + ';' + ('border-top-width:' + g)}}"></view></view><view wx:elif="{{j}}" style="{{'width:' + l + ';' + ('height:' + m)}}" class="uni-load-more__img uni-load-more__img--ios-H5"><image src="{{k}}" mode="widthFix"></image></view><text wx:if="{{n}}" class="uni-load-more__text" style="{{'color:' + p}}">{{o}}</text></view>