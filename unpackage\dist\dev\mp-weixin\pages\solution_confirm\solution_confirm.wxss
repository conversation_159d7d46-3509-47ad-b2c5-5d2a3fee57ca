/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.data-v-5489ef86:root {
  --primary-color: #3b7eeb;
}
.solution-confirm-container.data-v-5489ef86 {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 卡片样式 */
.work-order-card.data-v-5489ef86 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}
.work-order-header.data-v-5489ef86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.work-order-title.data-v-5489ef86 {
  font-size: 29rpx;
  color: #333;
  font-weight: bold;
}
.work-order-status.data-v-5489ef86 {
  padding: 6rpx 20rpx;
  border-radius: 30rpx;
}
.status-label.data-v-5489ef86 {
  font-size: 26rpx;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  color: #fff;
}
.status-processing.data-v-5489ef86 {
  background-color: #1890ff;
  color: #fff;
}
.work-order-date.data-v-5489ef86 {
  font-size: 28rpx;
  color: #666;
}

/* 工作方案详情样式 */
.work-plan-section.data-v-5489ef86 {
  margin: 30rpx 0;
}
.section-title.data-v-5489ef86 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding: 0 30rpx;
}
.plan-detail-card.data-v-5489ef86 {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 0 30rpx 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.plan-header.data-v-5489ef86 {
  padding: 25rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx 16rpx 0 0;
}
.plan-name.data-v-5489ef86 {
  font-size: 30rpx;
  font-weight: 600;
  color: #fff;
}
.plan-content.data-v-5489ef86 {
  padding: 20rpx 30rpx 30rpx;
}
.config-item.data-v-5489ef86 {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}
.config-item.data-v-5489ef86:last-child {
  border-bottom: none;
}
.config-title.data-v-5489ef86 {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 10rpx;
}
.config-value.data-v-5489ef86 {
  font-size: 32rpx;
  color: #007aff;
  font-weight: 700;
  margin-bottom: 8rpx;
}
.config-expression.data-v-5489ef86 {
  display: flex;
  align-items: center;
  margin-top: 8rpx;
}
.expression-label.data-v-5489ef86 {
  font-size: 24rpx;
  color: #999;
  margin-right: 10rpx;
}
.expression-text.data-v-5489ef86 {
  font-size: 24rpx;
  color: #666;
  font-family: "Courier New", monospace;
  background-color: #f8f9fa;
  padding: 4rpx 8rpx;
  border-radius: 6rpx;
}

/* 进度条样式 */
/* .progress-bar {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
} */
.progress-steps.data-v-5489ef86 {
  display: flex;
  justify-content: space-between;
  position: relative;
}
.progress-step.data-v-5489ef86 {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}
.step-circle.data-v-5489ef86 {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #e0e0e0;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 2;
}
.step-line.data-v-5489ef86 {
  position: absolute;
  top: 30rpx;
  left: 50%;
  right: -50%;
  height: 4rpx;
  background-color: #e0e0e0;
  z-index: 1;
}
.progress-step:last-child .step-line.data-v-5489ef86 {
  display: none;
}
.step-label.data-v-5489ef86 {
  font-size: 24rpx;
  color: #999;
  text-align: center;
}
.progress-step.active .step-circle.data-v-5489ef86 {
  background-color: #2979ff;
}
.progress-step.active .step-label.data-v-5489ef86 {
  color: #2979ff;
  font-weight: bold;
}
.progress-step.completed .step-circle.data-v-5489ef86 {
  background-color: #2979ff;
}
.step-line.completed.data-v-5489ef86 {
  background-color: #2979ff;
}
.progress-step.completed .step-label.data-v-5489ef86 {
  color: #2979ff;
}

/* 方案容器 */
.solutions-container.data-v-5489ef86 {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

/* 方案卡片样式 */
.solution-card.data-v-5489ef86 {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  border: 2rpx solid #eee;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 30rpx;
}
.solution-card.selected.data-v-5489ef86 {
  border-color: #2979ff;
  box-shadow: 0 4rpx 20rpx rgba(41, 121, 255, 0.1);
}
.solution-card.data-v-5489ef86:last-child {
  margin-bottom: 0;
}
.solution-header.data-v-5489ef86 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}
.solution-title-wrap.data-v-5489ef86 {
  display: flex;
  align-items: center;
}
.solution-title.data-v-5489ef86 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.solution-tag.data-v-5489ef86 {
  font-size: 24rpx;
  color: #2979ff;
  margin-left: 10rpx;
}
.solution-select.data-v-5489ef86 {
  display: flex;
  align-items: center;
}
.radio-button.data-v-5489ef86 {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #dddddd;
  border-image: initial;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}
.radio-button.selected.data-v-5489ef86 {
  border-color: #3b7eeb;
  background-color: var(--primary-color);
}
.radio-inner.data-v-5489ef86 {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
}
.solution-content.data-v-5489ef86 {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
  line-height: 1.5;
  gap: 16rpx;
  display: flex;
  flex-direction: column;
}
.solution-details.data-v-5489ef86 {
  margin-bottom: 20rpx;
}
.solution-item.data-v-5489ef86 {
  display: flex;
  flex-direction: column;
  padding: 10rpx 0;
  gap: 8rpx;
}
.solution-label.data-v-5489ef86 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.solution-value.data-v-5489ef86 {
  font-size: 32rpx;
  color: #333;
}
.solution-footer.data-v-5489ef86 {
  display: flex;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 1px solid #eee;
}

/* 配置项详情样式 */
/* .config-detail {
	margin-bottom: 20rpx;
}
 */
.config-detail.data-v-5489ef86:last-child {
  margin-bottom: 0;
}
.config-formula.data-v-5489ef86 {
  margin-top: 8rpx;
  padding: 8rpx 12rpx;
  background-color: #f8f9fa;
  border-radius: 6rpx;
  border-left: 3rpx solid #007aff;
}
.formula-label.data-v-5489ef86 {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}
.formula-text.data-v-5489ef86 {
  font-size: 24rpx;
  color: #333;
  font-family: "Courier New", monospace;
}

/* 空状态样式 */
.empty-state.data-v-5489ef86 {
  text-align: center;
  padding: 100rpx 30rpx;
}
.empty-text.data-v-5489ef86 {
  font-size: 28rpx;
  color: #999;
}
.discount-amount.data-v-5489ef86 {
  font-size: 24rpx;
  color: #999;
}
.view-detail.data-v-5489ef86 {
  font-size: 26rpx;
  color: #2979ff;
  display: flex;
  align-items: center;
}
.view-detail.data-v-5489ef86::before {
  content: "i";
  display: inline-block;
  width: 30rpx;
  height: 30rpx;
  line-height: 30rpx;
  text-align: center;
  font-size: 22rpx;
  font-style: italic;
  font-weight: bold;
  border-radius: 50%;
  background-color: #2979ff;
  color: #fff;
  margin-right: 10rpx;
}

/* 联系客服区域 */
.contact-section.data-v-5489ef86 {
  display: flex;
  gap: 30rpx;
}
.contact-button.data-v-5489ef86 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  padding: 20rpx;
  border-radius: 12rpx;
  background-color: #fff;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  font-size: 28rpx;
}
.contact-button.phone.data-v-5489ef86 {
  color: #2979ff;
}
.contact-button.wechat.data-v-5489ef86 {
  color: #07c160;
}
.contact-button .fa.data-v-5489ef86,
.contact-button .fab.data-v-5489ef86 {
  font-size: 32rpx;
}

/* 底部按钮 */
/* .action-buttons {
	padding: 20rpx 0;
} */
.confirm-button.data-v-5489ef86 {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  text-align: center;
  border-radius: 16rpx;
  font-size: 32rpx;
  background-color: #2979ff;
  color: #fff;
}